import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import ArticleSEO from 'components/SEO/ArticleSEO';
import LoadingSpinner from 'components/Common/LoadingSpinner';
import ErrorMessage from 'components/Common/ErrorMessage';
import ArticleNotFound from 'components/Articles/ArticleNotFound';
import ViewArticle from 'components/Home/Article Section/view-article';
import { formatDate } from 'utils/seo';

// Enhanced imports for modern UI
import { ArticleErrorBoundary } from 'components/ErrorBoundary/SpecializedErrorBoundaries';
import { usePerformanceMonitor } from 'hooks/usePerformanceMonitor';
import { staggerContainer, staggerItem } from 'design-system/animations';
import {
  CalendarIcon,
  UserIcon,
  EyeIcon,
  ShareIcon,
  BookmarkIcon,
  DocumentTextIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { BASE_URL } from 'services';

const ArticleViewPage = () => {
  const { articleSlug } = useParams();
  const { measureAsync, measureSync } = usePerformanceMonitor('ArticleViewPage');

  // Enhanced state management
  const [article, setArticle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [notFound, setNotFound] = useState(false);
  const [breadcrumbs, setBreadcrumbs] = useState([]);
  const [showPDFViewer, setShowPDFViewer] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [shareCount, setShareCount] = useState(0);
  const [viewCount, setViewCount] = useState(0);
  const [relatedArticles, setRelatedArticles] = useState([]);
  const [loadingRelated, setLoadingRelated] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);

  // Enhanced related articles fetching
  const fetchRelatedArticles = useCallback(async (categoryId, currentArticleId) => {
    const fetchOperation = async () => {
      try {
        setLoadingRelated(true);
        const response = await fetch(
          `${BASE_URL}/author/articles/related?category=${categoryId}&exclude=${currentArticleId}&limit=4`
        );

        if (response.ok) {
          const data = await response.json();
          setRelatedArticles(data.articles || []);
        }
      } catch (err) {
        console.error('Error fetching related articles:', err);
        setRelatedArticles([]);
      } finally {
        setLoadingRelated(false);
      }
    };

    await measureAsync('Related Articles Fetch', fetchOperation);
  }, [measureAsync]);

  // Track article view function
  const trackArticleView = useCallback(async (slug) => {
    const trackOperation = async () => {
      try {
        await fetch(`${BASE_URL}/author/articles/track-view/${slug}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });
      } catch (err) {
        console.error('Error tracking article view:', err);
      }
    };

    await measureAsync('Article View Tracking', trackOperation);
  }, [measureAsync]);

  // Enhanced article fetching with performance tracking
  const fetchArticle = useCallback(async () => {
    const fetchOperation = async () => {
      try {

        setLoading(true);
        setError(null);

        // Fetch article by slug
        const response = await fetch(`${BASE_URL}/author/articles/slug/${articleSlug}`);

        if (response.status === 404) {
          setNotFound(true);
          setLoading(false);
          return;
        }

        if (!response.ok) {
          throw new Error('Failed to fetch article');
        }

        const data = await response.json();
        const articleData = data.article.article;


        measureSync('Article Data Processing', () => {
          setArticle(articleData);
          setBreadcrumbs(data.breadcrumbs || []);
          setViewCount(articleData.viewCount || 0);
          setShareCount(articleData.shareCount || 0);
        });

        // Fetch related articles
        if (articleData.category) {
          await fetchRelatedArticles(articleData.category._id, articleData.articleId);
        }

        // Track article view
        await trackArticleView(articleSlug);

      } catch (err) {
        setError(err.message);
      } finally {

        setLoading(false);
      }
    };

    await measureAsync('Article Fetch Operation', fetchOperation);
  }, [articleSlug, measureAsync, measureSync, fetchRelatedArticles, trackArticleView]);

  // Check bookmark status on load
  const checkBookmarkStatus = useCallback(async () => {
    try {
      const userId = localStorage.getItem('userId');
      if (!userId) return;

      const response = await fetch(`${BASE_URL}/author/articles/bookmark-status/${articleSlug}`, {
        headers: {
          'x-user-id': userId,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setIsBookmarked(data.data.isBookmarked);
      }
    } catch (err) {
      console.error('Error checking bookmark status:', err);
    }
  }, [articleSlug]);

  useEffect(() => {
    if (articleSlug) {
      fetchArticle();
      checkBookmarkStatus();
    }
  }, [articleSlug, fetchArticle, checkBookmarkStatus]);

  // Enhanced social sharing with analytics
  const handleSocialShare = useCallback(async (platform = 'general') => {
    const shareOperation = async () => {
      try {
        // Track the share
        await fetch(`${BASE_URL}/author/articles/track-share/${articleSlug}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ platform }),
        });

        // Update local share count
        setShareCount(prev => prev + 1);

        // Get current page URL and article info
        const currentUrl = window.location.href;
        const articleTitle = article?.title || 'Check out this article';
        const articleDescription = article?.description || 'Interesting research article from Learning Publics';

        // Handle different sharing platforms
        switch (platform) {
          case 'facebook':
            window.open(
              `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`,
              '_blank',
              'width=600,height=400'
            );
            break;
          case 'twitter':
            window.open(
              `https://twitter.com/intent/tweet?url=${encodeURIComponent(currentUrl)}&text=${encodeURIComponent(articleTitle)}`,
              '_blank',
              'width=600,height=400'
            );
            break;
          case 'linkedin':
            window.open(
              `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(currentUrl)}`,
              '_blank',
              'width=600,height=400'
            );
            break;
          case 'email':
            window.location.href = `mailto:?subject=${encodeURIComponent(articleTitle)}&body=${encodeURIComponent(`${articleDescription}\n\nRead more: ${currentUrl}`)}`;
            break;
          case 'copy':
            navigator.clipboard.writeText(currentUrl).then(() => {
              alert('Link copied to clipboard!');
            });
            break;
          default:
            // General share - use Web Share API if available, otherwise copy link
            if (navigator.share) {
              navigator.share({
                title: articleTitle,
                text: articleDescription,
                url: currentUrl,
              });
            } else {
              navigator.clipboard.writeText(currentUrl).then(() => {
                alert('Link copied to clipboard!');
              });
            }
        }
      } catch (err) {
        console.error('Error sharing article:', err);
      }
    };

    await measureAsync('Social Share Tracking', shareOperation);
  }, [articleSlug, article, measureAsync]);

  // Enhanced PDF viewer controls
  const handleViewFullArticle = useCallback(() => {
    measureSync('PDF Viewer Open', () => {
      setShowPDFViewer(true);
    });
  }, [measureSync]);

  const handleClosePDFViewer = useCallback(() => {
    measureSync('PDF Viewer Close', () => {
      setShowPDFViewer(false);
    });
  }, [measureSync]);

  // Bookmark functionality
  const handleBookmark = useCallback(async () => {
    const bookmarkOperation = async () => {
      try {
        const method = isBookmarked ? 'DELETE' : 'POST';
        const userId = localStorage.getItem('userId') || `guest_${Date.now()}`;
        const userEmail = localStorage.getItem('userEmail') || '<EMAIL>';

        const response = await fetch(`${BASE_URL}/author/articles/bookmark/${articleSlug}`, {
          method,
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': userId,
            'x-user-email': userEmail,
          },
        });

        if (response.ok) {
          setIsBookmarked(prev => !prev);
          // Store user ID for future use
          if (!localStorage.getItem('userId')) {
            localStorage.setItem('userId', userId);
          }
        } else {
          console.error('Failed to update bookmark');
        }
      } catch (err) {
        console.error('Error updating bookmark:', err);
      }
    };

    await measureAsync('Bookmark Toggle', bookmarkOperation);
  }, [articleSlug, isBookmarked, measureAsync]);









  if (loading) return <LoadingSpinner />;
  if (notFound) return <ArticleNotFound />;
  if (error) return <ErrorMessage message={error} />;
  if (!article) return <ErrorMessage message="Article not found" />;

  return (
    <ArticleErrorBoundary>
      <ArticleSEO article={article} breadcrumbs={breadcrumbs} />

      <motion.div
        className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
        initial="hidden"
        animate="show"
        variants={staggerContainer}
      >
        {/* Enhanced Article Header */}
        <motion.header className="mb-12" variants={staggerItem}>
          <div className="mb-6">
            {article.category && (
              <motion.span
                className="inline-flex items-center gap-2 bg-red-100 text-red-800 text-sm font-medium px-4 py-2 rounded-full"
                whileHover={{ scale: 1.05 }}
              >
                <TagIcon className="w-4 h-4" />
                {article.category.name}
              </motion.span>
            )}
          </div>

          <motion.h1
            className="text-4xl md:text-5xl font-bold text-blue-900 mb-6 leading-tight"
            variants={staggerItem}
          >
            {article.title}
          </motion.h1>

          {/* Enhanced Article Meta */}
          <motion.div
            className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-8 p-4 bg-gray-50 rounded-lg"
            variants={staggerItem}
          >
            <div className="flex items-center gap-2">
              <UserIcon className="w-4 h-4 text-blue-600" />
              <span className="font-medium text-blue-900">By {article.author}</span>
            </div>
            <div className="flex items-center gap-2">
              <CalendarIcon className="w-4 h-4 text-blue-600" />
              <span>Published {formatDate(article.createdAt)}</span>
            </div>
            {article.updatedAt !== article.createdAt && (
              <div className="flex items-center gap-2">
                <CalendarIcon className="w-4 h-4 text-blue-600" />
                <span>Updated {formatDate(article.updatedAt)}</span>
              </div>
            )}
            <div className="flex items-center gap-2">
              <EyeIcon className="w-4 h-4 text-blue-600" />
              <span>{viewCount} views</span>
            </div>
            <div className="flex items-center gap-2">
              <ShareIcon className="w-4 h-4 text-blue-600" />
              <span>{shareCount} shares</span>
            </div>
          </motion.div>

          {/* Enhanced Article Description */}
          {article.description && (
            <motion.div
              className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-8"
              variants={staggerItem}
            >
              <h2 className="text-lg font-semibold text-blue-900 mb-3">Abstract</h2>
              <p className="text-gray-700 leading-relaxed">
                {article.description}
              </p>
            </motion.div>
          )}

          {/* Enhanced Keywords */}
          {article.keywords && article.keywords.length > 0 && (
            <motion.div className="mb-8" variants={staggerItem}>
              <h3 className="text-sm font-semibold text-blue-900 mb-3">Keywords</h3>
              <div className="flex flex-wrap gap-2">
                {article.keywords.map((keyword, index) => (
                  <motion.span
                    key={index}
                    className="inline-block bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full"
                    whileHover={{ scale: 1.05 }}
                  >
                    {keyword}
                  </motion.span>
                ))}
              </div>
            </motion.div>
          )}

          {/* Enhanced Action Buttons */}
          <motion.div
            className="flex flex-wrap items-center justify-between gap-4 p-6 bg-gray-50 rounded-lg mb-8"
            variants={staggerItem}
          >
            {/* View Full Article Button */}
            <motion.button
              onClick={handleViewFullArticle}
              className="flex items-center gap-3 px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 font-medium shadow-md"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <DocumentTextIcon className="w-5 h-5" />
              View Full Article
            </motion.button>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              <motion.button
                onClick={handleBookmark}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors duration-200 font-medium ${
                  isBookmarked
                    ? 'bg-red-100 text-red-700 hover:bg-red-200'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <BookmarkIcon className="w-4 h-4" />
                {isBookmarked ? 'Bookmarked' : 'Bookmark'}
              </motion.button>

              <div className="relative">
                <motion.button
                  onClick={() => setShowShareMenu(!showShareMenu)}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors duration-200 font-medium"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <ShareIcon className="w-4 h-4" />
                  Share ({shareCount})
                </motion.button>

                {/* Share Dropdown Menu */}
                {showShareMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute top-full mt-2 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-48"
                  >
                    <div className="py-2">
                      <button
                        onClick={() => { handleSocialShare('facebook'); setShowShareMenu(false); }}
                        className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                      >
                        <span className="text-blue-600">📘</span>
                        Facebook
                      </button>
                      <button
                        onClick={() => { handleSocialShare('twitter'); setShowShareMenu(false); }}
                        className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                      >
                        <span className="text-blue-400">🐦</span>
                        Twitter
                      </button>
                      <button
                        onClick={() => { handleSocialShare('linkedin'); setShowShareMenu(false); }}
                        className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                      >
                        <span className="text-blue-700">💼</span>
                        LinkedIn
                      </button>
                      <button
                        onClick={() => { handleSocialShare('email'); setShowShareMenu(false); }}
                        className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                      >
                        <span className="text-gray-600">✉️</span>
                        Email
                      </button>
                      <button
                        onClick={() => { handleSocialShare('copy'); setShowShareMenu(false); }}
                        className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-3"
                      >
                        <span className="text-gray-600">🔗</span>
                        Copy Link
                      </button>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>
        </motion.header>



        {/* Enhanced PDF Viewer Section */}
        <AnimatePresence mode="wait">
          {!showPDFViewer ? (
            <motion.main
              className="mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200">
                <div className="bg-gradient-to-r from-red-50 to-blue-50 px-6 py-8 text-center border-b">
                  <div className="max-w-2xl mx-auto">
                    <motion.div
                      initial={{ scale: 0.9 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      <DocumentTextIcon className="w-16 h-16 text-red-600 mx-auto mb-4" />
                      <h2 className="text-3xl font-bold text-blue-900 mb-4">Read the Full Article</h2>
                      <p className="text-gray-600 mb-6 text-lg">
                        Access the complete research paper with detailed methodology, findings, and conclusions.
                      </p>
                      <motion.button
                        onClick={handleViewFullArticle}
                        className="inline-flex items-center px-8 py-4 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg transition-colors duration-200 shadow-lg hover:shadow-xl text-lg"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <DocumentTextIcon className="w-6 h-6 mr-3" />
                        View Full Article
                      </motion.button>
                      <p className="text-sm text-gray-500 mt-4 flex items-center justify-center gap-2">
                        <span>📚 Read online</span>
                        <span>•</span>
                        <span>🔒 Secure viewing</span>
                        <span>•</span>
                        <span>📱 Mobile optimized</span>
                      </p>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.main>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="fixed inset-0 z-50 bg-white"
            >
              <ViewArticle
                data={{
                  url: article.articleUrl,
                  title: article.title,
                  slug: article.slug,
                  updatedAt: article.updatedAt
                }}
                close={handleClosePDFViewer}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced Related Articles Section */}
        {relatedArticles.length > 0 && (
          <motion.section
            className="mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
              <motion.h2
                className="text-2xl font-bold text-blue-900 mb-6 flex items-center gap-3"
                variants={staggerItem}
              >
                <DocumentTextIcon className="w-6 h-6 text-red-600" />
                Related Articles
              </motion.h2>

              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 gap-6"
                variants={staggerContainer}
                initial="hidden"
                animate="show"
              >
                {relatedArticles.map((relatedArticle) => (
                  <motion.div
                    key={relatedArticle.articleId}
                    className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
                    variants={staggerItem}
                    whileHover={{ scale: 1.02 }}
                    onClick={() => window.location.href = `/articles/${relatedArticle.slug}`}
                  >
                    <h3 className="font-semibold text-blue-900 mb-2 line-clamp-2">
                      {relatedArticle.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {relatedArticle.description}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{relatedArticle.author}</span>
                      <span>{formatDate(relatedArticle.createdAt)}</span>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            </div>
          </motion.section>
        )}

        {/* Loading state for related articles */}
        {loadingRelated && (
          <motion.section
            className="mb-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <div className="bg-white rounded-lg shadow-lg p-6 border border-gray-200">
              <h2 className="text-2xl font-bold text-blue-900 mb-6">Related Articles</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="bg-gray-50 rounded-lg p-4 animate-pulse">
                    <div className="h-4 bg-gray-300 rounded mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded mb-3 w-3/4"></div>
                    <div className="flex justify-between">
                      <div className="h-2 bg-gray-300 rounded w-1/4"></div>
                      <div className="h-2 bg-gray-300 rounded w-1/4"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.section>
        )}

        {/* Enhanced Article Footer */}
        <motion.footer
          className="border-t border-gray-200 pt-8 mb-12"
          variants={staggerItem}
        >
          <div className="grid md:grid-cols-2 gap-8">
            {/* Author Info */}
            <motion.div
              className="bg-gray-50 p-6 rounded-lg"
              whileHover={{ scale: 1.02 }}
            >
              <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center gap-2">
                <UserIcon className="w-5 h-5" />
                About the Author
              </h3>
              <div className="text-gray-700">
                <p className="font-medium text-lg">{article.author}</p>
                {article.email && (
                  <p className="text-sm text-gray-600 mt-1">{article.email}</p>
                )}
                <p className="text-sm text-gray-600 mt-2">
                  Contributing researcher at Learning Publics Journal
                </p>
              </div>
            </motion.div>

            {/* Enhanced Article Stats */}
            <motion.div
              className="bg-blue-50 p-6 rounded-lg"
              whileHover={{ scale: 1.02 }}
            >
              <h3 className="text-lg font-semibold text-blue-900 mb-4">Article Statistics</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="flex items-center gap-2 text-gray-600">
                    <EyeIcon className="w-4 h-4" />
                    Views:
                  </span>
                  <span className="font-medium text-blue-900">{viewCount}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="flex items-center gap-2 text-gray-600">
                    <ShareIcon className="w-4 h-4" />
                    Shares:
                  </span>
                  <span className="font-medium text-blue-900">{shareCount}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="flex items-center gap-2 text-gray-600">
                    <CalendarIcon className="w-4 h-4" />
                    Published:
                  </span>
                  <span className="font-medium text-blue-900">{formatDate(article.createdAt)}</span>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.footer>
      </motion.div>
    </ArticleErrorBoundary>
  );
};

export default ArticleViewPage;
