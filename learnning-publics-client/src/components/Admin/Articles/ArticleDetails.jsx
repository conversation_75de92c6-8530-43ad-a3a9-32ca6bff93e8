import { useNavigate } from "react-router-dom";
import { TableData, Status } from "components/Admin/Articles/Articles.style";
function ArticleDetails({ data, id }) {
  const navigate = useNavigate();
  return (
    <TableData key={id} id={id} onClick={() => navigate(`${id}`)}>
      <td className='writer'>{data.author}</td>
      <td className='title'>{data.title}</td>
      <td className='email'>{data.email}</td>
      <td>
        <Status status={data.status}>{data.status}</Status>
      </td>
    </TableData>
  );
}

export default ArticleDetails;
