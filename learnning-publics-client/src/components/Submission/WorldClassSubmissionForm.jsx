import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import { MainContainer } from 'globalStyles';
import SEOHead from 'components/SEO/SEOHead';
import Colors from 'utils/colors';
import { toast } from 'react-hot-toast';
import jmsApp from 'api/jms';
import { ARTICLE_ENDPOINT, CATEGORY_ENDPOINT } from 'api/ACTION';

const FormContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 0;
`;

const FormWrapper = styled(MainContainer)`
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
`;

const HeroSection = styled.div`
  background: linear-gradient(135deg, ${Colors.primary} 0%, #5a6a72 100%);
  color: white;
  padding: 3rem 2rem;
  border-radius: 1rem;
  margin-bottom: 3rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
  }
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 1;
`;

const HeroTitle = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  
  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const ProgressBar = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const ProgressSteps = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const ProgressStep = styled.div`
  display: flex;
  align-items: center;
  flex: 1;
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }
`;

const StepNumber = styled.div`
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: ${props => props.active ? Colors.primary : props.completed ? Colors.secondary : '#e5e7eb'};
  color: ${props => props.active || props.completed ? 'white' : '#64748b'};
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  margin-right: 1rem;
  transition: all 0.3s ease;
`;

const StepLabel = styled.div`
  font-weight: 600;
  color: ${props => props.active ? Colors.primary : props.completed ? Colors.secondary : '#64748b'};
  transition: all 0.3s ease;
`;

const StepConnector = styled.div`
  flex: 1;
  height: 2px;
  background: ${props => props.completed ? Colors.secondary : '#e5e7eb'};
  margin: 0 1rem;
  transition: all 0.3s ease;
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const FormSection = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const SectionTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 1.5rem;
  border-bottom: 2px solid ${Colors.secondary};
  padding-bottom: 0.5rem;
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: ${props => props.columns || '1fr'};
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  
  .required {
    color: #dc2626;
    margin-left: 0.25rem;
  }
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
  
  &:focus {
    outline: none;
    border-color: ${Colors.primary};
    box-shadow: 0 0 0 3px rgba(70, 85, 92, 0.1);
  }
  
  &:invalid {
    border-color: #dc2626;
  }
`;

const TextArea = styled.textarea`
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  min-height: 120px;
  resize: vertical;
  transition: all 0.3s ease;
  background: white;
  font-family: inherit;
  
  &:focus {
    outline: none;
    border-color: ${Colors.primary};
    box-shadow: 0 0 0 3px rgba(70, 85, 92, 0.1);
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: ${Colors.primary};
    box-shadow: 0 0 0 3px rgba(70, 85, 92, 0.1);
  }
`;

const FileUploadArea = styled.div`
  border: 2px dashed ${props => props.isDragOver ? Colors.primary : '#e5e7eb'};
  border-radius: 0.75rem;
  padding: 3rem 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background: ${props => props.isDragOver ? '#f8fafc' : 'white'};
  cursor: pointer;
  
  &:hover {
    border-color: ${Colors.primary};
    background: #f8fafc;
  }
`;

const FileUploadIcon = styled.div`
  font-size: 3rem;
  color: ${Colors.primary};
  margin-bottom: 1rem;
`;

const FileUploadText = styled.div`
  font-size: 1.1rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 0.5rem;
`;

const FileUploadSubtext = styled.div`
  font-size: 0.9rem;
  color: #64748b;
`;

const UploadedFile = styled.div`
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1rem;
`;

const FileName = styled.div`
  font-weight: 600;
  color: #0369a1;
`;

const FileSize = styled.div`
  font-size: 0.875rem;
  color: #64748b;
`;

const RemoveFileButton = styled.button`
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background 0.3s ease;
  
  &:hover {
    background: #b91c1c;
  }
`;

const NavigationButtons = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
`;

const Button = styled.button`
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const PrimaryButton = styled(Button)`
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  color: white;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
`;

const SecondaryButton = styled(Button)`
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  
  &:hover:not(:disabled) {
    background: #e5e7eb;
  }
`;

const CheckboxGroup = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin: 1rem 0;
`;

const Checkbox = styled.input`
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
  accent-color: ${Colors.primary};
`;

const CheckboxLabel = styled.label`
  font-size: 0.95rem;
  line-height: 1.5;
  color: #374151;
  cursor: pointer;
`;

const ErrorMessage = styled.div`
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.5rem;
`;

const SuccessMessage = styled.div`
  background: #dcfce7;
  border: 1px solid #16a34a;
  color: #15803d;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 600;
`;

const WorldClassSubmissionForm = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [categories, setCategories] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const fileInputRef = useRef(null);

  const [formData, setFormData] = useState({
    // Step 1: Manuscript Information
    title: '',
    abstract: '',
    keywords: '',
    category: '',
    manuscriptType: '',
    
    // Step 2: Author Information
    correspondingAuthor: '',
    email: '',
    phone: '',
    institution: '',
    country: '',
    orcid: '',
    coAuthors: <AUTHORS>
    
    // Step 3: Manuscript Details
    wordCount: '',
    references: '',
    figures: '',
    tables: '',
    supplementaryFiles: '',
    fundingInfo: '',
    conflictOfInterest: '',
    
    // Step 4: File Upload & Declarations
    coverLetter: '',
    ethicsStatement: '',
    dataAvailability: '',
    
    // Declarations
    originalWork: false,
    noConflict: false,
    ethicsApproval: false,
    consentToPublish: false,
    agreeToTerms: false
  });

  const [errors, setErrors] = useState({});

  const steps = [
    { number: 1, label: 'Manuscript Info', completed: currentStep > 1 },
    { number: 2, label: 'Author Details', completed: currentStep > 2 },
    { number: 3, label: 'Manuscript Details', completed: currentStep > 3 },
    { number: 4, label: 'Upload & Submit', completed: currentStep > 4 }
  ];

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await jmsApp.get(CATEGORY_ENDPOINT.GET_ARTICLE_CATEGORY());
      if (response.data && response.data.category) {
        setCategories(response.data.category);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to load categories');
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};
    
    switch (step) {
      case 1:
        if (!formData.title.trim()) newErrors.title = 'Title is required';
        if (!formData.abstract.trim()) newErrors.abstract = 'Abstract is required';
        if (!formData.keywords.trim()) newErrors.keywords = 'Keywords are required';
        if (!formData.category) newErrors.category = 'Category is required';
        if (!formData.manuscriptType) newErrors.manuscriptType = 'Manuscript type is required';
        break;
      case 2:
        if (!formData.correspondingAuthor.trim()) newErrors.correspondingAuthor = 'Corresponding author is required';
        if (!formData.email.trim()) newErrors.email = 'Email is required';
        if (!formData.institution.trim()) newErrors.institution = 'Institution is required';
        if (!formData.country.trim()) newErrors.country = 'Country is required';
        break;
      case 3:
        if (!formData.wordCount.trim()) newErrors.wordCount = 'Word count is required';
        break;
      case 4:
        if (!uploadedFile) newErrors.file = 'Manuscript file is required';
        if (!formData.originalWork) newErrors.originalWork = 'You must confirm this is original work';
        if (!formData.agreeToTerms) newErrors.agreeToTerms = 'You must agree to the terms';
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 4));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleFileUpload = (file) => {
    if (file) {
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please upload a PDF or Word document');
        return;
      }
      
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast.error('File size must be less than 10MB');
        return;
      }
      
      setUploadedFile(file);
      toast.success('File uploaded successfully');
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const file = e.dataTransfer.files[0];
    handleFileUpload(file);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const removeFile = () => {
    setUploadedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const convertFileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  const handleSubmit = async () => {
    if (!validateStep(4)) return;

    setIsSubmitting(true);

    try {
      // Convert file to base64 for backend compatibility
      let fileData = '';
      if (uploadedFile) {
        fileData = await convertFileToBase64(uploadedFile);
      }

      // Prepare submission data in the format expected by backend
      const submitData = {
        // Map new form fields to backend expectations
        title: formData.title,
        abstract: formData.abstract,
        keywords: formData.keywords,
        category: formData.category,
        manuscriptType: formData.manuscriptType,
        correspondingAuthor: formData.correspondingAuthor,
        email: formData.email,
        phone: formData.phone,
        institution: formData.institution,
        country: formData.country,
        orcid: formData.orcid,
        coAuthors: <AUTHORS>
        wordCount: formData.wordCount,
        references: formData.references,
        figures: formData.figures,
        tables: formData.tables,
        supplementaryFiles: formData.supplementaryFiles,
        fundingInfo: formData.fundingInfo,
        conflictOfInterest: formData.conflictOfInterest,
        coverLetter: formData.coverLetter,
        ethicsStatement: formData.ethicsStatement,
        dataAvailability: formData.dataAvailability,
        originalWork: formData.originalWork,
        noConflict: formData.noConflict,
        ethicsApproval: formData.ethicsApproval,
        consentToPublish: formData.consentToPublish,
        agreeToTerms: formData.agreeToTerms,
        fileData: fileData,
        format: uploadedFile ? uploadedFile.type : 'application/pdf'
      };

      // Submit to backend
      const response = await jmsApp.post(ARTICLE_ENDPOINT.SEND_ARTICLE(), submitData);
      
      if (response.data) {
        setIsSubmitted(true);
        toast.success('Manuscript submitted successfully!');
        
        // Reset form after successful submission
        setTimeout(() => {
          setCurrentStep(1);
          setFormData({
            title: '', abstract: '', keywords: '', category: '', manuscriptType: '',
            correspondingAuthor: '', email: '', phone: '', institution: '', country: '', orcid: '', coAuthors: <AUTHORS>
            wordCount: '', references: '', figures: '', tables: '', supplementaryFiles: '', fundingInfo: '', conflictOfInterest: '',
            coverLetter: '', ethicsStatement: '', dataAvailability: '',
            originalWork: false, noConflict: false, ethicsApproval: false, consentToPublish: false, agreeToTerms: false
          });
          setUploadedFile(null);
          setIsSubmitted(false);
        }, 3000);
      }
    } catch (error) {
      console.error('Submission error:', error);
      toast.error('Failed to submit manuscript. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <FormContainer>
        <FormWrapper>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <FormSection style={{ textAlign: 'center', padding: '4rem 2rem' }}>
              <div style={{ fontSize: '4rem', color: Colors.secondary, marginBottom: '2rem' }}>✓</div>
              <h2 style={{ fontSize: '2rem', fontWeight: '700', color: Colors.primary, marginBottom: '1rem' }}>
                Submission Successful!
              </h2>
              <p style={{ fontSize: '1.1rem', color: '#64748b', marginBottom: '2rem' }}>
                Thank you for submitting your manuscript to Learning Publics Journal. 
                You will receive a confirmation email shortly with your submission ID.
              </p>
              <p style={{ fontSize: '0.95rem', color: '#64748b' }}>
                Our editorial team will review your submission and contact you within 2-3 business days.
              </p>
            </FormSection>
          </motion.div>
        </FormWrapper>
      </FormContainer>
    );
  }

  return (
    <>
      <SEOHead
        title="Submit Your Research - Learning Publics Journal"
        description="Submit your research manuscript to Learning Publics Journal. Our streamlined submission process ensures your work reaches the global academic community."
        keywords="submit manuscript, academic publishing, research submission, peer review, agriculture research"
        canonicalUrl="/submit"
      />
      
      <FormContainer>
        <FormWrapper>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <HeroSection>
              <HeroContent>
                <HeroTitle>Submit Your Research</HeroTitle>
                <HeroSubtitle>
                  Share your groundbreaking research with the global academic community. 
                  Our streamlined submission process ensures your work gets the attention it deserves.
                </HeroSubtitle>
              </HeroContent>
            </HeroSection>

            <ProgressBar>
              <ProgressSteps>
                {steps.map((step, index) => (
                  <React.Fragment key={step.number}>
                    <ProgressStep>
                      <StepNumber 
                        active={currentStep === step.number}
                        completed={step.completed}
                      >
                        {step.completed ? '✓' : step.number}
                      </StepNumber>
                      <StepLabel 
                        active={currentStep === step.number}
                        completed={step.completed}
                      >
                        {step.label}
                      </StepLabel>
                    </ProgressStep>
                    {index < steps.length - 1 && (
                      <StepConnector completed={step.completed} />
                    )}
                  </React.Fragment>
                ))}
              </ProgressSteps>
            </ProgressBar>

            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {currentStep === 1 && (
                  <FormSection>
                    <SectionTitle>Manuscript Information</SectionTitle>
                    
                    <FormGroup>
                      <Label>
                        Manuscript Title <span className="required">*</span>
                      </Label>
                      <Input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Enter the full title of your manuscript"
                        maxLength="250"
                      />
                      {errors.title && <ErrorMessage>{errors.title}</ErrorMessage>}
                    </FormGroup>

                    <FormGroup>
                      <Label>
                        Abstract <span className="required">*</span>
                      </Label>
                      <TextArea
                        name="abstract"
                        value={formData.abstract}
                        onChange={handleInputChange}
                        placeholder="Provide a comprehensive abstract (250-300 words)"
                        maxLength="2000"
                        style={{ minHeight: '150px' }}
                      />
                      <div style={{ fontSize: '0.875rem', color: '#64748b', marginTop: '0.5rem' }}>
                        {formData.abstract.length}/2000 characters
                      </div>
                      {errors.abstract && <ErrorMessage>{errors.abstract}</ErrorMessage>}
                    </FormGroup>

                    <FormGrid columns="1fr 1fr">
                      <FormGroup>
                        <Label>
                          Keywords <span className="required">*</span>
                        </Label>
                        <Input
                          type="text"
                          name="keywords"
                          value={formData.keywords}
                          onChange={handleInputChange}
                          placeholder="5-7 keywords separated by commas"
                        />
                        {errors.keywords && <ErrorMessage>{errors.keywords}</ErrorMessage>}
                      </FormGroup>

                      <FormGroup>
                        <Label>
                          Category <span className="required">*</span>
                        </Label>
                        <Select
                          name="category"
                          value={formData.category}
                          onChange={handleInputChange}
                        >
                          <option value="">Select a category</option>
                          {categories.map(category => (
                            <option key={category._id} value={category._id}>
                              {category.name}
                            </option>
                          ))}
                        </Select>
                        {errors.category && <ErrorMessage>{errors.category}</ErrorMessage>}
                      </FormGroup>
                    </FormGrid>

                    <FormGroup>
                      <Label>
                        Manuscript Type <span className="required">*</span>
                      </Label>
                      <Select
                        name="manuscriptType"
                        value={formData.manuscriptType}
                        onChange={handleInputChange}
                      >
                        <option value="">Select manuscript type</option>
                        <option value="original-research">Original Research Article</option>
                        <option value="review">Review Article</option>
                        <option value="case-study">Case Study</option>
                        <option value="short-communication">Short Communication</option>
                        <option value="letter">Letter to Editor</option>
                        <option value="editorial">Editorial</option>
                      </Select>
                      {errors.manuscriptType && <ErrorMessage>{errors.manuscriptType}</ErrorMessage>}
                    </FormGroup>
                  </FormSection>
                )}

                {currentStep === 2 && (
                  <FormSection>
                    <SectionTitle>Author Information</SectionTitle>
                    
                    <FormGrid columns="1fr 1fr">
                      <FormGroup>
                        <Label>
                          Corresponding Author <span className="required">*</span>
                        </Label>
                        <Input
                          type="text"
                          name="correspondingAuthor"
                          value={formData.correspondingAuthor}
                          onChange={handleInputChange}
                          placeholder="Dr. John Smith"
                        />
                        {errors.correspondingAuthor && <ErrorMessage>{errors.correspondingAuthor}</ErrorMessage>}
                      </FormGroup>

                      <FormGroup>
                        <Label>
                          Email Address <span className="required">*</span>
                        </Label>
                        <Input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && <ErrorMessage>{errors.email}</ErrorMessage>}
                      </FormGroup>
                    </FormGrid>

                    <FormGrid columns="1fr 1fr">
                      <FormGroup>
                        <Label>Phone Number</Label>
                        <Input
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          placeholder="+****************"
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>ORCID ID</Label>
                        <Input
                          type="text"
                          name="orcid"
                          value={formData.orcid}
                          onChange={handleInputChange}
                          placeholder="0000-0000-0000-0000"
                        />
                      </FormGroup>
                    </FormGrid>

                    <FormGrid columns="1fr 1fr">
                      <FormGroup>
                        <Label>
                          Institution/Affiliation <span className="required">*</span>
                        </Label>
                        <Input
                          type="text"
                          name="institution"
                          value={formData.institution}
                          onChange={handleInputChange}
                          placeholder="University of Agriculture"
                        />
                        {errors.institution && <ErrorMessage>{errors.institution}</ErrorMessage>}
                      </FormGroup>

                      <FormGroup>
                        <Label>
                          Country <span className="required">*</span>
                        </Label>
                        <Input
                          type="text"
                          name="country"
                          value={formData.country}
                          onChange={handleInputChange}
                          placeholder="United States"
                        />
                        {errors.country && <ErrorMessage>{errors.country}</ErrorMessage>}
                      </FormGroup>
                    </FormGrid>

                    <FormGroup>
                      <Label>Co-Authors</Label>
                      <TextArea
                        name="coAuthors"
                        value={formData.coAuthors}
                        onChange={handleInputChange}
                        placeholder="List all co-authors with their affiliations (one per line)"
                        style={{ minHeight: '100px' }}
                      />
                    </FormGroup>
                  </FormSection>
                )}

                {currentStep === 3 && (
                  <FormSection>
                    <SectionTitle>Manuscript Details</SectionTitle>
                    
                    <FormGrid columns="1fr 1fr 1fr">
                      <FormGroup>
                        <Label>
                          Word Count <span className="required">*</span>
                        </Label>
                        <Input
                          type="number"
                          name="wordCount"
                          value={formData.wordCount}
                          onChange={handleInputChange}
                          placeholder="5000"
                          min="1"
                        />
                        {errors.wordCount && <ErrorMessage>{errors.wordCount}</ErrorMessage>}
                      </FormGroup>

                      <FormGroup>
                        <Label>Number of References</Label>
                        <Input
                          type="number"
                          name="references"
                          value={formData.references}
                          onChange={handleInputChange}
                          placeholder="25"
                          min="0"
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Number of Figures</Label>
                        <Input
                          type="number"
                          name="figures"
                          value={formData.figures}
                          onChange={handleInputChange}
                          placeholder="3"
                          min="0"
                        />
                      </FormGroup>
                    </FormGrid>

                    <FormGrid columns="1fr 1fr">
                      <FormGroup>
                        <Label>Number of Tables</Label>
                        <Input
                          type="number"
                          name="tables"
                          value={formData.tables}
                          onChange={handleInputChange}
                          placeholder="2"
                          min="0"
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Supplementary Files</Label>
                        <Input
                          type="text"
                          name="supplementaryFiles"
                          value={formData.supplementaryFiles}
                          onChange={handleInputChange}
                          placeholder="List any supplementary materials"
                        />
                      </FormGroup>
                    </FormGrid>

                    <FormGroup>
                      <Label>Funding Information</Label>
                      <TextArea
                        name="fundingInfo"
                        value={formData.fundingInfo}
                        onChange={handleInputChange}
                        placeholder="Provide details about funding sources, grant numbers, and acknowledgments"
                        style={{ minHeight: '100px' }}
                      />
                    </FormGroup>

                    <FormGroup>
                      <Label>Conflict of Interest Statement</Label>
                      <TextArea
                        name="conflictOfInterest"
                        value={formData.conflictOfInterest}
                        onChange={handleInputChange}
                        placeholder="Declare any potential conflicts of interest or state 'The authors declare no conflict of interest'"
                        style={{ minHeight: '80px' }}
                      />
                    </FormGroup>
                  </FormSection>
                )}

                {currentStep === 4 && (
                  <FormSection>
                    <SectionTitle>File Upload & Final Declarations</SectionTitle>
                    
                    <FormGroup>
                      <Label>
                        Manuscript File <span className="required">*</span>
                      </Label>
                      <FileUploadArea
                        isDragOver={isDragOver}
                        onDrop={handleDrop}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <FileUploadIcon>📄</FileUploadIcon>
                        <FileUploadText>
                          {uploadedFile ? 'File Uploaded Successfully' : 'Upload Manuscript File'}
                        </FileUploadText>
                        <FileUploadSubtext>
                          Drag and drop your file here, or click to browse
                          <br />
                          Supported formats: PDF, DOC, DOCX (Max 10MB)
                        </FileUploadSubtext>
                      </FileUploadArea>
                      
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept=".pdf,.doc,.docx"
                        onChange={(e) => handleFileUpload(e.target.files[0])}
                        style={{ display: 'none' }}
                      />
                      
                      {uploadedFile && (
                        <UploadedFile>
                          <div>
                            <FileName>{uploadedFile.name}</FileName>
                            <FileSize>{formatFileSize(uploadedFile.size)}</FileSize>
                          </div>
                          <RemoveFileButton onClick={removeFile}>
                            Remove
                          </RemoveFileButton>
                        </UploadedFile>
                      )}
                      
                      {errors.file && <ErrorMessage>{errors.file}</ErrorMessage>}
                    </FormGroup>

                    <FormGroup>
                      <Label>Cover Letter</Label>
                      <TextArea
                        name="coverLetter"
                        value={formData.coverLetter}
                        onChange={handleInputChange}
                        placeholder="Provide a brief cover letter explaining the significance of your research"
                        style={{ minHeight: '120px' }}
                      />
                    </FormGroup>

                    <FormGrid columns="1fr 1fr">
                      <FormGroup>
                        <Label>Ethics Statement</Label>
                        <TextArea
                          name="ethicsStatement"
                          value={formData.ethicsStatement}
                          onChange={handleInputChange}
                          placeholder="If applicable, provide ethics approval details"
                          style={{ minHeight: '80px' }}
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Data Availability Statement</Label>
                        <TextArea
                          name="dataAvailability"
                          value={formData.dataAvailability}
                          onChange={handleInputChange}
                          placeholder="Describe how readers can access the data supporting your conclusions"
                          style={{ minHeight: '80px' }}
                        />
                      </FormGroup>
                    </FormGrid>

                    <div style={{ marginTop: '2rem', padding: '1.5rem', background: '#f8fafc', borderRadius: '0.5rem' }}>
                      <h3 style={{ fontSize: '1.1rem', fontWeight: '600', marginBottom: '1rem', color: Colors.primary }}>
                        Required Declarations
                      </h3>
                      
                      <CheckboxGroup>
                        <Checkbox
                          type="checkbox"
                          name="originalWork"
                          checked={formData.originalWork}
                          onChange={handleInputChange}
                          id="originalWork"
                        />
                        <CheckboxLabel htmlFor="originalWork">
                          I confirm that this manuscript represents original work that has not been published elsewhere and is not under consideration by another journal.
                        </CheckboxLabel>
                      </CheckboxGroup>
                      {errors.originalWork && <ErrorMessage>{errors.originalWork}</ErrorMessage>}

                      <CheckboxGroup>
                        <Checkbox
                          type="checkbox"
                          name="noConflict"
                          checked={formData.noConflict}
                          onChange={handleInputChange}
                          id="noConflict"
                        />
                        <CheckboxLabel htmlFor="noConflict">
                          I have disclosed all potential conflicts of interest related to this research.
                        </CheckboxLabel>
                      </CheckboxGroup>

                      <CheckboxGroup>
                        <Checkbox
                          type="checkbox"
                          name="ethicsApproval"
                          checked={formData.ethicsApproval}
                          onChange={handleInputChange}
                          id="ethicsApproval"
                        />
                        <CheckboxLabel htmlFor="ethicsApproval">
                          If applicable, I have obtained all necessary ethics approvals and permissions for this research.
                        </CheckboxLabel>
                      </CheckboxGroup>

                      <CheckboxGroup>
                        <Checkbox
                          type="checkbox"
                          name="consentToPublish"
                          checked={formData.consentToPublish}
                          onChange={handleInputChange}
                          id="consentToPublish"
                        />
                        <CheckboxLabel htmlFor="consentToPublish">
                          I consent to the publication of this work under the journal's open access license.
                        </CheckboxLabel>
                      </CheckboxGroup>

                      <CheckboxGroup>
                        <Checkbox
                          type="checkbox"
                          name="agreeToTerms"
                          checked={formData.agreeToTerms}
                          onChange={handleInputChange}
                          id="agreeToTerms"
                        />
                        <CheckboxLabel htmlFor="agreeToTerms">
                          I agree to the journal's terms and conditions and publication policies.
                        </CheckboxLabel>
                      </CheckboxGroup>
                      {errors.agreeToTerms && <ErrorMessage>{errors.agreeToTerms}</ErrorMessage>}
                    </div>
                  </FormSection>
                )}
              </motion.div>
            </AnimatePresence>

            <NavigationButtons>
              <div>
                {currentStep > 1 && (
                  <SecondaryButton onClick={prevStep}>
                    Previous Step
                  </SecondaryButton>
                )}
              </div>
              
              <div>
                {currentStep < 4 ? (
                  <PrimaryButton onClick={nextStep}>
                    Next Step
                  </PrimaryButton>
                ) : (
                  <PrimaryButton 
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Manuscript'}
                  </PrimaryButton>
                )}
              </div>
            </NavigationButtons>
          </motion.div>
        </FormWrapper>
      </FormContainer>
    </>
  );
};

export default WorldClassSubmissionForm;
