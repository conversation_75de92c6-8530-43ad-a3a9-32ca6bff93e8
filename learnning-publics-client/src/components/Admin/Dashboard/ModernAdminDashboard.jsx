import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { observer } from 'mobx-react-lite';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import Colors from 'utils/colors';
import authStore from 'mobx/AuthStore';
import jmsApp from 'api/jms';
import { ADMIN_ROLES } from 'utils/admin-roles';
import { ARTICLE_ENDPOINT } from 'api/ACTION';
import {
  FaDollarSign,
  FaUsers,
  FaFileAlt,
  FaChartLine,
  FaCog
} from 'react-icons/fa';
import { 
  MdAnalytics, 
  MdTrendingUp,
  MdRateReview
} from 'react-icons/md';
import { SiSimpleanalytics } from 'react-icons/si';
import { HiViewGridAdd } from 'react-icons/hi';

// Modern Dashboard Styled Components
const DashboardContainer = styled.div`
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
`;

const WelcomeSection = styled.div`
  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: ${Colors.primary};
    margin-bottom: 0.5rem;
  }
  
  p {
    color: #64748b;
    font-size: 1.1rem;
  }
`;

const QuickActions = styled.div`
  display: flex;
  gap: 1rem;
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
  }
`;

const ActionButton = styled.button`
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
  
  @media (max-width: 768px) {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => props.color || Colors.primary};
  }
`;

const StatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const StatIcon = styled.div`
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  background: ${props => props.color || Colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    color: white;
    font-size: 1.5rem;
  }
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
`;

const StatTrend = styled.div`
  color: ${props => props.positive ? '#16a34a' : '#dc2626'};
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const Card = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
`;

const CardTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #9ca3af;
  text-align: center;
`;

const ModernAdminDashboard = observer(() => {
  const navigate = useNavigate();
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const currentUser = authStore.user;
  const userRole = authStore.userRole;

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await jmsApp.get(ARTICLE_ENDPOINT.GET_ALL_ARTICLES);
      if (response.data?.success) {
        setArticles(response.data.data || []);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const publishedCount = authStore.publishedArticles?.length || 0;
  const reviewCount = authStore.reviewedArticles?.review?.length || 0;
  const totalArticles = articles.length || 0;

  if (userRole === ADMIN_ROLES.E_LEARNING) {
    return (
      <DashboardContainer>
        <Header>
          <WelcomeSection>
            <h1>E-Learning Dashboard</h1>
            <p>Welcome back, {currentUser?.name}! Manage your e-learning content.</p>
          </WelcomeSection>
          <QuickActions>
            <ActionButton onClick={() => navigate("/admin/course")}>
              <FaFileAlt />
              Resources
            </ActionButton>
            <ActionButton onClick={() => navigate("/admin/plan")}>
              <FaCog />
              Courses
            </ActionButton>
          </QuickActions>
        </Header>

        <EmptyState>
          <FaFileAlt size={64} />
          <h3>E-Learning Management</h3>
          <p>Access your e-learning resources and course management tools.</p>
        </EmptyState>
      </DashboardContainer>
    );
  }

  return (
    <DashboardContainer>
      <Header>
        <WelcomeSection>
          <h1>Learning Publics Admin</h1>
          <p>Welcome back, {currentUser?.name || 'Administrator'}! Here's your journal overview.</p>
        </WelcomeSection>
        <QuickActions>
          <ActionButton onClick={() => navigate("/admin/articles")}>
            <FaFileAlt />
            Articles
          </ActionButton>
          <ActionButton onClick={() => navigate("/admin/pricing")}>
            <FaDollarSign />
            Pricing
          </ActionButton>
          <ActionButton onClick={() => navigate("/admin/analytics")}>
            <MdAnalytics />
            Analytics
          </ActionButton>
        </QuickActions>
      </Header>

      <StatsGrid>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <StatCard color="#16a34a">
            <StatHeader>
              <div>
                <StatValue>{publishedCount}</StatValue>
                <StatLabel>Published Articles</StatLabel>
                <StatTrend positive>
                  <MdTrendingUp />
                  +12% this month
                </StatTrend>
              </div>
              <StatIcon color="#16a34a">
                <SiSimpleanalytics />
              </StatIcon>
            </StatHeader>
          </StatCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <StatCard color="#3b82f6">
            <StatHeader>
              <div>
                <StatValue>{reviewCount}</StatValue>
                <StatLabel>Under Review</StatLabel>
                <StatTrend positive>
                  <MdTrendingUp />
                  +8% this week
                </StatTrend>
              </div>
              <StatIcon color="#3b82f6">
                <MdRateReview />
              </StatIcon>
            </StatHeader>
          </StatCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <StatCard color="#f59e0b">
            <StatHeader>
              <div>
                <StatValue>{totalArticles}</StatValue>
                <StatLabel>Total Submissions</StatLabel>
                <StatTrend positive>
                  <MdTrendingUp />
                  +15% this month
                </StatTrend>
              </div>
              <StatIcon color="#f59e0b">
                <FaFileAlt />
              </StatIcon>
            </StatHeader>
          </StatCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <StatCard color="#8b5cf6">
            <StatHeader>
              <div>
                <StatValue>$12,450</StatValue>
                <StatLabel>Revenue (YTD)</StatLabel>
                <StatTrend positive>
                  <MdTrendingUp />
                  +23% vs last year
                </StatTrend>
              </div>
              <StatIcon color="#8b5cf6">
                <FaChartLine />
              </StatIcon>
            </StatHeader>
          </StatCard>
        </motion.div>
      </StatsGrid>

      <ContentGrid>
        <Card>
          <CardTitle>
            <FaFileAlt />
            Recent Activity
          </CardTitle>
          
          {loading ? (
            <EmptyState>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p>Loading...</p>
            </EmptyState>
          ) : totalArticles === 0 ? (
            <EmptyState>
              <FaFileAlt size={48} />
              <h4>No Articles Yet</h4>
              <p>Articles will appear here once they are submitted.</p>
            </EmptyState>
          ) : (
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                <span style={{ color: '#64748b', fontSize: '0.875rem' }}>
                  Latest submissions and updates
                </span>
                <ActionButton onClick={() => navigate("/admin/articles")}>
                  <HiViewGridAdd />
                  View All
                </ActionButton>
              </div>
              <p style={{ color: '#64748b' }}>
                {totalArticles} total articles in the system
              </p>
            </div>
          )}
        </Card>

        <Card>
          <CardTitle>
            <MdAnalytics />
            Quick Actions
          </CardTitle>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <ActionButton onClick={() => navigate("/admin/pricing")} style={{ width: '100%' }}>
              <FaDollarSign />
              Manage Pricing
            </ActionButton>
            
            <ActionButton onClick={() => navigate("/admin/analytics")} style={{ width: '100%' }}>
              <MdAnalytics />
              View Analytics
            </ActionButton>
            
            <ActionButton onClick={() => navigate("/admin/staffs")} style={{ width: '100%' }}>
              <FaUsers />
              Manage Staff
            </ActionButton>
            
            <ActionButton onClick={() => navigate("/admin/settings")} style={{ width: '100%' }}>
              <FaCog />
              Settings
            </ActionButton>
          </div>
          
          <div style={{ marginTop: '2rem', padding: '1rem', background: '#f8fafc', borderRadius: '0.5rem' }}>
            <h4 style={{ color: Colors.primary, marginBottom: '0.5rem', fontSize: '1rem', fontWeight: '600' }}>
              System Status
            </h4>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', color: '#16a34a' }}>
              <div style={{ width: '8px', height: '8px', background: '#16a34a', borderRadius: '50%' }}></div>
              All systems operational
            </div>
          </div>
        </Card>
      </ContentGrid>
    </DashboardContainer>
  );
});

export default ModernAdminDashboard;
