import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, FaNewspaper } from "react-icons/fa";
import React, { useEffect, useState } from "react";

import { ARTICLE_ENDPOINT } from "api/ACTION";
import { Link, useNavigate } from "react-router-dom";
import { MagnifyingGlassIcon, BookOpenIcon, AcademicCapIcon } from '@heroicons/react/24/outline';
// import ViewArticle from "./Article Section/view-article"; // Removed - using proper routing now
import { convertUTCToLocalTime } from "utils/utc-to-date";
import jmsApp from "api/jms";
import landingPageGg1 from "../../../src/assets/images/banner.jpg";
import landingPageGg2 from "../../../src/assets/images/banner2.jpg";
import { override } from "utils/admin-roles";
import styled from "styled-components";
import tw from "twin.macro";

// import BlogSection from "./Blog Section"; // Using local BlogSection with statistics

import ArticleSection from "./Article Section";

// HeroComp is defined below in this file

const HomeComp = () => {
  const [isFetchingArticles, setIsFetchingArticles] = useState(false);
  const [articles, setArticles] = useState([]);
  const getAllArticles = async () => {
    try {
      setIsFetchingArticles(true);

      const { data } = await jmsApp.get(ARTICLE_ENDPOINT.ARTICLE_PUBLISHED());

      if (data.success) {
        setArticles(data.article);
      }
    } catch (err) {
      // console.log(err);
    } finally {
      setIsFetchingArticles(false);
    }
  };

  useEffect(() => {
    getAllArticles();
  }, []);

  return (
    <>
      <HeroComp />
      <div className="flex flex-col gap-8 w-full">
        <ArticleSection
          isFetchingArticles={isFetchingArticles}
          articles={articles}
          getAllArticles={getAllArticles}
        />
        <BlogSection articles={articles} />
      </div>
    </>
  );
};

export default HomeComp;
function HeroComp() {
  const images = [landingPageGg1, landingPageGg2];
  const [activeImage, setActiveImage] = useState(0);
  const [countDown, setCountDown] = useState(10000);
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const intervalId = setInterval(() => {
      setCountDown((prevCount) => prevCount - 1000); // decrement countDown by 1000 milliseconds
    }, 1000);

    return () => clearInterval(intervalId); // cleanup function to clear the interval
  }, []);

  useEffect(() => {
    if (countDown === 0) {
      setActiveImage((prevImage) => (prevImage === 0 ? 1 : 0));
      setCountDown(10000);
    }
  }, [countDown]);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/articles?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  return (
    <div className="mb-6 w-full">
      {/* Hero Section with Search */}
      <div className="relative bg-gradient-to-r from-red-900 to-red-800 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Learning Publics Journal
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Discover cutting-edge research and scholarly articles across multiple disciplines
            </p>

            {/* Prominent Search Bar */}
            <div className="max-w-4xl mx-auto mb-8">
              <form onSubmit={handleSearch} className="relative">
                <div className="flex items-center bg-white rounded-full shadow-2xl overflow-hidden">
                  <div className="flex-1 relative">
                    <MagnifyingGlassIcon className="absolute left-6 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400" />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search articles, authors, keywords..."
                      className="w-full pl-16 pr-6 py-6 text-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-0"
                    />
                  </div>
                  <button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-6 text-lg font-semibold transition-colors duration-200"
                  >
                    Search Articles
                  </button>
                </div>
              </form>
            </div>

            {/* Quick Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                to="/articles"
                className="inline-flex items-center px-8 py-4 bg-white text-red-900 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg"
              >
                <BookOpenIcon className="h-5 w-5 mr-2" />
                View All Articles
              </Link>
              <Link
                to="/submit"
                className="inline-flex items-center px-8 py-4 bg-transparent border-2 border-white text-white rounded-full font-semibold hover:bg-white hover:text-red-900 transition-colors duration-200"
              >
                <AcademicCapIcon className="h-5 w-5 mr-2" />
                Submit Article
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* About Section */}
      <div className="bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">About the Journal</h2>
              <div className="w-20 h-1 bg-red-900 mb-6"></div>
              <p className="text-lg text-gray-700 leading-relaxed">
                The journal is the official journal of{" "}
                <strong>Learning Publics</strong>. It provides a platform for
                scholarly exchange between family medicine and primary health care
                researchers and practitioners across Africa. It provides a contextual
                and holistic view of family medicine and primary health care as
                practiced across the continent.
              </p>
            </div>
            <div className="hidden lg:block">
              <div className="relative h-96 rounded-lg overflow-hidden shadow-xl">
                <img
                  src={images[activeImage]}
                  alt="Learning Publics"
                  className="w-full h-full object-cover transition-opacity duration-1000"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
// export const testArticles = [
//   {
//     title:
//       "Understanding of ‘generalist medical practice’ in South African medical schools",
//     author: "Langalibalele H. Mabuza, Mosa Moshabela",
//     date: "08 March 2024",
//     type: "Original Research"
//   },
//   {
//     title:
//       "IDEAL: Maintaining PHC-focused training in a MBChB programme through a COVID-induced innovation",
//     author:
//       "Nondumiso B.Q. Ncube, Tawanda Chivese, Ferdinand C. Mukumbang, Hazel A. Bradley, Helen Schneider, Richard Laing",
//     date: "08 March 2024",
//     type: "Review Article"
//   },
//   {
//     title:
//       "Views of Nigerian civil servants about compulsory COVID-19 vaccination: A qualitative study",
//     author: "Ian Couper, Julia Blitz, Therese Fish",
//     date: "08 March 2024",
//     type: "Original Research"
//   },
//   {
//     title:
//       "Mental health impact of COVID-19 on healthcare workers versus adults in Africa",
//     author: "Thandi M. Dlamini, Siyabonga Dlamini",
//     date: "08 March 2024",
//     type: "Original Research"
//   }
// ];
// ArticleSection component removed from here - now using the imported component from ./Article Section/index.jsx
// This ensures proper routing to individual article pages instead of modal popup
function BlogSection({ articles }) {
  return (
    <div className=" flex flex-col gap-6 items-start justify-start divide-y-2 pr-4">
      <div className=" flex flex-col items-start justify-start gap-2">
        <div className=" flex items-center justify-start gap-3 uppercase text-sm xxs:text-lg w-full ">
          <FaChartBar />
          <span>FAST FACTS 2018-{new Date().getFullYear()} STATISTICS (UPDATED DAILY)</span>
        </div>
        <div className=" flex items-center flex-col xxs:flex-row justify-start gap-6 text-white w-full uppercase">
          <div className=" flex flex-col bg-red-900 py-[0.5px] w-[150px]  lg:w-[250px] rounded-lg items-center justify-center ">
            <span className=" text-4xl">{articles.length + 56}</span>
            <span className="text-sm">published</span>
            <span className="text-sm">content</span>
          </div>
          <div className=" flex flex-col bg-red-900 py-[0.5px]  w-[150px]  lg:w-[250px] rounded-lg items-center justify-center ">
            <h1 className=" text-4xl">{articles.length * 3 + 90}</h1>
            <span className="text-sm">total</span>
            <span className="text-sm">downloads</span>
          </div>
        </div>
      </div>
      <div className=" flex flex-col items-start justify-start gap-4 pt-6 bg-gray-200 p-2">
        <div className=" flex items-center justify-start uppercase font-bold text-xl gap-3">
          <FaNewspaper />
          <p>announcement</p>
        </div>
        <div className=" flex flex-col items-start justify-start gap-4">
          <p className=" text-start capitalize font-bold text-red-400">
            other programs
          </p>
          <p>
            Prepare for success in WAEC and JAMB with our tailored courses.
            Covering a range of subjects, our comprehensive platform offers
            interactive lessons, practice tests, and personalized feedback to
            build confidence and competence for both exams.
          </p>
          <p>Prepare for WAEC and JAMB by utilizing our courses</p>
          <BlogLink to="/elearn" className="w-fit whitespace-nowrap">
            Visit&nbsp;our&nbsp;e-learning&nbsp;portal
          </BlogLink>
        </div>
      </div>
    </div>
  );
}
const BlogLink = styled(Link)`
  display: inline-block;
  /* // padding: 10px 20px; */
  background-color: #3498db;
  /* // color: #fff; */
  text-decoration: none;
  border-radius: 5px;
  /* // font-weight: bold; */
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #297fb8;
  }

  ${tw`px-4 py-2 rounded-md text-white font-bold text-xs sm:text-sm`}
`;
