/* PDF Viewer Styles - Fix Text Layer Duplication */

/* Make text layer functional for selection while visually hidden */
.pdf-page-wrapper .react-pdf__Page__textContent {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  opacity: 0.01 !important; /* Very low opacity but not 0 for selection */
  pointer-events: auto !important;
  z-index: 2 !important;
  color: rgba(0, 0, 0, 0.01) !important; /* Nearly transparent but selectable */
  background: transparent !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
}

/* Ensure the canvas (visual PDF) is properly positioned */
.pdf-page-wrapper .react-pdf__Page__canvas {
  position: relative !important;
  z-index: 1 !important;
  display: block !important;
}

/* Make sure the page container has proper positioning */
.pdf-page-wrapper {
  position: relative !important;
  display: inline-block !important;
}

/* Make text spans selectable while nearly invisible */
.pdf-page-wrapper .react-pdf__Page__textContent span {
  color: rgba(0, 0, 0, 0.01) !important; /* Nearly transparent but selectable */
  background: transparent !important;
  border: none !important;
  outline: none !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  cursor: text !important;
}

/* Remove extra spacing and improve PDF layout */
.pdf-page-container {
  margin: 0 !important;
  padding: 0 !important;
}

.pdf-page-wrapper {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 0 !important;
}

/* Custom scrollbar styles for PDF container */
.pdf-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #ef4444 #f1f5f9;
  margin: 0 !important;
}

.pdf-scroll-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.pdf-scroll-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.pdf-scroll-container::-webkit-scrollbar-thumb {
  background: #ef4444;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.pdf-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #dc2626;
}

/* Remove any extra spacing from react-pdf components */
.react-pdf__Document {
  margin: 0 !important;
  padding: 0 !important;
}

.react-pdf__Page {
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
}

/* Floating navigation styles */
.floating-nav {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Mobile navigation adjustments */
@media (max-width: 640px) {
  /* Ensure navigation is visible above mobile browser UI and nav bars */
  .fixed.bottom-16 {
    bottom: max(4rem, calc(4rem + env(safe-area-inset-bottom, 0))) !important;
  }

  /* Adjust for mobile viewport */
  .floating-nav {
    min-height: 44px; /* iOS minimum touch target */
  }
}

/* Ensure navigation doesn't interfere with mobile gestures */
@media (max-width: 768px) {
  .fixed.bottom-16 {
    /* Add extra space for mobile browser navigation bars */
    bottom: max(5rem, calc(4rem + env(safe-area-inset-bottom, 1rem))) !important;
  }
}

/* Scroll hint animation */
@keyframes scrollHint {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(2px); }
}

.scroll-hint {
  animation: scrollHint 2s ease-in-out infinite;
  display: inline-block;
}

/* Ensure proper text selection works */
.pdf-page-wrapper .react-pdf__Page__textContent::selection {
  background: rgba(59, 130, 246, 0.3) !important;
}

.pdf-page-wrapper .react-pdf__Page__textContent span::selection {
  background: rgba(59, 130, 246, 0.3) !important;
}

/* Loading and error states */
.pdf-page-container .react-pdf__message {
  padding: 2rem;
  text-align: center;
  color: #6b7280;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pdf-scroll-container {
    max-height: 70vh !important;
  }
  
  .pdf-scroll-container::-webkit-scrollbar {
    width: 6px;
  }
}

/* High DPI display adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .pdf-page-wrapper .react-pdf__Page__canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Focus styles for accessibility */
.pdf-page-wrapper:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Print styles */
@media print {
  .pdf-scroll-container {
    max-height: none !important;
    overflow: visible !important;
  }
  
  .pdf-page-wrapper .react-pdf__Page__textContent {
    opacity: 1 !important;
    color: black !important;
  }
}
