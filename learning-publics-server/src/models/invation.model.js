const { Schema, model } = require("mongoose");
const { CONSTANTS } = require("../config");

const InvitationSchema = new Schema({
  token: {
    type: String,
    index: true, 
  },
  email: {
    type: String,
    required: true,
  },
  admin: {
    type: Schema.Types.ObjectId,
    ref: "Account",
    required: true,
  },
  role:{
    type: String,
    required: true,
    enum: CONSTANTS.ACCOUNT_TYPE,
  }
}, 
{ timestamps: true }
);
const InvitationModel = model("Invitation", InvitationSchema);
module.exports = InvitationModel;