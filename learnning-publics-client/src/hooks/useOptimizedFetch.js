import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'react-hot-toast';
import jmsApp from 'api/jms';
import { handleRefreshToken } from 'utils/auth';

/**
 * Optimized data fetching hook with caching, error handling, and retry logic
 * Designed for Learning Publics Journal application
 */
export const useOptimizedFetch = (endpoint, options = {}) => {
  const {
    params = {},
    dependencies = [],
    enableCache = true,
    cacheTime = 5 * 60 * 1000, // 5 minutes default
    retryAttempts = 3,
    retryDelay = 1000,
    onSuccess,
    onError,
    transform,
    immediate = true
  } = options;

  const [data, setData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  
  const abortControllerRef = useRef(null);
  const cacheRef = useRef(new Map());
  const lastFetchRef = useRef(null);

  // Generate cache key from endpoint and params
  const getCacheKey = useCallback(() => {
    return `${endpoint}-${JSON.stringify(params)}`;
  }, [endpoint, params]);

  // Check if cached data is still valid
  const getCachedData = useCallback(() => {
    if (!enableCache) return null;
    
    const cacheKey = getCacheKey();
    const cached = cacheRef.current.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < cacheTime) {
      return cached.data;
    }
    
    return null;
  }, [enableCache, getCacheKey, cacheTime]);

  // Store data in cache
  const setCachedData = useCallback((newData) => {
    if (!enableCache) return;
    
    const cacheKey = getCacheKey();
    cacheRef.current.set(cacheKey, {
      data: newData,
      timestamp: Date.now()
    });
  }, [enableCache, getCacheKey]);

  // Fetch data with retry logic and error handling
  const fetchData = useCallback(async (customParams = {}) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Check cache first
    const cachedData = getCachedData();
    if (cachedData) {
      setData(cachedData);
      setError(null);
      if (onSuccess) onSuccess(cachedData);
      return cachedData;
    }

    setIsLoading(true);
    setError(null);
    
    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    const finalParams = { ...params, ...customParams };
    let currentRetry = 0;

    const attemptFetch = async () => {
      try {
        const response = await jmsApp.get(endpoint, {
          params: finalParams,
          signal: abortControllerRef.current.signal
        });

        let responseData = response.data;
        
        // Apply data transformation if provided
        if (transform) {
          responseData = transform(responseData);
        }

        setData(responseData);
        setError(null);
        setRetryCount(0);
        setCachedData(responseData);
        
        if (onSuccess) onSuccess(responseData);
        return responseData;

      } catch (err) {
        // Handle abort
        if (err.name === 'AbortError') {
          return;
        }

        // Handle 401 errors with token refresh
        if (err.response?.status === 401) {
          try {
            await handleRefreshToken();
            // Retry the request after token refresh
            return attemptFetch();
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
          }
        }

        // Retry logic for network errors
        if (currentRetry < retryAttempts && 
            (err.code === 'NETWORK_ERROR' || err.response?.status >= 500)) {
          currentRetry++;
          setRetryCount(currentRetry);
          
          await new Promise(resolve => setTimeout(resolve, retryDelay * currentRetry));
          return attemptFetch();
        }

        // Final error handling
        const errorMessage = err.response?.data?.error || 
                           err.response?.data?.message || 
                           err.message || 
                           'An unexpected error occurred';
        
        setError({
          message: errorMessage,
          status: err.response?.status,
          code: err.code,
          retryCount: currentRetry
        });

        if (onError) {
          onError(err);
        } else {
          toast.error(errorMessage);
        }

        throw err;
      }
    };

    try {
      const result = await attemptFetch();
      return result;
    } finally {
      setIsLoading(false);
      lastFetchRef.current = Date.now();
    }
  }, [
    endpoint, 
    params, 
    retryAttempts, 
    retryDelay, 
    onSuccess, 
    onError, 
    transform,
    getCachedData,
    setCachedData
  ]);

  // Manual retry function
  const retry = useCallback(() => {
    return fetchData();
  }, [fetchData]);

  // Refresh function that bypasses cache
  const refresh = useCallback((customParams = {}) => {
    // Clear cache for this endpoint
    const cacheKey = getCacheKey();
    cacheRef.current.delete(cacheKey);
    
    return fetchData(customParams);
  }, [fetchData, getCacheKey]);

  // Clear all cache
  const clearCache = useCallback(() => {
    cacheRef.current.clear();
  }, []);

  // Effect to fetch data on mount and dependency changes
  useEffect(() => {
    if (immediate && endpoint) {
      fetchData();
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [immediate, endpoint, ...dependencies]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    data,
    isLoading,
    error,
    retryCount,
    fetchData,
    retry,
    refresh,
    clearCache,
    lastFetch: lastFetchRef.current
  };
};

/**
 * Specialized hook for article fetching with Learning Publics specific optimizations
 */
export const useArticleFetch = (articleId, options = {}) => {
  return useOptimizedFetch(
    articleId ? `/api/v1/author/articles/${articleId}` : null,
    {
      ...options,
      transform: (data) => {
        // Learning Publics specific data transformation
        if (data.article) {
          return {
            ...data.article,
            // Ensure consistent date formatting
            createdAt: new Date(data.article.createdAt).toISOString(),
            updatedAt: new Date(data.article.updatedAt).toISOString(),
            // Add computed fields
            isRecent: Date.now() - new Date(data.article.createdAt).getTime() < 7 * 24 * 60 * 60 * 1000
          };
        }
        return data;
      }
    }
  );
};

/**
 * Specialized hook for articles list with pagination and filtering
 */
export const useArticlesList = (filters = {}, options = {}) => {
  const {
    category,
    search,
    author,
    country,
    page = 1,
    limit = 10
  } = filters;

  return useOptimizedFetch(
    '/api/v1/author/articles',
    {
      ...options,
      params: {
        category,
        search,
        author,
        country,
        page,
        limit
      },
      dependencies: [category, search, author, country, page, limit],
      transform: (data) => {
        if (data.articles) {
          return {
            ...data,
            articles: data.articles.map(article => ({
              ...article,
              createdAt: new Date(article.createdAt).toISOString(),
              updatedAt: new Date(article.updatedAt).toISOString(),
              isRecent: Date.now() - new Date(article.createdAt).getTime() < 7 * 24 * 60 * 60 * 1000
            }))
          };
        }
        return data;
      }
    }
  );
};
