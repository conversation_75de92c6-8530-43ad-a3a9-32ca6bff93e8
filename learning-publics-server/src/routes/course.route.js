const express = require("express");
const courseRoute = express.Router();
const Controller = require("../controllers");
const { adminRequired, userRequired } = require("../middlewares/auth.middleware");

courseRoute.post("/", adminRequired, Controller.CourseController.registerCourse).get("/", admin<PERSON>equired, Controller.CourseController.course).patch("/", admin<PERSON>equired, Controller.CourseController.updateCourse);
courseRoute.get("/:id", userRequired, Controller.CourseController.courseById).delete("/", adminRequired, Controller.CourseController.courseDelete);
courseRoute.get("/not/used", adminRequired, Controller.CourseController.courseNotInPlan);
courseRoute.patch("/completed", userRequired, Controller.CourseController.completeCourse);
module.exports = {
  courseRoute,
};