import React from "react";
import styled from "styled-components";
import Colors from "utils/colors";

const StyledUl = styled.ul`
  list-style-type: none;
  border-radius: 7px;
  overflow: hidden;
  display: block;
  width: 100px;
  text-align: center;
  position: absolute;
  top: 0px;
  
`;

const Dropbtn = styled.div`
  display: inline-block;
  color: white;
  border-radius: 7px;
  text-align: center;
  padding: 6px 8px;
  text-transform: capitalize;
  text-decoration: none;
  background: ${(props) => {
    return props.children === "total"
      ? `${Colors.primary}`
      : props.children === "pending"
      ? `${Colors.black}`
      : props.children === "accepted"
      ? "orange"
      : props.children === "rejected"
      ? "red"
      : props.children === "review"
      ? "gray"
      : props.children === "published"
      ? "green"
      : "blue";
  }};
`;

const DropDownContent = styled.div`
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 100px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 10000;
  cursor: pointer;
  top: 0px;
`;

const DropDownLi = styled.li`
  display: block;
  position: relative;
  border-radius: 7px;
  backgroud: red;
  &:hover ${DropDownContent} {
    display: block;
    position: relative;
  }
`;

const SubA = styled.a`
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  text-align: left;
  &:hover {
    background-color: #f1f1f1;
  }
`;

const DropDownBtn = ({ props, handleClick, options }) => {
  return (
    <StyledUl>
      <DropDownLi>
        <Dropbtn>{props}</Dropbtn>
        <DropDownContent>
          {options.map((e, i) => (
            <div key={i}>
              <SubA onClick={() => handleClick(e)}>{e}</SubA>
            </div>
          ))}
        </DropDownContent>
      </DropDownLi>
    </StyledUl>
  );
};

export default DropDownBtn;
