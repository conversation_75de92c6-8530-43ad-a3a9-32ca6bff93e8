const { default: mongoose } = require("mongoose");
const { CONSTANTS } = require("../config");
const AccountModel = require("../models/account.model");
const RecoveryLinkModel = require("../models/recovery.model");
const TemporalAccountModel = require("../models/temporal.model");
const UserPlanModel = require("../models/user.plan.model");
const { ACTIONS } = require("../utils/actions");

exports.register = async (details) => {
  try {
    const account = await AccountModel.create({...details})
    account.refreshToken = [];
    account.save();
    if (!account) return {error: "Account registration failed, try again"};
    return account;
  } catch (error) { 
      await AccountModel.findOneAndDelete({email:details.email}).exec();
    return {error};
  }
};
exports.registerTempAccount = async (details) => {
  try {
    const save = await TemporalAccountModel.create({...details});
    if (!save)
      return {error: "Account registration failed, try again"};
    return save;
  } catch (error) {
    return {error};
  }
};
exports.temporalUser = async (id, token, otp) => {
  try {
    const temp = await TemporalAccountModel.findOne({_id:id, otp});
    if (!temp) return temp;
    let exist = temp.refreshToken.find((item) => item === token);
    if (exist) {
      return exist = temp;
    }
    return temp;
  } catch (error) {
    return {error};
  }
};
exports.updateTempToken = async (info) => {
  try {
    const temp = await TemporalAccountModel.findByIdAndUpdate({_id:info.id}, {otp:info.otp});
    if (!temp) return {error: "Token does not exist"};
    temp.refreshToken.push(info.refreshToken);
    temp.save();
    return temp;
  } catch (error) {
    return {error};
  }
};
exports.deleteTempToken = async (info) => {
  try {
    const temp = await TemporalAccountModel.findByIdAndUpdate({_id:info.id});
    if (!temp) return {error: "Token does not exist"};
    const exist = temp.refreshToken.filter((item)=>item !== info.refreshToken);
    temp.refreshToken = [...exist];
    temp.save();
    return temp;
  } catch (error) {
    return {error};
  }
};
exports.updateTempOtp = async (id, info) => {
  try {
    const exist = await TemporalAccountModel.findOneAndUpdate({_id: id}, {otp:info.otp}, {returnOriginal:false});
    if (!exist) return {error: "Token does not exist"};
    exist.refreshToken = [info.refreshToken];
    exist.save();
    return exist;
  } catch (error) {
    return {error};
  }
};
exports.removeTempAccount = async (id) => {
  try {
    return await TemporalAccountModel.findOneAndDelete({_id:id});
  } catch (error) {
    return {error};
  }
};
exports.defaultRegistration = async (details) => {
  try {
    const check = await AccountModel.findOne({type:CONSTANTS.ACCOUNT_TYPE[2]});
    if (check)
      return {error: "Super Account already exist"};
    return await AccountModel.create({...details});
  } catch (error) {
    return {error};
  }
};
exports.create = async (details) => {
  try {
    return await AccountModel.create({...details});
  } catch (error) {
    return {error};
  }
};
exports.checkEmail = async (email) => {
  try {
    let exist = await TemporalAccountModel.findOne({email});
    if (exist) {
      exist.register = ACTIONS.INCOMPLETE_REG;
      return exist;
    }
    exist = await AccountModel.findOne({email});
    return exist;
  } catch (error) {
    return {error};
  }
};
exports.checkUsername = async (username) => {
  try {
    let exist = await TemporalAccountModel.findOne({username});
    if (exist) {
      exist.register = ACTIONS.INCOMPLETE_REG;
      return exist;
    } else {
      exist = await AccountModel.findOne({username});
      return exist;
    } 
  } catch (error) {
    return {error};
  }
};
exports.checkById = async (id) => {
  try {
    const temp = await TemporalAccountModel.findOne({_id:id});
    if (temp) {
      return temp;
    } else {
      return await AccountModel.findOne({_id:id});
    }
  } catch (error) {
    return {error};
  }
};
exports.checkByIdAndEmail = async (id, email) => {
  try {
    return await AccountModel.findOne({_id:id, email});
  } catch (error) {
    return {error};
  }
};

exports.logOut = async (id) => {
  try {
    const logout = await AccountModel.findByIdAndUpdate({_id:id});
    logout.refreshToken = [];
    logout.save();
    return logout;
  } catch (error) {
    return {error};
  }
};
exports.updateToken = async (id, refreshToken, token) => {
  try {
    const userInfor = await AccountModel.findByIdAndUpdate({_id:id});
    const existingToken = userInfor.refreshToken.filter(rt => rt !== token);
    userInfor.refreshToken = [...existingToken, refreshToken];
    userInfor.save();
    return userInfor;
  } catch (error) {
    return {error};
  }
};
exports.upatePassword = async (id, password) => {
  try {
    return await AccountModel.findByIdAndUpdate({_id:id}, {password});
  } catch (error) {
    return {error};
  }
};
exports.userAccounts = async () => {
  try {
    return await AccountModel.find();
  } catch (error) {
    return {error};
  }
};
exports.removeAccount = async (email) => {
  try {
    return await AccountModel.findOneAndDelete({email}).exec();
  } catch (error) {
    return {error};
  }
};
exports.passwordRecoveryInfor = async (infor) =>{
  try {
    await RecoveryLinkModel.deleteOne({user:infor.userId});
    return await RecoveryLinkModel.create({...infor});
  } catch (error) {
    return {error};
  }
};
exports.getRecoveryInfor = async (id) =>{
  try {
    return await RecoveryLinkModel.findOne({uniqueString:id});
  } catch (error) {
    return {error};
  }
};
exports.deleteRecoveryInfor = async (id) =>{
  try {
    return await RecoveryLinkModel.findOneAndDelete({uniqueString:id});
  } catch (error) {
    return {error};
  }
};
exports.resetPassword = async (id, password) =>{
  try {
    const user = await RecoveryLinkModel.findOne({uniqueString:id});
    if (!user) return {error: "Password reset fail, try again"};
    return await AccountModel.findOneAndUpdate({_id:user.user}, {password});
  } catch (error) {
    return {error};
  }
};
exports.updateAccount = async (id, details) => {
  try {
    return await AccountModel.findByIdAndUpdate({_id:id}, {...details}, {returnOriginal: false});
  } catch (error) {
    return {error};
  }
};

exports.getAdmin = async () =>{
  try {
    return await AccountModel.find({type:{$ne: CONSTANTS.ACCOUNT_TYPE[0]}}).exec();
  } catch (error) {
    return {error};
  }
};
exports.adminByRole = async (role) =>{
  try {
    return await AccountModel.find({type: {$regex: new RegExp(role, 'i')}}).exec();
  } catch (error) {
    return {error};
  }
};

exports.auditors = async (auditor) =>{
  try{
      const editorInfo = [];
      auditor.forEach(el=>{
        editorInfo.push(mongoose.Types.ObjectId(el));
      });
      return await AccountModel.find({_id:[...editorInfo]},"-__v -createdAt -updatedAt -password -refreshToken "); 
  }catch(error){
      throw new Error(error)
  }
}
