// Paystack Configuration for Learning Publics Journal
export const PAYSTACK_CONFIG = {
  // Use test key for development, production key for live
  publicKey: process.env.REACT_APP_PAYSTACK_PUBLIC_KEY || 'pk_test_your_test_key_here',

  // Payment types for the journal platform (mapped to backend service types)
  PAYMENT_TYPES: {
    ARTICLE_SUBMISSION: 'article_submission',
    EXPEDITED_REVIEW: 'expedited_review',
    OPEN_ACCESS: 'open_access_fee',
    REPRINTS: 'reprints',
    ADDITIONAL_REVIEW: 'additional_review',
    PRIORITY_PROCESSING: 'priority_processing',
    SUPPORT_DONATION: 'support_donation',
    SUBSCRIPTION: 'subscription'
  },

  // Default fallback amounts (will be overridden by dynamic pricing)
  FALLBACK_FEES: {
    ARTICLE_SUBMISSION: 0, // Currently free by default
    EXPEDITED_REVIEW: 50, // $50 USD
    OPEN_ACCESS: 25, // $25 USD
    REPRINTS: 10, // $10 USD
    ADDITIONAL_REVIEW: 30, // $30 USD
    PRIORITY_PROCESSING: 40, // $40 USD
  },

  // Currency
  CURRENCY: 'NGN',

  // Payment channels
  CHANNELS: ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer'],

  // Metadata for tracking
  METADATA_KEYS: {
    PAYMENT_TYPE: 'payment_type',
    USER_ID: 'user_id',
    ARTICLE_ID: 'article_id',
    SUPPORT_TIER: 'support_tier',
    JOURNAL_SECTION: 'journal_section'
  }
};

// Helper function to convert amount to kobo
export const toKobo = (nairaAmount) => {
  return Math.round(nairaAmount * 100);
};

// Helper function to convert kobo to naira
export const toNaira = (koboAmount) => {
  return koboAmount / 100;
};

// Format currency for display
export const formatCurrency = (amount, currency = 'NGN') => {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

// Payment reference generator
export const generatePaymentReference = (type, userId = null) => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const userPart = userId ? `_${userId.substring(0, 6)}` : '';
  return `LP_${type.toUpperCase()}_${timestamp}_${random}${userPart}`;
};

// Validate payment configuration
export const validatePaystackConfig = () => {
  if (!PAYSTACK_CONFIG.publicKey || PAYSTACK_CONFIG.publicKey === 'pk_test_your_test_key_here') {
    console.warn('Paystack public key not configured. Please set REACT_APP_PAYSTACK_PUBLIC_KEY in your environment variables.');
    return false;
  }
  return true;
};

export default PAYSTACK_CONFIG;
