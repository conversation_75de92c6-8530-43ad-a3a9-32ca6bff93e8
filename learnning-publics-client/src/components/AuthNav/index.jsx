import {
  AuthNav<PERSON><PERSON><PERSON>,
  Auth<PERSON><PERSON><PERSON><PERSON>,
  AuthNavLogoMain,
  AuthNavRight,
  AuthNavText,
  AuthUserIcon
} from "./style";
import { DASHBOARD_ROUTE, LOGIN_ROUTE } from "routes";
import { Link, Outlet, useNavigate } from "react-router-dom";
import React, { useEffect, useState } from "react";

import { AppLogo } from "utils/assets";
import AuthSideNav from "./SideBar";
import { Img } from "globalStyles";
import authStore from "mobx/AuthStore";
import { observer } from "mobx-react-lite";

const AuthNav = () => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  function toggleAuthNav() {
    setIsOpen(!isOpen);
  }

  useEffect(() => {
    const status = authStore.loggedInStatus();

    if (!status) {
      navigate(`${LOGIN_ROUTE}`);
    }
    window.scrollTo(0, 0);
  }, [navigate]);
  return (
    <>
      {isOpen && (
        <AuthSideNav
          isOpen={isOpen}
          // user={userProfile}
          onClick={() => toggleAuthNav()}
        />
      )}
      <AuthNavContainer>
        <AuthNavLogoMain>
          <Link className="home-link" to={DASHBOARD_ROUTE}>
            <Img src={AppLogo} alt="LPJ" />
          </Link>
        </AuthNavLogoMain>
        <AuthNavLogo onClick={() => toggleAuthNav()}>
          <Img src={AppLogo} alt="LPJ" />
        </AuthNavLogo>
        <AuthNavRight>
          <AuthNavText>
            <p className="student__name">Welcome Prince</p>
            <span className="student__category">Student</span>
          </AuthNavText>
          <AuthUserIcon>
            <Img src={AppLogo} alt="lpj" />
          </AuthUserIcon>
        </AuthNavRight>
      </AuthNavContainer>
      <Outlet />
    </>
  );
};

export default observer(AuthNav);
