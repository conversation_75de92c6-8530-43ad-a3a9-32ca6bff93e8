import {
  ABOUT_ROUTE,
  BLOG_ROUTE,
  ELEARN_ROUTE,
  HOME_ROUTE,
  JOURNAL_ROUTE,
  SUBMIT_ROUTE,
  SUPPORT_ROUTE,
} from 'routes'
import { AppLink, Img } from 'globalStyles'
import {
  AppLogo,
  FBicon,
  IGicon,
  LinkedInIcon,
  LocationIcon,
  TwitterIcon,
  email,
  phone,
} from 'utils/assets'
import { FooterUl, SocialDivFooter } from './style'

import Colors from 'utils/colors'
import FooterItem from './FooterItem'
import React from 'react'
import Socials from './Socials'
import media from 'utils/media'
import styled from 'styled-components'
import tw from 'twin.macro'
import { motion } from 'framer-motion'

// Professional Footer Styles
const FooterContainer = styled.footer`
  background: linear-gradient(135deg, ${Colors.primary} 0%, #5a6a72 50%, #6d7d85 100%);
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  }
`

const FooterContent = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 4rem 2rem 2rem;

  ${media.mobile`
    padding: 3rem 1rem 2rem;
  `}
`

const FooterGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;

  ${media.tablet`
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  `}

  ${media.mobile`
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  `}
`

const FooterSection = styled.div`
  display: flex;
  flex-direction: column;
`

const SectionTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #f8fafc;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 2rem;
    height: 2px;
    background: ${Colors.secondary};

    ${media.mobile`
      left: 50%;
      transform: translateX(-50%);
    `}
  }
`

const FooterLink = styled(AppLink)`
  color: #cbd5e1;
  text-decoration: none;
  padding: 0.5rem 0;
  transition: all 0.3s ease;
  font-size: 0.95rem;

  &:hover {
    color: ${Colors.secondary};
    transform: translateX(0.25rem);
  }
`

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  color: #cbd5e1;

  img {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    filter: brightness(0) invert(1);
  }

  ${media.mobile`
    justify-content: center;
  `}
`

const SocialLinks = styled.div`
  display: flex;
  gap: 1rem;
  margin-top: 1rem;

  ${media.mobile`
    justify-content: center;
  `}
`

const SocialLink = styled.a`
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background: ${Colors.secondary};
    transform: translateY(-2px);
  }

  img {
    width: 1.25rem;
    height: 1.25rem;
    filter: brightness(0) invert(1);
  }
`

const JournalInfo = styled.div`
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 0.75rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
`

const JournalTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #f8fafc;
`

const JournalDescription = styled.p`
  color: #cbd5e1;
  line-height: 1.6;
  margin-bottom: 1rem;
`

const JournalMeta = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.875rem;
  color: #94a3b8;

  ${media.mobile`
    justify-content: center;
  `}
`

const MetaItem = styled.span`
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
`

const FooterBottom = styled.div`
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;

  ${media.mobile`
    flex-direction: column;
    text-align: center;
  `}
`

const Copyright = styled.p`
  color: #94a3b8;
  font-size: 0.875rem;
`

const LegalLinks = styled.div`
  display: flex;
  gap: 2rem;

  ${media.mobile`
    gap: 1rem;
  `}
`

const LegalLink = styled(AppLink)`
  color: #94a3b8;
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;

  &:hover {
    color: ${Colors.secondary};
  }
`

function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <FooterContainer>
      <FooterContent>
        {/* Journal Information Section */}
        <JournalInfo>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
            <Img src={AppLogo} alt="Learning Publics Logo" style={{ width: '3rem', height: '3rem', marginRight: '1rem' }} />
            <JournalTitle>Learning Publics Journal</JournalTitle>
          </div>
          <JournalDescription>
            A premier academic journal dedicated to advancing research in agriculture and environmental studies.
            We publish high-quality, peer-reviewed articles that contribute to sustainable development and environmental conservation.
          </JournalDescription>
          <JournalMeta>
            <MetaItem>ISSN: 2026-5654</MetaItem>
            <MetaItem>Open Access</MetaItem>
            <MetaItem>Peer Reviewed</MetaItem>
            <MetaItem>International</MetaItem>
          </JournalMeta>
        </JournalInfo>

        {/* Main Footer Grid */}
        <FooterGrid>
          {/* Navigation Links */}
          <FooterSection>
            <SectionTitle>Quick Links</SectionTitle>
            <FooterLink to={HOME_ROUTE}>Home</FooterLink>
            <FooterLink to="/articles">Browse Articles</FooterLink>
            <FooterLink to={JOURNAL_ROUTE}>Explore Journals</FooterLink>
            <FooterLink to={SUBMIT_ROUTE}>Submit Article</FooterLink>
            <FooterLink to="/search">Advanced Search</FooterLink>
            <FooterLink to={ELEARN_ROUTE}>E-Learning Portal</FooterLink>
          </FooterSection>

          {/* For Authors */}
          <FooterSection>
            <SectionTitle>For Authors</SectionTitle>
            <FooterLink to={SUBMIT_ROUTE}>Submit Manuscript</FooterLink>
            <FooterLink to="/guidelines">Author Guidelines</FooterLink>
            <FooterLink to="/peer-review">Peer Review Process</FooterLink>
            <FooterLink to="/publication-ethics">Publication Ethics</FooterLink>
            <FooterLink to="/open-access">Open Access Policy</FooterLink>
          </FooterSection>

          {/* For Readers */}
          <FooterSection>
            <SectionTitle>For Readers</SectionTitle>
            <FooterLink to="/current-issue">Current Issue</FooterLink>
            <FooterLink to="/archives">Archives</FooterLink>
            <FooterLink to="/rss.xml">RSS Feeds</FooterLink>
            <FooterLink to="/alerts">Email Alerts</FooterLink>
            <FooterLink to="/mobile-app">Mobile App</FooterLink>
          </FooterSection>

          {/* About */}
          <FooterSection>
            <SectionTitle>About</SectionTitle>
            <FooterLink to={ABOUT_ROUTE}>About Us</FooterLink>
            <FooterLink to="/editorial-board">Editorial Board</FooterLink>
            <FooterLink to="/aims-scope">Aims & Scope</FooterLink>
            <FooterLink to="/indexing">Indexing</FooterLink>
            <FooterLink to={SUPPORT_ROUTE}>Contact Support</FooterLink>
          </FooterSection>

          {/* Contact & Social */}
          <FooterSection>
            <SectionTitle>Contact Us</SectionTitle>
            <ContactItem>
              <img src={email} alt="Email" />
              <span><EMAIL></span>
            </ContactItem>
            <ContactItem>
              <img src={phone} alt="Phone" />
              <span>+****************</span>
            </ContactItem>
            <ContactItem>
              <img src={LocationIcon} alt="Location" />
              <span>8126 Birch Bay Drive Unit 101<br />Blaine WA 98230 USA</span>
            </ContactItem>

            <SocialLinks>
              <SocialLink href="https://twitter.com/learningpublics" target="_blank" rel="noopener noreferrer">
                <img src={TwitterIcon} alt="Twitter" />
              </SocialLink>
              <SocialLink href="https://facebook.com/learningpublics" target="_blank" rel="noopener noreferrer">
                <img src={FBicon} alt="Facebook" />
              </SocialLink>
              <SocialLink href="https://linkedin.com/company/learningpublics" target="_blank" rel="noopener noreferrer">
                <img src={LinkedInIcon} alt="LinkedIn" />
              </SocialLink>
              <SocialLink href="https://instagram.com/learningpublics" target="_blank" rel="noopener noreferrer">
                <img src={IGicon} alt="Instagram" />
              </SocialLink>
            </SocialLinks>
          </FooterSection>
        </FooterGrid>

        {/* Footer Bottom */}
        <FooterBottom>
          <Copyright>
            © {currentYear} Learning Publics Journal. All rights reserved.
          </Copyright>
          <LegalLinks>
            <LegalLink to="/privacy-policy">Privacy Policy</LegalLink>
            <LegalLink to="/terms-of-service">Terms of Service</LegalLink>
            <LegalLink to="/cookie-policy">Cookie Policy</LegalLink>
            <LegalLink to="/accessibility">Accessibility</LegalLink>
          </LegalLinks>
        </FooterBottom>
      </FooterContent>
    </FooterContainer>
  )
}

export default Footer
