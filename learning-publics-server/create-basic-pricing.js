const mongoose = require('mongoose');
require('dotenv').config();

// Simple pricing config schema (without all the complex fields)
const basicPricingSchema = new mongoose.Schema({
  serviceType: { type: String, required: true, unique: true },
  serviceName: { type: String, required: true },
  serviceDescription: { type: String, required: true },
  currentPrice: {
    amount: { type: Number, required: true },
    currency: { type: String, default: 'USD' }
  },
  isActive: { type: Boolean, default: true },
  isFree: { type: Boolean, default: false },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const BasicPricing = mongoose.model('PricingConfig', basicPricingSchema);

const basicPricingData = [
  {
    serviceType: 'article_submission',
    serviceName: 'Article Submission',
    serviceDescription: 'Standard article submission and initial review process. Currently free for all authors.',
    currentPrice: { amount: 0, currency: 'USD' },
    isActive: true,
    isFree: true
  },
  {
    serviceType: 'expedited_review',
    serviceName: 'Expedited Peer Review',
    serviceDescription: 'Fast-track peer review process with guaranteed response within 2 weeks.',
    currentPrice: { amount: 50, currency: 'USD' },
    isActive: true,
    isFree: false
  },
  {
    serviceType: 'open_access_fee',
    serviceName: 'Open Access Publication',
    serviceDescription: 'Make your article freely available to all readers worldwide.',
    currentPrice: { amount: 25, currency: 'USD' },
    isActive: true,
    isFree: false
  },
  {
    serviceType: 'reprints',
    serviceName: 'Article Reprints',
    serviceDescription: 'High-quality printed copies of your published article.',
    currentPrice: { amount: 15, currency: 'USD' },
    isActive: true,
    isFree: false
  }
];

async function createBasicPricing() {
  try {
    console.log('🔌 Connecting to database...');
    const dbUrl = process.env.DB_URI || process.env.DB_URL || 'mongodb://localhost:27017/learning-publics';
    console.log('Using DB:', dbUrl.includes('mongodb+srv') ? 'Cloud MongoDB' : 'Local MongoDB');
    await mongoose.connect(dbUrl, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ Connected to database');

    // Check if pricing data already exists
    const existingCount = await BasicPricing.countDocuments();
    if (existingCount > 0) {
      console.log(`⚠️  Found ${existingCount} existing pricing configs. Skipping creation.`);
      console.log('💡 To recreate, delete existing configs first.');
      process.exit(0);
    }

    console.log('📝 Creating basic pricing configurations...');
    
    for (const pricingData of basicPricingData) {
      const config = new BasicPricing(pricingData);
      await config.save();
      console.log(`✅ Created: ${config.serviceName} - ${config.isFree ? 'FREE' : '$' + config.currentPrice.amount}`);
    }

    console.log(`🎉 Successfully created ${basicPricingData.length} pricing configurations!`);
    console.log('\n📊 Pricing Summary:');
    basicPricingData.forEach(config => {
      const price = config.isFree ? 'FREE' : `$${config.currentPrice.amount} ${config.currentPrice.currency}`;
      console.log(`   • ${config.serviceName}: ${price}`);
    });

    console.log('\n🔧 You can now test the pricing API:');
    console.log('   curl http://localhost:4001/api/v1/pricing/service/article_submission');

  } catch (error) {
    console.error('❌ Error creating pricing configurations:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
    process.exit(0);
  }
}

createBasicPricing();
