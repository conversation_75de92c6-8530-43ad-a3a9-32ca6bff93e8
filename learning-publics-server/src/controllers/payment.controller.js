const { APIError } = require('../utils/apiError');
const { CONSTANTS } = require('../config');
const logger = require('../logger');
const { PAYSTACK_SECRETE_KEY } = require('../config/env');
const https = require('https');
const crypto = require('crypto');

// Payment models (you may need to create these)
const PaymentRecord = require('../models/payment.model');
const SupportPayment = require('../models/supportPayment.model');

// Verify payment with Paystack
exports.verifyPayment = async (req, res, next) => {
  try {
    const { reference, paymentType, amount, currency, metadata } = req.body;

    if (!reference) {
      return next(APIError.badRequest('Payment reference is required'));
    }

    // Verify with Paystack
    const options = {
      hostname: 'api.paystack.co',
      port: 443,
      path: `/transaction/verify/${reference}`,
      method: 'GET',
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRETE_KEY}`
      }
    };

    const paystackReq = https.request(options, (paystackRes) => {
      let data = '';
      
      paystackRes.on('data', (chunk) => {
        data += chunk;
      });

      paystackRes.on('end', async () => {
        try {
          const response = JSON.parse(data);
          
          if (response.status && response.data.status === 'success') {
            // Payment verified successfully
            logger.info(`Payment verified successfully: ${reference}`);
            
            res.status(200).json({
              success: true,
              verified: true,
              data: response.data,
              message: 'Payment verified successfully'
            });
          } else {
            logger.error(`Payment verification failed: ${reference}`);
            res.status(400).json({
              success: false,
              verified: false,
              message: 'Payment verification failed'
            });
          }
        } catch (error) {
          logger.error('Payment verification error:', error);
          return next(APIError.internal('Payment verification failed'));
        }
      });
    });

    paystackReq.on('error', (error) => {
      logger.error('Paystack request error:', error);
      return next(APIError.internal('Payment verification request failed'));
    });

    paystackReq.end();

  } catch (error) {
    logger.error('Payment verification error:', error);
    return next(APIError.internal(error.message));
  }
};

// Create payment record
exports.createPaymentRecord = async (req, res, next) => {
  try {
    const { type, amount, currency, description, userId, articleId, supportTier, email } = req.body;

    const paymentRecord = new PaymentRecord({
      type,
      amount,
      currency,
      description,
      userId,
      articleId,
      supportTier,
      email,
      status: 'pending',
      createdAt: new Date()
    });

    await paymentRecord.save();

    res.status(201).json({
      success: true,
      paymentId: paymentRecord._id,
      message: 'Payment record created successfully'
    });

  } catch (error) {
    logger.error('Payment record creation error:', error);
    return next(APIError.internal(error.message));
  }
};

// Process support payment (donations)
exports.processSupportPayment = async (req, res, next) => {
  try {
    const { reference, supportTier, amount, currency, userEmail, userName, recurring } = req.body;

    if (!reference || !supportTier || !amount) {
      return next(APIError.badRequest('Missing required payment information'));
    }

    // Create support payment record
    const supportPayment = new SupportPayment({
      reference,
      supportTier,
      amount,
      currency,
      userEmail,
      userName,
      recurring,
      status: 'completed',
      paymentDate: new Date()
    });

    await supportPayment.save();

    logger.info(`Support payment processed: ${reference} - ${supportTier}`);

    res.status(200).json({
      success: true,
      data: {
        paymentId: supportPayment._id,
        reference,
        supportTier,
        amount,
        message: 'Thank you for your support!'
      }
    });

  } catch (error) {
    logger.error('Support payment processing error:', error);
    return next(APIError.internal(error.message));
  }
};

// Process journal payment (expedited review, open access, etc.)
exports.processJournalPayment = async (req, res, next) => {
  try {
    const { reference, paymentType, amount, currency, articleId, userEmail, userName } = req.body;

    if (!reference || !paymentType || !amount) {
      return next(APIError.badRequest('Missing required payment information'));
    }

    // Update payment record
    await PaymentRecord.findOneAndUpdate(
      { reference },
      { 
        status: 'completed',
        completedAt: new Date()
      }
    );

    logger.info(`Journal payment processed: ${reference} - ${paymentType}`);

    res.status(200).json({
      success: true,
      data: {
        reference,
        paymentType,
        amount,
        articleId,
        message: 'Payment processed successfully'
      }
    });

  } catch (error) {
    logger.error('Journal payment processing error:', error);
    return next(APIError.internal(error.message));
  }
};

// Process free submission
exports.processFreeSubmission = async (req, res, next) => {
  try {
    const { articleId, email, userName } = req.body;

    logger.info(`Free submission processed: ${articleId}`);

    res.status(200).json({
      success: true,
      data: {
        articleId,
        message: 'Free submission processed successfully'
      }
    });

  } catch (error) {
    logger.error('Free submission processing error:', error);
    return next(APIError.internal(error.message));
  }
};

// Get payment history
exports.getPaymentHistory = async (req, res, next) => {
  try {
    const userId = req.userId;
    const { page = 1, limit = 10 } = req.query;

    const payments = await PaymentRecord.find({ userId })
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await PaymentRecord.countDocuments({ userId });

    res.status(200).json({
      success: true,
      data: {
        payments,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    logger.error('Payment history error:', error);
    return next(APIError.internal(error.message));
  }
};

// Paystack webhook handler
exports.paystackWebhook = async (req, res, next) => {
  try {
    const hash = crypto.createHmac('sha512', PAYSTACK_SECRETE_KEY)
      .update(JSON.stringify(req.body))
      .digest('hex');

    if (hash === req.headers['x-paystack-signature']) {
      const event = req.body;
      
      if (event.event === 'charge.success') {
        const { reference, amount, customer } = event.data;
        
        // Update payment record
        await PaymentRecord.findOneAndUpdate(
          { reference },
          { 
            status: 'completed',
            completedAt: new Date(),
            paystackData: event.data
          }
        );

        logger.info(`Webhook payment confirmed: ${reference}`);
      }

      res.status(200).send('OK');
    } else {
      logger.warn('Invalid webhook signature');
      res.status(400).send('Invalid signature');
    }

  } catch (error) {
    logger.error('Webhook error:', error);
    res.status(500).send('Webhook error');
  }
};
