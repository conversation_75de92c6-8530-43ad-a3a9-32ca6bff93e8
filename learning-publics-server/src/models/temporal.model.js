const { Schema, model } = require("mongoose");
const { CONSTANTS } = require("../config");

const TemporatlAccountSchema = new Schema({
  name: {
    type: String,
    requied: [true, "Name is required"],
    trim: true,
  },
  username: {
    type: String,
    unique: [true, "Username is requred"],
    trim: true,
    index: true,
  },
  email: {
    type: String,
    requied: [true, "Email is required"],
    trim: true,
    index: true,
    unique: true,
  },
  password:{
    type: String,
    required: [true, "Password is required"],
    index: true,
  },
  phone: {
    type: String,
    trim: true,
  },
  status: {
    type: String,
    required: [true, "Account status is required"],
    enum: CONSTANTS.ACCOUNT_STATUS,
    default: CONSTANTS.ACCOUNT_STATUS[0],
  },
  refreshToken: {
    type: [String],  
    index: true,
  },
  type: {
    type: String,
    required: [true, "Account type is required"],
    enum: CONSTANTS.ACCOUNT_TYPE,
    index: true,
  },
  otp: {
    type: String,
    index: true,
    trim: true,
  },
},
{timestamps: true}
);

const TemporalAccountModel = model("TemporalAccount", TemporatlAccountSchema);

module.exports = TemporalAccountModel;