import jmsApp from 'api/jms';
import { PAYSTACK_CONFIG } from 'config/paystack';

// Payment API endpoints
const PAYMENT_ENDPOINTS = {
  VERIFY_PAYMENT: '/payment/verify',
  CREATE_PAYMENT: '/payment/create',
  GET_PAYMENT_HISTORY: '/payment/history',
  PROCESS_SUPPORT_PAYMENT: '/payment/support',
  PROCESS_JOURNAL_PAYMENT: '/payment/journal'
};

class PaymentService {
  /**
   * Verify payment with backend after Paystack success
   */
  static async verifyPayment(paymentReference, paymentData) {
    try {
      const response = await jmsApp.post(PAYMENT_ENDPOINTS.VERIFY_PAYMENT, {
        reference: paymentReference,
        paymentType: paymentData.type,
        amount: paymentData.amount,
        currency: PAYSTACK_CONFIG.CURRENCY,
        metadata: {
          userId: paymentData.userId,
          articleId: paymentData.articleId,
          supportTier: paymentData.supportTier,
          paymentDescription: paymentData.description
        }
      });

      return {
        success: true,
        data: response.data,
        verified: response.data.verified || false
      };
    } catch (error) {
      console.error('Payment verification error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Payment verification failed',
        verified: false
      };
    }
  }

  /**
   * Create payment record before processing
   */
  static async createPaymentRecord(paymentData) {
    try {
      const response = await jmsApp.post(PAYMENT_ENDPOINTS.CREATE_PAYMENT, {
        type: paymentData.type,
        amount: paymentData.amount,
        currency: PAYSTACK_CONFIG.CURRENCY,
        description: paymentData.description,
        userId: paymentData.userId,
        articleId: paymentData.articleId,
        supportTier: paymentData.supportTier,
        email: paymentData.email
      });

      return {
        success: true,
        data: response.data,
        paymentId: response.data.paymentId
      };
    } catch (error) {
      console.error('Payment creation error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to create payment record'
      };
    }
  }

  /**
   * Process support payment (donations/subscriptions)
   */
  static async processSupportPayment(paymentReference, supportData) {
    try {
      const response = await jmsApp.post(PAYMENT_ENDPOINTS.PROCESS_SUPPORT_PAYMENT, {
        reference: paymentReference,
        supportTier: supportData.supportTier,
        amount: supportData.amount,
        currency: PAYSTACK_CONFIG.CURRENCY,
        userEmail: supportData.email,
        userName: supportData.userName,
        recurring: supportData.recurring || false
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Support payment processing error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to process support payment'
      };
    }
  }

  /**
   * Process journal-related payment (expedited review, open access, etc.)
   */
  static async processJournalPayment(paymentReference, journalData) {
    try {
      const response = await jmsApp.post(PAYMENT_ENDPOINTS.PROCESS_JOURNAL_PAYMENT, {
        reference: paymentReference,
        paymentType: journalData.type,
        amount: journalData.amount,
        currency: PAYSTACK_CONFIG.CURRENCY,
        articleId: journalData.articleId,
        userEmail: journalData.email,
        userName: journalData.userName
      });

      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Journal payment processing error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to process journal payment'
      };
    }
  }

  /**
   * Get payment history for user
   */
  static async getPaymentHistory(userId, filters = {}) {
    try {
      const params = new URLSearchParams({
        userId,
        ...filters
      });

      const response = await jmsApp.get(`${PAYMENT_ENDPOINTS.GET_PAYMENT_HISTORY}?${params}`);

      return {
        success: true,
        data: response.data,
        payments: response.data.payments || []
      };
    } catch (error) {
      console.error('Payment history error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch payment history',
        payments: []
      };
    }
  }

  /**
   * Handle free submission (no payment required)
   */
  static async processFreeSubmission(submissionData) {
    try {
      // For free submissions, we still want to track them
      const response = await jmsApp.post('/articles/submit-free', {
        articleId: submissionData.articleId,
        userEmail: submissionData.email,
        userName: submissionData.userName,
        submissionType: 'free_article_submission'
      });

      return {
        success: true,
        data: response.data,
        reference: 'FREE_SUBMISSION'
      };
    } catch (error) {
      console.error('Free submission processing error:', error);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to process free submission'
      };
    }
  }

  /**
   * Calculate payment amount based on service type
   */
  static calculatePaymentAmount(serviceType, customAmount = null) {
    if (customAmount !== null) {
      return customAmount;
    }

    switch (serviceType) {
      case PAYSTACK_CONFIG.PAYMENT_TYPES.ARTICLE_PROCESSING:
        return PAYSTACK_CONFIG.JOURNAL_FEES.ARTICLE_PROCESSING;
      case PAYSTACK_CONFIG.PAYMENT_TYPES.EXPEDITED_REVIEW:
        return PAYSTACK_CONFIG.JOURNAL_FEES.EXPEDITED_REVIEW;
      case PAYSTACK_CONFIG.PAYMENT_TYPES.OPEN_ACCESS:
        return PAYSTACK_CONFIG.JOURNAL_FEES.OPEN_ACCESS;
      case PAYSTACK_CONFIG.PAYMENT_TYPES.REPRINTS:
        return PAYSTACK_CONFIG.JOURNAL_FEES.REPRINTS;
      case PAYSTACK_CONFIG.PAYMENT_TYPES.SUPPORT_DONATION:
        return customAmount || PAYSTACK_CONFIG.JOURNAL_FEES.SUPPORT_TIER_1;
      default:
        return 0;
    }
  }

  /**
   * Get service description for payment type
   */
  static getServiceDescription(serviceType, customDescription = null) {
    if (customDescription) {
      return customDescription;
    }

    switch (serviceType) {
      case PAYSTACK_CONFIG.PAYMENT_TYPES.ARTICLE_PROCESSING:
        return 'Article Processing Fee';
      case PAYSTACK_CONFIG.PAYMENT_TYPES.EXPEDITED_REVIEW:
        return 'Expedited Peer Review Service';
      case PAYSTACK_CONFIG.PAYMENT_TYPES.OPEN_ACCESS:
        return 'Open Access Publication Fee';
      case PAYSTACK_CONFIG.PAYMENT_TYPES.REPRINTS:
        return 'Article Reprints';
      case PAYSTACK_CONFIG.PAYMENT_TYPES.SUPPORT_DONATION:
        return 'Journal Support Donation';
      default:
        return 'Journal Service Payment';
    }
  }
}

export default PaymentService;
