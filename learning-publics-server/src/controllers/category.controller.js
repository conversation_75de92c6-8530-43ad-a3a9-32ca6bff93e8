/* eslint-disable camelcase */
const { CONSTANTS } = require("../config");
const logger = require("../logger");
const { requiredAdminRole } = require("../middlewares/auth.middleware");
const { createCategory, getCategory, categoryUpdate, categorydelete, findCategoryById } = require("../services");
const { META, ERROR_FIELD } = require("../utils/actions");
const { APIError } = require("../utils/apiError");
const { cloudinary, accessPath } = require("../utils/cloudinary");
const responsBuilder = require("../utils/responsBuilder");
exports.createCategory = async (req, res, next) => {
  try {
    if (requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error) return next(APIError.unauthorized());
    if (!req.userType) return next(APIError.unauthorized());
    const {name} = req.body;
    if (!name) return next(APIError.badRequest("Category name is required"));
    const details = {name:name.toLowerCase(), admin:req.userId};
    if (req.body.fileData) {
      const {fileData, format} = req.body;
      if (!format) return next(APIError.badRequest("Image format is required"));
      if (!CONSTANTS.CATEGORY_IMG_FORMAT.includes(format)) return next(APIError.badRequest("Invalid image format"));
      const uploadFile = await cloudinary.uploader.upload(fileData, {
        upload_preset: accessPath.preset(),
        folder: accessPath.folder(),
      });
      logger.info('Category image uploaded successfully', {
        meta: META.CLOUDINARY_SERVICE,
      }); 
      details.image = {id:uploadFile.public_id, url:uploadFile.secure_url};
    }
    const category = await createCategory(details);
    if (category.error) return next(APIError.customError(category.error, 400));
    logger.info("Category created successfully", {meta: META.CATEGORY_SERVICE});
    res.status(200).json({success: true, msg: "Category created successfully"});
  } catch (error) {
    console.log(error);
    next(error);
  }
};
exports.updateCategory = async (req, res, next) => {
  try {
    if (requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error) return next(APIError.unauthorized());
    const {name, id} = req.body;
    if (!id) return next(APIError.badRequest("Category ID is required"));
    if (!name) return next(APIError.badRequest("Category name is required"));
    const details = {name:name.toLowerCase(), admin:req.userId, _id:id};
    if (req.body.fileData) {
      const {fileData, format} = req.body;
      if (!format) return next(APIError.badRequest("Image format is required"));
      if (!CONSTANTS.CATEGORY_IMG_FORMAT.includes(format)) return next(APIError.badRequest("Invalid image format"));
      //delete existing image
      const catExist = await findCategoryById(id);
      if (!catExist) return next(APIError.badRequest("Category does not exist"));
      if (catExist.error) return next(APIError.badRequest(catExist.error));
      if (catExist.image.length > 0) {
        await cloudinary.uploader.destroy(catExist.image[0].id, {
          upload_preset: accessPath.preset(),
          folder: accessPath.folder(),
        });
        logger.info('Existing category image deleted successfully', {
          meta: META.CLOUDINARY_SERVICE,
        }); 
      }
      const uploadFile = await cloudinary.uploader.upload(fileData, {
        upload_preset: accessPath.preset(),
        folder: accessPath.folder(),
      });
      logger.info('Category image updated successfully', {
        meta: META.CLOUDINARY_SERVICE,
      }); 
      details.image = {id:uploadFile.public_id, url:uploadFile.secure_url};
    }
    const category = await categoryUpdate(details);
    if (!category) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (category.error) return next(APIError.customError(category.error, 400));
    logger.info("Category updated successfully", {meta: META.CATEGORY_SERVICE});
    res.status(200).json({success: true, msg: "Category updated successfully"});
  } catch (error) {
    next(error);
  }
};
exports.deleteCategory = async (req, res, next) => {
  try {
    if (requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error) return next(APIError.unauthorized());
    const {categoryId} = req.query;
    if (!categoryId) return next(APIError.badRequest("Category ID is required"));
    const catExist = await findCategoryById(categoryId);
    if (!catExist) return next(APIError.badRequest("Category does not exist"));
    if (catExist.error) return next(APIError.badRequest(catExist.error));
    if (catExist.image.length > 0) {
      await cloudinary.uploader.destroy(catExist.image[0].id, {
        upload_preset: accessPath.preset(),
        folder: accessPath.folder(),
      });
      logger.info('Category image deleted successfully', {
        meta: META.CLOUDINARY_SERVICE,
      });
    }
    const category = await categorydelete(categoryId);
    if (!category) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (category.error) return next(APIError.customError(category.error, 400));
    logger.info("Category deleted successfully", {meta: META.CATEGORY_SERVICE});
    res.status(200).json({success: true, msg: "Category deleted successfully"});
  } catch (error) {
    next(error);
  }
};
exports.articleCategory = async (req, res, next) => {
  try {
    const category = await getCategory();
    if (!category) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (category.error) return next(APIError.customError(category.error, 400));
    logger.info("Category retrieved successfully", {meta: META.CATEGORY_SERVICE});
    const data = category.map((cur) => {
      return responsBuilder.buildCategory(cur.toObject());
    });
    const response = responsBuilder.commonReponse("Found", data, "category");
    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};
exports.category = async (req, res, next) => {
  try { 
   
    const category = await getCategory();
    if (!category) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (category.error) return next(APIError.customError(category.error, 400));
    logger.info("Category retrieved successfully", {meta: META.CATEGORY_SERVICE});
    const data = category.map((cur) => {
      return responsBuilder.buildAdminCategory(cur.toObject());
    });
    const response = responsBuilder.commonReponse("Found", data, "category");
    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};
