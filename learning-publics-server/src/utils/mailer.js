/* eslint-disable no-unused-vars */
const { domainMail, mailAuth } = require("./mail.auth");
const nodemailer = require("nodemailer");
const hbs = require("nodemailer-express-handlebars");
require("dotenv").config();
const path = require("path");
const {CONFIG, CONSTANTS} = require("../config");
const config = require("../config/env");

const handlebarsOptions = {
  viewEngine: {
    extName: ".handlebars",
    partialsDir: path.resolve("./src/views"),
    defaultLayout: false,
  },
  viewPath: path.resolve("./src/views"),
  extName: ".handlebars",
};
let transporter = nodemailer.createTransport(
  {
    ...mailAuth
  });
  
transporter.use("compile", hbs(handlebarsOptions));
exports.mailOptions = (sendTo, subject, message) => {
  return {
    from: sendTo,
    to: domainMail.mail(),
    subject,
    text: message,
    html: message,
  };
};
 
const registrationMailOptions = (sendTo, subject, username) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "registration",
    context: { 
      loginLink: `${process.env.FRONTEND_ORIGIN_URL}/login`,
      site: `${process.env.FRONTEND_ORIGIN_URL}`,
      message: "You're most welcome",
      username,
      conpanyName: config.COMPANY_NAME,
    },
  };
};
//send invitatin mail
const invitationMailOptions = (sendTo, subject, uniqueString,role, expiresIn) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "invitation",
    context: { 
      link: `${process.env.FRONTEND_ORIGIN_URL}/invite/id=${uniqueString}`,
      company: `${CONFIG.APP_NAME}`, 
      expires: `Invitation expires in ${expiresIn}`,
      role:`${role.charAt(0).toUpperCase()+role.slice(1)}`,
    },
  };
};
const passwordMailOptions = (sendTo, subject, expiryTime, uniqueString) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "resetpassword",
    context: {
      expiryTime: `${expiryTime} `,
      link: `${process.env.FRONTEND_ORIGIN_URL}/user/verify-reset?id=${uniqueString}`,
    },
  };
};
const verificationdMailOptions = (
  sendTo,
  subject,
  expiryTime,
  uniqueString,
  username
) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "verify",
    context: {
      expiryTime: `${expiryTime} `,
      link: `${config.FRONTEND_ORIGIN_URL}/user/verify?id=${uniqueString}`,
      username,
    },
  };
};
const articleUploadMailOptions = (
  sendTo,
  subject,
  title,
  name
) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "articleupload",
    context: {
      email: `${config.FRONTEND_ORIGIN_URL}`,
      name,
      company: `${CONFIG.APP_NAME}`,
      title,
    },
  };
};
const articleAdminNotifyMailOptions = (
  sendTo,
  subject,
  title,
  username
) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "articleuploaded",
    context: {
      email: `infor@${config.FRONTEND_ORIGIN_URL}`,
      username,
      company: `${CONFIG.APP_NAME}`,
      title,
      login:  `${config.FRONTEND_ORIGIN_URL}/login`,
      contact: `+234 9084 47384`,
    },
  };
};
const paymentCompleteMailOptions = (
  sendTo,
  subject,
  plan,
  name
) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "payment.completed",
    context: {
      plan,
      name,
    },
  };
};

const registrationOTPMailOptions = (sendTo, subject, otp, expires) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "otp",
    context: { 
      otp,
      company: `${CONFIG.APP_NAME}`,
    },
  };
};
exports.recoveryPasswordMailHandler = async (
  email,
  expiryTime,
  uniqueString
) => {
  return new Promise((resolve, reject) => {
    const mail = passwordMailOptions(
      email,
      "Password Reset",
      expiryTime,
      uniqueString
    );
    transporter.sendMail(mail, (err, data) => {
      if (err) { 
        return reject(err);
      }
      return resolve({ success: true });
    });
  });
};
exports.registrationOTPMailHandler = async (email, otp, expires) => {
  try {
    return new Promise((resolve, reject) => {
      const mail = registrationOTPMailOptions(
        email,
        "Registration OTP",
        otp,
        expires
      );
      transporter.sendMail(mail, (err, data) => {
        if (err) {
          return reject(err);
        }
        return resolve({ success: true });
      });
    });
  } catch (error) {
    return { error: error };
  }
};
exports.registrationMailHandler = async (email, username) => {
  try {
    return new Promise((resolve, reject) => {
      const mail = registrationMailOptions(
        email,
        "Account Registration",
        username,
      );
      transporter.sendMail(mail, (err, data) => {
        if (err) {
          return reject(err);
        }
        return resolve({ success: true });
      });
    });
  } catch (error) {
    return { error: error };
  }
};

exports.invitationMailHandler = async (email, uniqueString,role, expiresIn) => {
  try {
    return new Promise((resolve, reject) => {
      const mail = invitationMailOptions(
        email,
        "Invitation",
        uniqueString,
        role,
        expiresIn
      );
      transporter.sendMail(mail, (err, data) => {
        if (err) {
          return reject(err);
        }
        return resolve({ success: true });
      });
    });
  } catch (error) {
    return { error: error };
  }
};
exports.paymentSuccessMailHandler = async (email,plan, name) => {
  try {
    return new Promise((resolve, reject) => {
      const mail = paymentCompleteMailOptions(
        email,
        "Plan Purchase",
        plan,
        name
      );
      transporter.sendMail(mail, (err, data) => {
        if (err) {
          return reject(err);
        }
        return resolve({ success: true });
      });
    });
  } catch (error) {
    return { error: error };
  }
};
exports.verificationMailHandler = async (
  email,
  expiryTime,
  uniqueString,
  username
) => {
  return new Promise((resolve, reject) => {
    const mail = verificationdMailOptions(
      email,
      "Account Verification",
      expiryTime,
      uniqueString,
      username
    );
    transporter.sendMail(mail, (err, data) => {
      if (err) {
        return reject(err);
      }
      return resolve({ success: true });
    });
  });
};
exports.articleUploadMailHandler = async (
  email,
  title,
  name
) => {
  return new Promise((resolve, reject) => {
    const mail = articleUploadMailOptions(
      email,
      "Article Received",
      title,
      name
    );
    transporter.sendMail(mail, (err, data) => {
      if (err) {
        return reject(err);
      }
      return resolve({ success: true });
    });
  });
};
exports.articleAdminNotifyMailHandler = async (
  email,
  title,
  username
) => {
  return new Promise((resolve, reject) => {
    const mail = articleAdminNotifyMailOptions(
      email,
      "New Article Received",
      title,
      username
    );
    transporter.sendMail(mail, (err, data) => {
      if (err) {
        return reject(err);
      }
      return resolve({ success: true });
    });
  });
};
const articleRejectionMailOptions = (
  sendTo,
  subject,
  title,
  username
) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "article.reject",
    context: {
      username,
      sender: `${CONFIG.APP_NAME}`,
      message:  `We hope this mail finds you well. We are sorry to inform you that your article "${title}" was not approved.`,
      comment:"",
    },
  };
};
exports.articleRejectionMailHandler = async (
  email,
  title,
  username
) => {
  return new Promise((resolve, reject) => {
    const mail = articleRejectionMailOptions(
      email,
      "Article Reply",
      title,
      username
    );
    transporter.sendMail(mail, (err, data) => {
      if (err) {
        return reject(err);
      }
      return resolve({ success: true });
    });
  });
};

const articleApprovalMailOptions = (
  sendTo,
  subject,
  title,
  username,
  articleId,
  status
) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject,
    template: "article.approved",
    context: {
      username,
      sender: `${CONFIG.APP_NAME}`,
      message:  `We are glad to inform you that your article "${title}" has been ${status}.`,
      home: `${config.FRONTEND_ORIGIN_URL}/home`,
      aboutus: `${config.FRONTEND_ORIGIN_URL}/about`,
      contactus: `${config.FRONTEND_ORIGIN_URL}/contact`,
      site: `${config.FRONTEND_ORIGIN_URL}/home`,
      company: `${CONFIG.APP_NAME}`,
      address: `${config.COMPANY_ADDRESS}`,
      link: `${config.FRONTEND_ORIGIN_URL}/article?token=${articleId}`,
      viewbtn: `${status === CONSTANTS.ARTICLE_STATUS[4]? "inline-block": "none"}`,
      info:`${status.charAt(0).toUpperCase() + status.slice(1)}`
    },
  };
};
exports.articleAprovedMailHandler = async (
  email,
  title,
  username,
  articleId,
  status
) => {
  return new Promise((resolve, reject) => {
    const mail = articleApprovalMailOptions(
      email,
      "Article Reply",
      title,
      username,
      articleId,
      status
    );
    transporter.sendMail(mail, (err, data) => {
      if (err) {
        return reject(err);
      }
      return resolve({ success: true });
    });
  });
};
const articleResubmissionMailOptions = (
  sendTo,
  subject,
  title,
  username,
  articleId,
  editorsComments
) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject, 
    template: "article.reject",
    context: {
      username,
      sender: `${CONFIG.APP_NAME}`,
      message:  `We hope this mail finds you well. You are required to make following changes your article "${title}" and resubmit using the ID:${articleId}`,
      comments:` ${editorsComments}`
    },
  };
};

exports.articleResubmissionMailHandler = async (
  email,
  title,
  username, 
  articleId,
  editorsComments
) => {
  return new Promise((resolve, reject) => {
    const mail = articleResubmissionMailOptions(
      email,
      "Article Reply",
      title,
      username,
      articleId,
      editorsComments
    );
    transporter.sendMail(mail, (err, data) => {
      if (err) {
        return reject(err);
      }
      return resolve({ success: true });
    });
  });
};

const articleReviewMailOptions = (
  sendTo,
  subject,
  title,
  username,
  articleId,
  editorsComments,
  article
) => {
  return {
    from: `${CONFIG.APP_NAME} ${domainMail.mail()}`,
    to: sendTo,
    subject, 
    template: "article.reject",
    attachments:[{...article}],
    context: {
      username,
      sender: `${CONFIG.APP_NAME}`,
      message:  `We hope this mail finds you well. You are required to make following changes your article "${title}" and resubmit using the ID:${articleId}`,
      comments:` ${editorsComments}`
    },
  };
};

exports.articleReviewMailHandler = async (
  email,
  title,
  username, 
  articleId,
  editorsComments,
   article
) => {
  return new Promise((resolve, reject) => {
    const mail = articleReviewMailOptions(
      email,
      "Article Review",
      title,
      username,
      articleId,
      editorsComments,
      article
    );
    transporter.sendMail(mail, (err, data) => {
      if (err) {
        return reject(err);
      }
      return resolve({ success: true });
    });
  });
};