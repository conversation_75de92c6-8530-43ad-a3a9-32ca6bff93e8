import AuthSideNavDesk from "components/AuthNav/SideBar/SideBarMobile"
import { DashboardBody, DashboardContainer, Main } from "components/AuthNav/SideBar/style"
import { observer } from "mobx-react-lite"
import React from "react"

const AuthLayout = ({ children }) => {
	return (
		<Main>
			<div style={{ width: "100%", height: "75px", backgroundColor: "#f90" }}></div>

			<DashboardContainer>
				<AuthSideNavDesk />
				<DashboardBody>
					{" "}
					{children}
				</DashboardBody>
			</DashboardContainer>
		</Main>
	)
}

export default observer(AuthLayout)
