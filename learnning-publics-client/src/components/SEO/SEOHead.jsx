import React from 'react';
import { Helmet } from 'react-helmet-async';

const SEOHead = ({ 
  title, 
  description, 
  keywords, 
  author, 
  publishedDate, 
  modifiedDate,
  articleUrl,
  imageUrl,
  doi,
  issn = "2026-5654",
  volume,
  issue,
  pages,
  category,
  tags = [],
  type = "article",
  canonicalUrl,
  abstract
}) => {
  const siteUrl = process.env.REACT_APP_SITE_URL || 'https://www.learningpublics.org';
  const fullUrl = canonicalUrl || `${siteUrl}${articleUrl}`;
  const defaultImage = `${siteUrl}/images/learning-publics-logo.png`;
  const articleImage = imageUrl || defaultImage;

  // Generate structured data for academic articles
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ScholarlyArticle",
    "headline": title,
    "description": description || abstract,
    "author": {
      "@type": "Person",
      "name": author
    },
    "publisher": {
      "@type": "Organization",
      "name": "Learning Publics",
      "logo": {
        "@type": "ImageObject",
        "url": `${siteUrl}/images/learning-publics-logo.png`
      }
    },
    "datePublished": publishedDate,
    "dateModified": modifiedDate || publishedDate,
    "url": fullUrl,
    "image": articleImage,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": fullUrl
    },
    ...(doi && { "identifier": [
      {
        "@type": "PropertyValue",
        "name": "DOI",
        "value": doi
      }
    ]}),
    ...(issn && { "issn": issn }),
    ...(volume && { "volumeNumber": volume }),
    ...(issue && { "issueNumber": issue }),
    ...(pages && { "pagination": pages }),
    ...(category && { "about": {
      "@type": "Thing",
      "name": category
    }}),
    "keywords": keywords || tags.join(', '),
    "inLanguage": "en",
    "isAccessibleForFree": true,
    "license": "https://creativecommons.org/licenses/by/4.0/"
  };

  // Dublin Core metadata for academic indexing
  const dublinCoreData = {
    "DC.Title": title,
    "DC.Creator": author,
    "DC.Subject": keywords || tags.join(', '),
    "DC.Description": description || abstract,
    "DC.Publisher": "Learning Publics",
    "DC.Date": publishedDate,
    "DC.Type": "Text.Serial.Journal",
    "DC.Format": "application/pdf",
    "DC.Identifier": doi || fullUrl,
    "DC.Language": "en",
    "DC.Rights": "Creative Commons Attribution 4.0 International License"
  };

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title} | Learning Publics Journal</title>
      <meta name="description" content={description || abstract} />
      <meta name="keywords" content={keywords || tags.join(', ')} />
      <meta name="author" content={author} />
      <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      <meta name="googlebot" content="index, follow" />
      <link rel="canonical" href={fullUrl} />

      {/* Open Graph Tags */}
      <meta property="og:type" content="article" />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description || abstract} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:image" content={articleImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="Learning Publics Journal" />
      <meta property="og:locale" content="en_US" />
      {publishedDate && <meta property="article:published_time" content={publishedDate} />}
      {modifiedDate && <meta property="article:modified_time" content={modifiedDate} />}
      {author && <meta property="article:author" content={author} />}
      {category && <meta property="article:section" content={category} />}
      {tags.map(tag => (
        <meta key={tag} property="article:tag" content={tag} />
      ))}

      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description || abstract} />
      <meta name="twitter:image" content={articleImage} />
      <meta name="twitter:site" content="@LearningPublics" />

      {/* Academic Meta Tags */}
      <meta name="citation_title" content={title} />
      <meta name="citation_author" content={author} />
      <meta name="citation_publication_date" content={publishedDate} />
      <meta name="citation_journal_title" content="Learning Publics Journal" />
      {issn && <meta name="citation_issn" content={issn} />}
      {volume && <meta name="citation_volume" content={volume} />}
      {issue && <meta name="citation_issue" content={issue} />}
      {pages && <meta name="citation_firstpage" content={pages.split('-')[0]} />}
      {pages && pages.includes('-') && <meta name="citation_lastpage" content={pages.split('-')[1]} />}
      {doi && <meta name="citation_doi" content={doi} />}
      <meta name="citation_pdf_url" content={`${siteUrl}/api/v1/author/articles/pdf/${articleUrl?.split('/').pop()}`} />
      <meta name="citation_abstract_html_url" content={fullUrl} />
      <meta name="citation_language" content="en" />

      {/* Dublin Core Metadata */}
      {Object.entries(dublinCoreData).map(([key, value]) => (
        <meta key={key} name={key} content={value} />
      ))}

      {/* Additional Academic Metadata */}
      <meta name="prism.publicationName" content="Learning Publics Journal" />
      <meta name="prism.issn" content={issn} />
      {volume && <meta name="prism.volume" content={volume} />}
      {issue && <meta name="prism.number" content={issue} />}
      {publishedDate && <meta name="prism.publicationDate" content={publishedDate} />}

      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>

      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#dc2626" />
      <meta name="msapplication-TileColor" content="#dc2626" />
      <meta name="application-name" content="Learning Publics Journal" />
      <meta name="apple-mobile-web-app-title" content="Learning Publics" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="mobile-web-app-capable" content="yes" />
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true" />
      <link rel="preconnect" href="https://res.cloudinary.com" />
    </Helmet>
  );
};

export default SEOHead;
