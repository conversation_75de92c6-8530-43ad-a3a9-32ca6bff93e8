import React, { useState } from "react";

import { USER } from "api/ACTION";
import jmsApp from "api/jms";
import styled from "styled-components";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";

const Container = styled.div`
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
`;

const Label = styled.label`
  font-weight: bold;
  margin-bottom: 5px;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px;
  margin-bottom: 20px;
  border: 1px solid #ccc;
  border-radius: 3px;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: between;
  gap: 4;
`;

const Button = styled.button`
  padding: 8px 16px;
  background-color: #4caf50;
  color: #fff;
  border: none;
  border-radius: 3px;
  cursor: pointer;
`;

const DeleteButton = styled(Button)`
  background-color: #f44336;

  &:hover {
    background-color: #d32f2f;
  }
`;

const VerifyButton = styled(Button)`
  background-color: #2196f3;

  &:hover {
    background-color: #1976d2;
  }
`;

const UserAccount = () => {
  const [active, setActive] = useState(false);
  const [values, setValues] = useState({
    name: "",
    username: "",
    email: "",
    phone: ""
  });

  const handleChange = (e) => {
    setValues({ ...values, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Perform submission logic here

    setActive(true);
    try {
      const { data } = await jmsApp.patch(USER.UPDATE_USER(), {
        ...values
      });
      if (data.success) {
        toast.success(data.msg);
        setActive(false);
      }
    } catch (error) {
      setActive(false);
    }
  };

  const navigate = useNavigate();

  const handleDelete = async () => {
    // Perform delete user logic here

    setActive(true);
    try {
      const { data } = await jmsApp.patch(USER.DELETE_USER());

      if (data.success) {
        setActive(false);
        navigate("/login");
      }
    } catch (error) {
      setActive(false);
    }
  };

  const handleVerifyStatus = async () => {
    // Perform verify status logic here

    setActive(true);
    try {
      await jmsApp.delete(USER.UPDATE_USER());

      setActive(false);
    } catch (error) {
      setActive(false);
    }
  };

  return (
    <>
      <h1
        style={{
          color: "green",
          paddingTop: 40,
          paddingBottom: 10,
          margin: "auto"
        }}
      >
        {" "}
        Update User Details{" "}
      </h1>
      <Container>
        <form onSubmit={handleSubmit}>
          <Label>Name:</Label>
          <Input
            type="text"
            name="name"
            value={values.name}
            onChange={handleChange}
          />
          <Label>User Name:</Label>
          <Input
            type="text"
            name="username"
            value={values.username}
            onChange={handleChange}
          />

          <Label>Email:</Label>
          <Input
            type="email"
            name="email"
            value={values.email}
            onChange={handleChange}
          />

          <Label>Phone:</Label>
          <Input
            type="phone"
            value={values.phone}
            name="phone"
            onChange={handleChange}
          />

          <ButtonContainer>
            <Button type="submit">{active ? "updating..." : "Save"}</Button>
            <DeleteButton type="button" onClick={handleDelete}>
              {active ? "Deleting user" : "Delete User"}
            </DeleteButton>
            <VerifyButton type="button" onClick={handleVerifyStatus}>
              {active ? "verifying..." : "Verify Status"}
            </VerifyButton>
          </ButtonContainer>
        </form>
      </Container>
    </>
  );
};

export default UserAccount;
