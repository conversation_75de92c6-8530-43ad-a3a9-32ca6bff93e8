const PricingConfig = require('../models/pricingConfig.model');
const Account = require('../models/account.model');

const defaultPricingConfigs = [
  {
    serviceType: 'article_submission',
    serviceName: 'Article Submission',
    serviceDescription: 'Standard article submission and initial review process. Currently free for all authors to encourage academic participation.',
    currentPrice: {
      amount: 0,
      currency: 'USD'
    },
    isActive: true,
    isFree: true,
    paymentMethods: ['paystack', 'stripe'],
    refundPolicy: {
      isRefundable: false,
      refundPeriodDays: 0,
      refundPercentage: 0,
      conditions: ['Article submission fees are non-refundable']
    },
    regionalPricing: [
      {
        region: 'developing_countries',
        countries: ['NG', 'GH', 'KE', 'UG', 'TZ', 'ZA', 'EG', 'MA', 'TN', 'DZ'],
        price: {
          amount: 0,
          currency: 'USD'
        },
        isActive: true
      }
    ],
    adminSettings: {
      autoApplyScheduledChanges: true,
      requireApprovalForChanges: false,
      notifyUsersOfPriceChanges: true,
      priceChangeNotificationDays: 14
    }
  },
  {
    serviceType: 'expedited_review',
    serviceName: 'Expedited Peer Review',
    serviceDescription: 'Fast-track peer review process with guaranteed response within 2 weeks instead of standard 4-6 weeks.',
    currentPrice: {
      amount: 50,
      currency: 'USD'
    },
    isActive: true,
    isFree: false,
    paymentMethods: ['paystack', 'stripe'],
    refundPolicy: {
      isRefundable: true,
      refundPeriodDays: 7,
      refundPercentage: 100,
      conditions: [
        'Refund available if expedited timeline is not met',
        'Request must be made within 7 days of payment'
      ]
    },
    regionalPricing: [
      {
        region: 'developing_countries',
        countries: ['NG', 'GH', 'KE', 'UG', 'TZ', 'ZA', 'EG', 'MA', 'TN', 'DZ'],
        price: {
          amount: 25,
          currency: 'USD'
        },
        isActive: true
      }
    ]
  },
  {
    serviceType: 'open_access_fee',
    serviceName: 'Open Access Publication',
    serviceDescription: 'Make your article freely available to all readers worldwide with immediate open access publication.',
    currentPrice: {
      amount: 25,
      currency: 'USD'
    },
    isActive: true,
    isFree: false,
    paymentMethods: ['paystack', 'stripe'],
    refundPolicy: {
      isRefundable: false,
      refundPeriodDays: 0,
      refundPercentage: 0,
      conditions: ['Open access fees are non-refundable once article is published']
    },
    regionalPricing: [
      {
        region: 'developing_countries',
        countries: ['NG', 'GH', 'KE', 'UG', 'TZ', 'ZA', 'EG', 'MA', 'TN', 'DZ'],
        price: {
          amount: 10,
          currency: 'USD'
        },
        isActive: true
      }
    ]
  },
  {
    serviceType: 'reprints',
    serviceName: 'Article Reprints',
    serviceDescription: 'High-quality printed copies of your published article for distribution and archival purposes.',
    currentPrice: {
      amount: 15,
      currency: 'USD'
    },
    isActive: true,
    isFree: false,
    paymentMethods: ['paystack', 'stripe'],
    bulkPricing: [
      {
        minimumQuantity: 10,
        price: {
          amount: 12,
          currency: 'USD'
        },
        description: '10+ copies - 20% discount'
      },
      {
        minimumQuantity: 25,
        price: {
          amount: 10,
          currency: 'USD'
        },
        description: '25+ copies - 33% discount'
      }
    ]
  },
  {
    serviceType: 'additional_review',
    serviceName: 'Additional Peer Review',
    serviceDescription: 'Request additional expert reviewers for comprehensive feedback on complex or interdisciplinary research.',
    currentPrice: {
      amount: 30,
      currency: 'USD'
    },
    isActive: true,
    isFree: false,
    paymentMethods: ['paystack', 'stripe'],
    regionalPricing: [
      {
        region: 'developing_countries',
        countries: ['NG', 'GH', 'KE', 'UG', 'TZ', 'ZA', 'EG', 'MA', 'TN', 'DZ'],
        price: {
          amount: 15,
          currency: 'USD'
        },
        isActive: true
      }
    ]
  },
  {
    serviceType: 'priority_processing',
    serviceName: 'Priority Processing',
    serviceDescription: 'Priority handling of your submission with dedicated editorial attention and faster processing times.',
    currentPrice: {
      amount: 40,
      currency: 'USD'
    },
    isActive: true,
    isFree: false,
    paymentMethods: ['paystack', 'stripe'],
    regionalPricing: [
      {
        region: 'developing_countries',
        countries: ['NG', 'GH', 'KE', 'UG', 'TZ', 'ZA', 'EG', 'MA', 'TN', 'DZ'],
        price: {
          amount: 20,
          currency: 'USD'
        },
        isActive: true
      }
    ]
  }
];

async function seedPricingConfigs() {
  try {
    console.log('🌱 Starting pricing configuration seeding...');

    // For initial seeding, we'll create configs without requiring an admin user
    // In production, you should have an admin user created first
    console.log('👤 Creating pricing configs without admin user requirement for initial setup...');

    // Clear existing pricing configs (optional - remove in production)
    const existingCount = await PricingConfig.countDocuments();
    if (existingCount > 0) {
      console.log(`🗑️  Found ${existingCount} existing pricing configs. Skipping seeding to avoid duplicates.`);
      console.log('💡 To reseed, manually delete existing configs first.');
      return;
    }

    // Create pricing configurations
    const createdConfigs = [];
    
    for (const configData of defaultPricingConfigs) {
      const config = new PricingConfig({
        ...configData,
        // For initial seeding, we'll skip the user references
        // createdBy: adminUser._id,
        // lastModifiedBy: adminUser._id
      });

      await config.save();
      createdConfigs.push(config);

      console.log(`✅ Created pricing config: ${config.serviceName} (${config.serviceType})`);
    }

    console.log(`🎉 Successfully seeded ${createdConfigs.length} pricing configurations!`);
    
    // Display summary
    console.log('\n📊 Pricing Summary:');
    createdConfigs.forEach(config => {
      const price = config.isFree ? 'FREE' : `$${config.currentPrice.amount} ${config.currentPrice.currency}`;
      console.log(`   • ${config.serviceName}: ${price}`);
    });

    console.log('\n🔧 Admin can now manage pricing through:');
    console.log('   • Admin dashboard pricing management interface');
    console.log('   • API endpoints for dynamic pricing updates');
    console.log('   • Scheduled price changes and promotional campaigns');

    return createdConfigs;

  } catch (error) {
    console.error('❌ Error seeding pricing configurations:', error);
    throw error;
  }
}

// Export for use in other seeders or standalone execution
module.exports = {
  seedPricingConfigs,
  defaultPricingConfigs
};

// Allow direct execution
if (require.main === module) {
  const mongoose = require('mongoose');
  
  // Connect to database
  mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/learning-publics', {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });

  seedPricingConfigs()
    .then(() => {
      console.log('✅ Pricing seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Pricing seeding failed:', error);
      process.exit(1);
    });
}
