const { CONSTANTS } = require('../config');
const logger = require('../logger');
const { getPublishedArticles, getCategories } = require('../services');
const { META } = require('../utils/actions');

const FRONTEND_URL = process.env.FRONTEND_ORIGIN_URL || 'https://www.learningpublics.com';

// Generate XML sitemap index
exports.getSitemapIndex = async (req, res, next) => {
  try {
    const lastModified = new Date().toISOString();
    
    const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${FRONTEND_URL}/sitemap-static.xml</loc>
    <lastmod>${lastModified}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${FRONTEND_URL}/sitemap-articles.xml</loc>
    <lastmod>${lastModified}</lastmod>
  </sitemap>
  <sitemap>
    <loc>${FRONTEND_URL}/sitemap-categories.xml</loc>
    <lastmod>${lastModified}</lastmod>
  </sitemap>
</sitemapindex>`;

    res.set('Content-Type', 'application/xml');
    res.send(sitemapIndex);
    
    logger.info('Sitemap index generated successfully', { meta: META.SEO_SERVICE });
  } catch (error) {
    logger.error('Error generating sitemap index:', error);
    next(error);
  }
};

// Generate articles sitemap
exports.getArticlesSitemap = async (req, res, next) => {
  try {
    const articles = await getPublishedArticles();
    
    if (articles.error) {
      throw new Error(articles.error);
    }

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">`;

    articles.forEach(article => {
      const lastmod = new Date(article.updatedAt).toISOString();
      const articleUrl = `${FRONTEND_URL}/articles/${article.slug}`;
      const pdfUrl = `${FRONTEND_URL}/api/v1/author/articles/pdf/${article.slug}`;
      
      sitemap += `
  <url>
    <loc>${articleUrl}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
    <news:news>
      <news:publication>
        <news:name>Learning Publics Journal</news:name>
        <news:language>en</news:language>
      </news:publication>
      <news:publication_date>${new Date(article.createdAt).toISOString()}</news:publication_date>
      <news:title>${escapeXml(article.title)}</news:title>
      <news:keywords>${escapeXml(article.keywords?.join(', ') || '')}</news:keywords>
    </news:news>
  </url>
  <url>
    <loc>${pdfUrl}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`;
    });

    sitemap += '\n</urlset>';

    res.set('Content-Type', 'application/xml');
    res.send(sitemap);
    
    logger.info(`Articles sitemap generated with ${articles.length} articles`, { meta: META.SEO_SERVICE });
  } catch (error) {
    logger.error('Error generating articles sitemap:', error);
    next(error);
  }
};

// Generate categories sitemap
exports.getCategoriesSitemap = async (req, res, next) => {
  try {
    const categories = await getCategories();
    
    if (categories.error) {
      throw new Error(categories.error);
    }

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    categories.forEach(category => {
      const categoryUrl = `${FRONTEND_URL}/articles/category/${category.slug}`;
      
      sitemap += `
  <url>
    <loc>${categoryUrl}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`;
    });

    sitemap += '\n</urlset>';

    res.set('Content-Type', 'application/xml');
    res.send(sitemap);
    
    logger.info(`Categories sitemap generated with ${categories.length} categories`, { meta: META.SEO_SERVICE });
  } catch (error) {
    logger.error('Error generating categories sitemap:', error);
    next(error);
  }
};

// Generate static pages sitemap
exports.getStaticPagesSitemap = async (req, res, next) => {
  try {
    const staticPages = [
      { url: '', priority: '1.0', changefreq: 'daily' },
      { url: '/articles', priority: '0.9', changefreq: 'daily' },
      { url: '/about', priority: '0.7', changefreq: 'monthly' },
      { url: '/contact', priority: '0.6', changefreq: 'monthly' },
      { url: '/submit', priority: '0.8', changefreq: 'monthly' },
      { url: '/guidelines', priority: '0.7', changefreq: 'monthly' },
      { url: '/editorial-board', priority: '0.7', changefreq: 'monthly' },
      { url: '/privacy-policy', priority: '0.3', changefreq: 'yearly' },
      { url: '/terms-of-service', priority: '0.3', changefreq: 'yearly' }
    ];

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

    staticPages.forEach(page => {
      sitemap += `
  <url>
    <loc>${FRONTEND_URL}${page.url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
    });

    sitemap += '\n</urlset>';

    res.set('Content-Type', 'application/xml');
    res.send(sitemap);
    
    logger.info('Static pages sitemap generated successfully', { meta: META.SEO_SERVICE });
  } catch (error) {
    logger.error('Error generating static pages sitemap:', error);
    next(error);
  }
};

// Generate robots.txt
exports.getRobotsTxt = async (req, res, next) => {
  try {
    const robotsTxt = `User-agent: *
Allow: /

# Academic crawlers
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

# Academic search engines
User-agent: ia_archiver
Allow: /

User-agent: Scooter
Allow: /

# Disallow admin and API endpoints
Disallow: /admin
Disallow: /api/
Disallow: /login
Disallow: /register

# Allow PDF access for academic indexing
Allow: /api/v1/author/articles/pdf/

# Sitemaps
Sitemap: ${FRONTEND_URL}/sitemap.xml

# Crawl delay for academic crawlers
Crawl-delay: 1`;

    res.set('Content-Type', 'text/plain');
    res.send(robotsTxt);
    
    logger.info('Robots.txt generated successfully', { meta: META.SEO_SERVICE });
  } catch (error) {
    logger.error('Error generating robots.txt:', error);
    next(error);
  }
};

// Generate RSS feed
exports.getRSSFeed = async (req, res, next) => {
  try {
    const articles = await getPublishedArticles();

    if (articles.error) {
      throw new Error(articles.error);
    }

    // Get latest 20 articles for RSS
    const latestArticles = articles.slice(0, 20);
    const lastBuildDate = new Date().toUTCString();
    const pubDate = latestArticles.length > 0 ? new Date(latestArticles[0].createdAt).toUTCString() : lastBuildDate;

    let rss = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom" xmlns:dc="http://purl.org/dc/elements/1.1/">
  <channel>
    <title>Learning Publics Journal of Agriculture and Environmental Studies</title>
    <link>${FRONTEND_URL}</link>
    <description>Academic research articles in agriculture and environmental studies</description>
    <language>en-us</language>
    <lastBuildDate>${lastBuildDate}</lastBuildDate>
    <pubDate>${pubDate}</pubDate>
    <ttl>1440</ttl>
    <generator>Learning Publics Journal</generator>
    <managingEditor><EMAIL> (Learning Publics Editorial Team)</managingEditor>
    <webMaster><EMAIL> (Learning Publics Technical Team)</webMaster>
    <category>Academic Research</category>
    <category>Agriculture</category>
    <category>Environmental Studies</category>
    <atom:link href="${FRONTEND_URL}/rss.xml" rel="self" type="application/rss+xml" />
    <image>
      <url>${FRONTEND_URL}/images/learning-publics-logo.png</url>
      <title>Learning Publics Journal</title>
      <link>${FRONTEND_URL}</link>
    </image>`;

    latestArticles.forEach(article => {
      const articleUrl = `${FRONTEND_URL}/articles/${article.slug}`;
      const pubDate = new Date(article.createdAt).toUTCString();
      const description = escapeXml(article.description || '');
      const title = escapeXml(article.title);
      const author = escapeXml(article.author);
      const category = article.category ? escapeXml(article.category.name) : 'Research';

      rss += `
    <item>
      <title>${title}</title>
      <link>${articleUrl}</link>
      <description>${description}</description>
      <author><EMAIL> (${author})</author>
      <category>${category}</category>
      <pubDate>${pubDate}</pubDate>
      <guid isPermaLink="true">${articleUrl}</guid>
      <dc:creator>${author}</dc:creator>
      <dc:date>${new Date(article.createdAt).toISOString()}</dc:date>
    </item>`;
    });

    rss += `
  </channel>
</rss>`;

    res.set('Content-Type', 'application/rss+xml');
    res.send(rss);

    logger.info(`RSS feed generated with ${latestArticles.length} articles`, { meta: META.SEO_SERVICE });
  } catch (error) {
    logger.error('Error generating RSS feed:', error);
    next(error);
  }
};

// Generate Atom feed
exports.getAtomFeed = async (req, res, next) => {
  try {
    const articles = await getPublishedArticles();

    if (articles.error) {
      throw new Error(articles.error);
    }

    // Get latest 20 articles for Atom
    const latestArticles = articles.slice(0, 20);
    const updated = latestArticles.length > 0 ? new Date(latestArticles[0].updatedAt).toISOString() : new Date().toISOString();

    let atom = `<?xml version="1.0" encoding="UTF-8"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <title>Learning Publics Journal of Agriculture and Environmental Studies</title>
  <link href="${FRONTEND_URL}" />
  <link href="${FRONTEND_URL}/atom.xml" rel="self" />
  <id>${FRONTEND_URL}/</id>
  <updated>${updated}</updated>
  <subtitle>Academic research articles in agriculture and environmental studies</subtitle>
  <generator>Learning Publics Journal</generator>
  <rights>Creative Commons Attribution 4.0 International License</rights>
  <category term="Academic Research" />
  <category term="Agriculture" />
  <category term="Environmental Studies" />`;

    latestArticles.forEach(article => {
      const articleUrl = `${FRONTEND_URL}/articles/${article.slug}`;
      const published = new Date(article.createdAt).toISOString();
      const updated = new Date(article.updatedAt).toISOString();
      const description = escapeXml(article.description || '');
      const title = escapeXml(article.title);
      const author = escapeXml(article.author);
      const category = article.category ? escapeXml(article.category.name) : 'Research';

      atom += `
  <entry>
    <title>${title}</title>
    <link href="${articleUrl}" />
    <id>${articleUrl}</id>
    <published>${published}</published>
    <updated>${updated}</updated>
    <summary>${description}</summary>
    <author>
      <name>${author}</name>
      <email><EMAIL></email>
    </author>
    <category term="${category}" />
  </entry>`;
    });

    atom += `
</feed>`;

    res.set('Content-Type', 'application/atom+xml');
    res.send(atom);

    logger.info(`Atom feed generated with ${latestArticles.length} articles`, { meta: META.SEO_SERVICE });
  } catch (error) {
    logger.error('Error generating Atom feed:', error);
    next(error);
  }
};

// Helper function to escape XML characters
function escapeXml(unsafe) {
  if (!unsafe) return '';
  return unsafe.replace(/[<>&'"]/g, function (c) {
    switch (c) {
      case '<': return '&lt;';
      case '>': return '&gt;';
      case '&': return '&amp;';
      case '\'': return '&apos;';
      case '"': return '&quot;';
    }
  });
}
