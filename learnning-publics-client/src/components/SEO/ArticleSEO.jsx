import React from 'react';
import { Helmet } from 'react-helmet-async';
import { 
  generateArticleStructuredData, 
  generateBreadcrumbStructuredData,
  generateOpenGraphTags,
  generateTwitterCardTags,
  generateAcademicMetaTags,
  generateCanonicalUrl
} from 'utils/seo';

const ArticleSEO = ({ article, breadcrumbs = [] }) => {
  if (!article) return null;

  const structuredData = generateArticleStructuredData(article);
  const breadcrumbData = breadcrumbs.length > 0 ? generateBreadcrumbStructuredData(breadcrumbs) : null;
  const openGraphTags = generateOpenGraphTags(article);
  const twitterTags = generateTwitterCardTags(article);
  const academicTags = generateAcademicMetaTags(article);
  const canonicalUrl = generateCanonicalUrl(`/articles/${article.slug}`);
  
  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{article.title} | Learning Publics</title>
      <meta name="description" content={article.metaDescription || article.description} />
      <meta name="keywords" content={article.keywords?.join(', ')} />
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Open Graph Tags */}
      {Object.entries(openGraphTags).map(([property, content]) => (
        <meta key={property} property={property} content={content} />
      ))}
      
      {/* Twitter Card Tags */}
      {Object.entries(twitterTags).map(([name, content]) => (
        <meta key={name} name={name} content={content} />
      ))}
      
      {/* Academic Meta Tags */}
      {Object.entries(academicTags).map(([name, content]) => (
        <meta key={name} name={name} content={content} />
      ))}
      
      {/* Additional SEO Meta Tags */}
      <meta name="author" content={article.author} />
      <meta name="robots" content="index, follow" />
      <meta name="googlebot" content="index, follow" />
      <meta name="language" content="en-US" />
      <meta name="revisit-after" content="7 days" />
      
      {/* Article specific meta tags */}
      <meta property="article:author" content={article.author} />
      <meta property="article:published_time" content={article.createdAt} />
      <meta property="article:modified_time" content={article.updatedAt} />
      <meta property="article:section" content={article.category?.name} />
      {article.keywords?.map(keyword => (
        <meta key={keyword} property="article:tag" content={keyword} />
      ))}
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
      
      {breadcrumbData && (
        <script type="application/ld+json">
          {JSON.stringify(breadcrumbData)}
        </script>
      )}
    </Helmet>
  );
};

export default ArticleSEO;
