import { useEffect, useRef, useCallback } from 'react';

/**
 * Performance monitoring hook for Learning Publics Journal components
 * Tracks render times, memory usage, and component lifecycle metrics
 */
export const usePerformanceMonitor = (componentName, options = {}) => {
  const {
    enableLogging = process.env.NODE_ENV === 'development',
    trackMemory = false,
    trackRenders = true,
    trackMounts = true,
    onPerformanceData
  } = options;

  const renderCountRef = useRef(0);
  const mountTimeRef = useRef(null);
  const lastRenderTimeRef = useRef(null);
  const renderTimesRef = useRef([]);
  const memoryUsageRef = useRef([]);

  // Track component mount time
  useEffect(() => {
    if (trackMounts) {
      mountTimeRef.current = performance.now();
      
      if (enableLogging) {
        console.log(`🚀 [${componentName}] Component mounted at ${mountTimeRef.current.toFixed(2)}ms`);
      }
    }

    // Cleanup function to track unmount
    return () => {
      if (trackMounts && mountTimeRef.current) {
        const unmountTime = performance.now();
        const lifespan = unmountTime - mountTimeRef.current;
        
        if (enableLogging) {
          console.log(`🔄 [${componentName}] Component unmounted after ${lifespan.toFixed(2)}ms lifespan`);
        }

        // Report performance data
        if (onPerformanceData) {
          onPerformanceData({
            componentName,
            type: 'unmount',
            lifespan,
            renderCount: renderCountRef.current,
            averageRenderTime: renderTimesRef.current.length > 0 
              ? renderTimesRef.current.reduce((a, b) => a + b, 0) / renderTimesRef.current.length 
              : 0,
            memoryUsage: memoryUsageRef.current
          });
        }
      }
    };
  }, [componentName, trackMounts, enableLogging, onPerformanceData]);

  // Track renders
  useEffect(() => {
    if (trackRenders) {
      const renderStartTime = performance.now();
      renderCountRef.current += 1;

      // Use setTimeout to measure render completion
      setTimeout(() => {
        const renderEndTime = performance.now();
        const renderTime = renderEndTime - renderStartTime;
        
        lastRenderTimeRef.current = renderTime;
        renderTimesRef.current.push(renderTime);
        
        // Keep only last 50 render times to prevent memory leaks
        if (renderTimesRef.current.length > 50) {
          renderTimesRef.current = renderTimesRef.current.slice(-50);
        }

        if (enableLogging && renderTime > 16) { // Log slow renders (>16ms)
          console.warn(`⚠️ [${componentName}] Slow render #${renderCountRef.current}: ${renderTime.toFixed(2)}ms`);
        }
      }, 0);
    }
  });

  // Track memory usage
  useEffect(() => {
    if (trackMemory && 'memory' in performance) {
      const memoryInfo = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit,
        timestamp: Date.now()
      };
      
      memoryUsageRef.current.push(memoryInfo);
      
      // Keep only last 20 memory snapshots
      if (memoryUsageRef.current.length > 20) {
        memoryUsageRef.current = memoryUsageRef.current.slice(-20);
      }

      if (enableLogging) {
        const usedMB = (memoryInfo.used / 1024 / 1024).toFixed(2);
        console.log(`💾 [${componentName}] Memory usage: ${usedMB}MB`);
      }
    }
  });

  // Performance measurement utilities
  const measureAsync = useCallback(async (operationName, asyncOperation) => {
    const startTime = performance.now();
    
    try {
      const result = await asyncOperation();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (enableLogging) {
        console.log(`⏱️ [${componentName}] ${operationName}: ${duration.toFixed(2)}ms`);
      }

      if (onPerformanceData) {
        onPerformanceData({
          componentName,
          type: 'async_operation',
          operationName,
          duration,
          timestamp: Date.now()
        });
      }

      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (enableLogging) {
        console.error(`❌ [${componentName}] ${operationName} failed after ${duration.toFixed(2)}ms:`, error);
      }

      throw error;
    }
  }, [componentName, enableLogging, onPerformanceData]);

  const measureSync = useCallback((operationName, syncOperation) => {
    const startTime = performance.now();
    
    try {
      const result = syncOperation();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (enableLogging && duration > 5) { // Log operations taking >5ms
        console.log(`⏱️ [${componentName}] ${operationName}: ${duration.toFixed(2)}ms`);
      }

      if (onPerformanceData) {
        onPerformanceData({
          componentName,
          type: 'sync_operation',
          operationName,
          duration,
          timestamp: Date.now()
        });
      }

      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (enableLogging) {
        console.error(`❌ [${componentName}] ${operationName} failed after ${duration.toFixed(2)}ms:`, error);
      }

      throw error;
    }
  }, [componentName, enableLogging, onPerformanceData]);

  // Get performance summary
  const getPerformanceSummary = useCallback(() => {
    const now = performance.now();
    const lifespan = mountTimeRef.current ? now - mountTimeRef.current : 0;
    
    return {
      componentName,
      renderCount: renderCountRef.current,
      lifespan,
      lastRenderTime: lastRenderTimeRef.current,
      averageRenderTime: renderTimesRef.current.length > 0 
        ? renderTimesRef.current.reduce((a, b) => a + b, 0) / renderTimesRef.current.length 
        : 0,
      maxRenderTime: renderTimesRef.current.length > 0 
        ? Math.max(...renderTimesRef.current) 
        : 0,
      minRenderTime: renderTimesRef.current.length > 0 
        ? Math.min(...renderTimesRef.current) 
        : 0,
      memorySnapshots: memoryUsageRef.current.length,
      currentMemory: trackMemory && 'memory' in performance 
        ? {
            used: performance.memory.usedJSHeapSize,
            total: performance.memory.totalJSHeapSize,
            limit: performance.memory.jsHeapSizeLimit
          }
        : null
    };
  }, [componentName, trackMemory]);

  // Log performance summary
  const logPerformanceSummary = useCallback(() => {
    if (enableLogging) {
      const summary = getPerformanceSummary();
      console.group(`📊 [${componentName}] Performance Summary`);
      console.log('Render Count:', summary.renderCount);
      console.log('Component Lifespan:', `${summary.lifespan.toFixed(2)}ms`);
      console.log('Average Render Time:', `${summary.averageRenderTime.toFixed(2)}ms`);
      console.log('Max Render Time:', `${summary.maxRenderTime.toFixed(2)}ms`);
      console.log('Min Render Time:', `${summary.minRenderTime.toFixed(2)}ms`);
      
      if (summary.currentMemory) {
        console.log('Current Memory Usage:', `${(summary.currentMemory.used / 1024 / 1024).toFixed(2)}MB`);
      }
      
      console.groupEnd();
    }
  }, [componentName, enableLogging, getPerformanceSummary]);

  return {
    measureAsync,
    measureSync,
    getPerformanceSummary,
    logPerformanceSummary,
    renderCount: renderCountRef.current,
    lastRenderTime: lastRenderTimeRef.current
  };
};

/**
 * Hook to track Core Web Vitals for Learning Publics Journal
 */
export const useWebVitals = (options = {}) => {
  const { onVitalsData, enableLogging = process.env.NODE_ENV === 'development' } = options;

  useEffect(() => {
    // Track Largest Contentful Paint (LCP)
    const observeLCP = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          
          if (enableLogging) {
            console.log(`🎯 LCP: ${lastEntry.startTime.toFixed(2)}ms`);
          }

          if (onVitalsData) {
            onVitalsData({
              metric: 'LCP',
              value: lastEntry.startTime,
              timestamp: Date.now()
            });
          }
        });

        observer.observe({ entryTypes: ['largest-contentful-paint'] });
        return () => observer.disconnect();
      }
    };

    // Track First Input Delay (FID)
    const observeFID = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            const fid = entry.processingStart - entry.startTime;
            
            if (enableLogging) {
              console.log(`⚡ FID: ${fid.toFixed(2)}ms`);
            }

            if (onVitalsData) {
              onVitalsData({
                metric: 'FID',
                value: fid,
                timestamp: Date.now()
              });
            }
          });
        });

        observer.observe({ entryTypes: ['first-input'] });
        return () => observer.disconnect();
      }
    };

    // Track Cumulative Layout Shift (CLS)
    const observeCLS = () => {
      if ('PerformanceObserver' in window) {
        let clsValue = 0;
        
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          
          if (enableLogging) {
            console.log(`📐 CLS: ${clsValue.toFixed(4)}`);
          }

          if (onVitalsData) {
            onVitalsData({
              metric: 'CLS',
              value: clsValue,
              timestamp: Date.now()
            });
          }
        });

        observer.observe({ entryTypes: ['layout-shift'] });
        return () => observer.disconnect();
      }
    };

    const cleanupLCP = observeLCP();
    const cleanupFID = observeFID();
    const cleanupCLS = observeCLS();

    return () => {
      if (cleanupLCP) cleanupLCP();
      if (cleanupFID) cleanupFID();
      if (cleanupCLS) cleanupCLS();
    };
  }, [enableLogging, onVitalsData]);
};
