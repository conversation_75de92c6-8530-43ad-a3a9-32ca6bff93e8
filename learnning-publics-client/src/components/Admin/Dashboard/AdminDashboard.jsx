import { ADMIN_ROLES, override } from "utils/admin-roles";
import {
  ARTICLE_ENDPOINT,
  COURSE,
  DOWNLOAD_ENDPOINT,
  USER_PLAN
} from "api/ACTION";
import {
  AdminDashboardCont,
  ArticleCont,
  CardCont,
  StatusProp
} from "components/Admin/Dashboard/Dashboard.style";
import ArticleAssign, { handleAssign } from "./article-asign";
import {
  PlanHeading,
  courseHeading,
  heading
} from "../sharedComponents/articleData";
import {
  handleArticlePublished,
  handleArticleReview
} from "./admin-article-count";
import { useEffect, useMemo, useState, Suspense } from "react";

import AddCategoreis from "../categories/AddCategoreis";
import ArticlesTable from "../Articles/articles-table";
import { Cloudinary } from "@cloudinary/url-gen";
import { AdminDashboardSkeleton } from "components/Common/Skeleton";
import { HiViewGridAdd } from "react-icons/hi";
import { LOCAL_STORAGE } from "api/LOCALSTORAGE";
import { MdRateReview, MdAnalytics, MdTrendingUp } from "react-icons/md";
import { SiSimpleanalytics } from "react-icons/si";
import { FaDollarSign, FaUsers, FaFileAlt, FaChartLine } from "react-icons/fa";
import Table from "../sharedComponents/Table-v2";
import authStore from "mobx/AuthStore";
import axios from "axios";
import { handleRefreshToken } from "utils/auth";
import jmsApp from "api/jms";
import { observer } from "mobx-react-lite";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import Colors from "utils/colors";

// Modern Dashboard Styled Components
const ModernDashboardContainer = styled.div`
  padding: 2rem;
  background: #f8fafc;
  min-height: 100vh;
`;

const DashboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
`;

const WelcomeSection = styled.div`
  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: ${Colors.primary};
    margin-bottom: 0.5rem;
  }

  p {
    color: #64748b;
    font-size: 1.1rem;
  }
`;

const QuickActions = styled.div`
  display: flex;
  gap: 1rem;

  @media (max-width: 768px) {
    width: 100%;
    justify-content: space-between;
  }
`;

const ActionButton = styled.button`
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }
`;

const ModernStatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const ModernStatCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: ${props => props.color || Colors.primary};
  }
`;

const StatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const StatIcon = styled.div`
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  background: ${props => props.color || Colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    color: white;
    font-size: 1.5rem;
  }
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
`;

const StatTrend = styled.div`
  color: ${props => props.positive ? '#16a34a' : '#dc2626'};
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const DashboardCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
`;

const CardTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

// Enhanced imports for optimization
// import { AdminErrorBoundary, TableErrorBoundary } from "components/ErrorBoundary/SpecializedErrorBoundaries";
// import { useOptimizedFetch } from "hooks/useOptimizedFetch";
// import { usePerformanceMonitor } from "hooks/usePerformanceMonitor";

// import Table from "../sharedComponents/Table";

// import Table from "../sharedComponents/Table";

const AdminDashboard = observer(() => {
  const isAssignModalOpen = authStore.getOpenModalState();
  const navigate = useNavigate();
  const [data, setData] = useState([]);
  const [isFetchingData, setIsFetchingData] = useState(false);
  const columns = useMemo(() => heading, []);
  const courseColumns = useMemo(() => PlanHeading, []);
  const resourcesColumns = useMemo(() => courseHeading, []);
  const currentUser = LOCAL_STORAGE.user();
  const userRole = currentUser?.type ?? "";
  const handleData = async () => {
    try {
      const { data } = await jmsApp.get(ARTICLE_ENDPOINT.GET_ARTICLE());
      if (data?.success) {
        setData(data.article);
      }
    } catch (error) {
      if (error.response?.status === 401) {
        await handleRefreshToken();
        // await handleData()
      } else if (error.response) {
        // toast.error(error.response.data.error);
      }
    }
  };
  const handleLearning = async () => {
    setIsFetchingData(true);
    try {
      const { data: resources } = await jmsApp.get(COURSE.GET_COURSE());
      const { data: courses } = await jmsApp.get(USER_PLAN.GET_PLAN());
      setData({ resources, courses: courses });
    } catch (error) {
      if (error.response?.status === 401) {
        await handleRefreshToken();
        // await handleData()
      } else if (error.response) {
        // toast.error(error.response.data.error);
      }
    } finally {
      setIsFetchingData(false);
    }
  };

  const handleUpdateData = (updatedData) => {
    setData([...updatedData]);
  };

  useEffect(() => {
    if (userRole !== ADMIN_ROLES.E_LEARNING) {
      handleData();
      handleArticlePublished();
      handleArticleReview();
    } else {
      handleLearning();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const cld = new Cloudinary({
    cloud: {
      cloudName: process.env.REACT_APP_CLOUDINARY_NAME,
      apiKey: process.env.REACT_APP_CLOUDINARY_API_KEY,
      apiSecret: process.env.REACT_APP_CLOUDINARY_API_SECRET
    }
  });
  const downloadFile = async (articleURL, title, articleId) => {
    const myImage = cld.image(articleURL);
    await axios({
      url: myImage.publicID, //your url
      method: "GET",
      responseType: "blob" // important
    }).then((res) => {
      jmsApp
        .post(DOWNLOAD_ENDPOINT.DOWNLOADED_ARTICLE(), { articleId: articleId })
        .then(() => {
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(res.data);
          link.setAttribute("download", `${title}.pdf`);
          document.body.appendChild(link);
          link.click();
        })
        .catch((err) => {
          if (err.response?.status === 401) {
            handleRefreshToken();
          } else if (err.response) {
            // toast.error(err.response.data.error);
          }
        });
    });
  };

  function handleDownload(article) {
    const { articleUrl, title, articleId } = article;

    downloadFile(articleUrl, title, articleId);
  }
  function handleView(article) {
    navigate(`/admin/articles/${article.articleId}`);
  }

  return userRole !== ADMIN_ROLES.E_LEARNING ? (
    <ModernDashboardContainer>
      <DashboardHeader>
        <WelcomeSection>
          <h1>Learning Publics Admin</h1>
          <p>Welcome back, {currentUser?.name || 'Administrator'}! Here's your journal overview.</p>
        </WelcomeSection>
        <QuickActions>
          <ActionButton onClick={() => navigate("/admin/articles")}>
            <FaFileAlt />
            Articles
          </ActionButton>
          <ActionButton onClick={() => navigate("/admin/pricing")}>
            <FaDollarSign />
            Pricing
          </ActionButton>
          <ActionButton onClick={() => navigate("/admin/analytics")}>
            <MdAnalytics />
            Analytics
          </ActionButton>
        </QuickActions>
      </DashboardHeader>

      <ModernStatsGrid>
        <ModernStatCard color="#16a34a">
          <StatHeader>
            <div>
              <StatValue>{authStore.publishedArticles?.length || 0}</StatValue>
              <StatLabel>Published Articles</StatLabel>
              <StatTrend positive>
                <MdTrendingUp />
                +12% this month
              </StatTrend>
            </div>
            <StatIcon color="#16a34a">
              <SiSimpleanalytics />
            </StatIcon>
          </StatHeader>
        </ModernStatCard>

        <ModernStatCard color="#3b82f6">
          <StatHeader>
            <div>
              <StatValue>{authStore.reviewedArticles?.review?.length || 0}</StatValue>
              <StatLabel>Under Review</StatLabel>
              <StatTrend positive>
                <MdTrendingUp />
                +8% this week
              </StatTrend>
            </div>
            <StatIcon color="#3b82f6">
              <MdRateReview />
            </StatIcon>
          </StatHeader>
        </ModernStatCard>

        <ModernStatCard color="#f59e0b">
          <StatHeader>
            <div>
              <StatValue>{data?.length || 0}</StatValue>
              <StatLabel>Total Submissions</StatLabel>
              <StatTrend positive>
                <MdTrendingUp />
                +15% this month
              </StatTrend>
            </div>
            <StatIcon color="#f59e0b">
              <FaFileAlt />
            </StatIcon>
          </StatHeader>
        </ModernStatCard>

        <ModernStatCard color="#8b5cf6">
          <StatHeader>
            <div>
              <StatValue>4</StatValue>
              <StatLabel>Active Services</StatLabel>
              <StatTrend>
                No change
              </StatTrend>
            </div>
            <StatIcon color="#8b5cf6">
              <FaChartLine />
            </StatIcon>
          </StatHeader>
        </ModernStatCard>
      </ModernStatsGrid>

      <ArticleCont>
        <h3>Recent Articles</h3>
        <div className="recent">
          <div className="all">
            <button
              onClick={() => navigate("/admin/articles")}
              className=" transition-colors duration-300 ease-in-out hover:shadow-lg active:shadow-none "
            >
              <span>
                <HiViewGridAdd />
              </span>
              View All
            </button>
          </div>
          {data.length === 0 && (
            <div className=" w-full  flex items-center justify-center h-full font-bold text-3xl opacity-20 uppercase">
              No Article To Show
            </div>
          )}
          {data.length > 0 && (
            // <Table
            //   data={data.slice(0, 6)}
            //   columns={columns}
            //   color="none"
            //   heading={heading}
            //   handleUpdateData={handleUpdateData}
            //   hasStatus={true}
            //   actions={[
            //     { name: "download", action: handleDownload },
            //     { name: "view", action: handleView }
            //     // { name: "edit", action: handleEdit }
            //   ]}
            //   value=""
            //   user="article"
            // />
            <ArticlesTable
              data={data.slice(0, 6)}
              columns={columns}
              hasStatus={true}
              heading={heading}
              value=""
              handleUpdateData={handleUpdateData}
              // actions={[
              //   { name: "download", action: handleDownload },
              //   { name: "view", action: handleView },
              //   { name: "assign", action: handleAssign }
              // ]}
              user={userRole}
              // user={ADMIN_ROLES.SUPER}
            />
          )}
          </div>

          {userRole === ADMIN_ROLES.SUPER_ADMIN && <AddCategoreis />}
          {isAssignModalOpen && <ArticleAssign />}
        </DashboardCard>

        <DashboardCard>
          <CardTitle>
            <MdAnalytics />
            Quick Actions
          </CardTitle>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <ActionButton onClick={() => navigate("/admin/pricing")} style={{ width: '100%' }}>
              <FaDollarSign />
              Manage Pricing
            </ActionButton>

            <ActionButton onClick={() => navigate("/admin/analytics")} style={{ width: '100%' }}>
              <MdAnalytics />
              View Analytics
            </ActionButton>

            <ActionButton onClick={() => navigate("/admin/staffs")} style={{ width: '100%' }}>
              <FaUsers />
              Manage Staff
            </ActionButton>

            <ActionButton onClick={() => navigate("/admin/settings")} style={{ width: '100%' }}>
              <MdAnalytics />
              Settings
            </ActionButton>
          </div>

          <div style={{ marginTop: '2rem', padding: '1rem', background: '#f8fafc', borderRadius: '0.5rem' }}>
            <h4 style={{ color: Colors.primary, marginBottom: '0.5rem', fontSize: '1rem', fontWeight: '600' }}>
              System Status
            </h4>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', color: '#16a34a' }}>
              <div style={{ width: '8px', height: '8px', background: '#16a34a', borderRadius: '50%' }}></div>
              All systems operational
            </div>
          </div>
        </DashboardCard>
      </DashboardGrid>
    </ModernDashboardContainer>
  ) : (
    <AdminDashboardCont>
      <div className=" w-full flex items-center justify-between py-4 px-6 ">
        <h1 className=" text-xl font-bold uppercase">DashBoard</h1>
        {currentUser.name && (
          <div className=" flex text-xs lg:text-xl font-bold uppercase space-x-4 items-center justify-end ">
            <span>Welcome</span>
            <span className=" ">{currentUser?.name}</span>
          </div>
        )}
      </div>
      <div>
        <CardCont>
          <div className="card" key={authStore.publishedArticles?.articleId}>
            <StatusProp status={"published"}>
              {authStore.publishedArticles?.icon}
              <SiSimpleanalytics />
            </StatusProp>
            <div className="">
              <h4>Resources</h4>
              <span> {data?.resources?.course?.length}</span>
            </div>
          </div>
          <div className="card" key={authStore.reviewedArticles?.articleId}>
            <StatusProp status={"review"}>
              {authStore.reviewedArticles.review?.icon}
              <MdRateReview />
            </StatusProp>
            <div className="">
              <h4>Courses</h4>
              <span> {data?.courses?.plans?.length}</span>
            </div>
          </div>
        </CardCont>
        <div>
          {data?.resources?.course?.length > 0 && (
            <div>
              <h1 className=" w-full text-center font-bold text-2xl underline">
                Resources
              </h1>
              <Table
                data={data?.resources?.course.slice(0, 5)}
                columns={resourcesColumns}
                color="grey"
                heading={courseHeading}
                user={userRole}
                // handleDelete={(id) => handleDelete(id)}
                // viewCourses={(courses) => handleCourse(courses)}
                // handleEdit={(plan) => handleEdit(plan)}
              />
            </div>
          )}
          {data?.courses?.plans?.length > 0 && (
            <div>
              <h1 className=" w-full text-center font-bold text-2xl underline">
                Courses
              </h1>
              <Table
                data={data?.courses.plans.slice(0, 5)}
                columns={courseColumns}
                color="grey"
                heading={PlanHeading}
                user={userRole}
                // handleDelete={(id) => handleDelete(id)}
                // viewCourses={(courses) => handleCourse(courses)}
                // handleEdit={(plan) => handleEdit(plan)}
              />
            </div>
          )}
          {!data && !isFetchingData && (
            <div className=" w-full  flex items-center justify-center h-full font-bold text-3xl opacity-20 uppercase">
              No Data To Show
            </div>
          )}
          {isFetchingData && (
            <AdminDashboardSkeleton />
          )}
        </div>
      </div>
    </AdminDashboardCont>
  );
});

export default AdminDashboard;
