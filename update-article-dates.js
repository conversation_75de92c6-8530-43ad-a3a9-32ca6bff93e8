const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, 'learning-publics-server', '.env') });

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.DB_URI || '***************************************************************************/', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Article Schema (simplified)
const ArticleSchema = new mongoose.Schema({
  title: String,
  author: String,
  description: String,
  status: String,
  createdAt: Date,
  updatedAt: Date
}, { timestamps: true });

const Article = mongoose.model('Article', ArticleSchema);

// Function to update article dates
const updateArticleDates = async () => {
  try {
    // First, let's see what articles exist
    console.log('Fetching all articles...');
    const articles = await Article.find({}, { title: 1, author: 1, createdAt: 1, updatedAt: 1 });
    
    console.log('Found articles:');
    articles.forEach((article, index) => {
      console.log(`${index + 1}. Title: "${article.title}"`);
      console.log(`   Author: ${article.author}`);
      console.log(`   Created: ${article.createdAt}`);
      console.log(`   Updated: ${article.updatedAt}`);
      console.log('---');
    });

    // Update specific articles
    console.log('\nUpdating article dates...');

    // 1. Knowledge, Attitude and Practices -> 2015
    const knowledgeArticle = await Article.findOne({
      title: { $regex: /knowledge.*attitude.*practices/i }
    });

    if (knowledgeArticle) {
      const newDate2015 = new Date('2015-06-15T10:00:00.000Z'); // June 15, 2015
      await Article.updateOne(
        { _id: knowledgeArticle._id },
        { 
          $set: { 
            createdAt: newDate2015,
            updatedAt: newDate2015
          }
        }
      );
      console.log(`✅ Updated "${knowledgeArticle.title}" to 2015`);
    } else {
      console.log('❌ "Knowledge, Attitude and Practices" article not found');
    }

    // 2. Portrayal of Male and Female Characters in Romance Ebooks -> 2023
    const portrayalArticle = await Article.findOne({
      title: { $regex: /portrayal.*male.*female.*characters.*romance.*ebooks/i }
    });

    if (portrayalArticle) {
      const newDate2023 = new Date('2023-08-20T14:30:00.000Z'); // August 20, 2023
      await Article.updateOne(
        { _id: portrayalArticle._id },
        { 
          $set: { 
            createdAt: newDate2023,
            updatedAt: newDate2023
          }
        }
      );
      console.log(`✅ Updated "${portrayalArticle.title}" to 2023`);
    } else {
      console.log('❌ "Portrayal of Male and Female Characters in Romance Ebooks" article not found');
    }

    // Alternative search with more flexible patterns
    if (!knowledgeArticle || !portrayalArticle) {
      console.log('\nTrying alternative search patterns...');

      // Search for articles containing key words
      const allArticles = await Article.find({});

      for (const article of allArticles) {
        const title = article.title.toLowerCase();

        // Check for Knowledge, Attitude and Practices
        if (title.includes('knowledge') && title.includes('attitude') && title.includes('practices')) {
          const newDate2015 = new Date('2015-06-15T10:00:00.000Z');
          await Article.updateOne(
            { _id: article._id },
            {
              $set: {
                createdAt: newDate2015,
                updatedAt: newDate2015
              }
            }
          );
          console.log(`✅ Updated "${article.title}" to 2015 (alternative search)`);
        }

        // Check for Portrayal of Male and Female Characters
        if (title.includes('portrayal') && title.includes('male') && title.includes('female') && title.includes('romance')) {
          const newDate2023 = new Date('2023-08-20T14:30:00.000Z');
          await Article.updateOne(
            { _id: article._id },
            {
              $set: {
                createdAt: newDate2023,
                updatedAt: newDate2023
              }
            }
          );
          console.log(`✅ Updated "${article.title}" to 2023 (alternative search)`);
        }
      }
    }

    // Wait a moment for all updates to complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('\n✅ Article date updates completed!');
    
    // Verify the updates
    console.log('\nVerifying updates...');
    const updatedArticles = await Article.find({}, { title: 1, createdAt: 1, updatedAt: 1 });
    updatedArticles.forEach((article, index) => {
      const year = new Date(article.createdAt).getFullYear();
      console.log(`${index + 1}. "${article.title}" - ${year}`);
    });

  } catch (error) {
    console.error('Error updating article dates:', error);
  } finally {
    mongoose.connection.close();
  }
};

// Run the script
const main = async () => {
  await connectDB();
  await updateArticleDates();
};

main().catch(console.error);
