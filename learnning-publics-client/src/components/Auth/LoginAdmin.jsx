import * as Yup from "yup";

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from ".";
import {
  <PERSON>th<PERSON><PERSON>,
  <PERSON>th<PERSON>onte<PERSON>,
  Authcon<PERSON>er,
  <PERSON>ginContainer,
  LoginDiv,
  LoginFormContainer
} from "./style";
import {
  CREATE_ACCOUNT_ROUTE,
  DASHBOARD_ROUTE,
  FORGOT_PASSWORD_ROUTE
} from "routes";
import { Div, Img } from "globalStyles";
import { Link, useNavigate } from "react-router-dom";

import { AUTH_ENDPOINTS } from "api/ACTION";
import { AppLogo } from "utils/assets";
import TextInput from "components/TextInput";
import authStore from "mobx/AuthStore";
import bg from "../../assets/images/banner2.jpg";
import jmsApp from "api/jms";
import { observer } from "mobx-react-lite";
import { toast } from "react-hot-toast";
import { useEffect } from "react";
import { useFormik } from "formik";

function LoginComponent() {
  const navigate = useNavigate();
  const formik = useFormik({
    enableReinitialize: false,
    initialValues: {
      username: "",
      password: ""
    },
    onSubmit: async (info, helpers) => {
      authStore.loading();
      try {
        const { data } = await toast.promise(
          jmsApp.post(AUTH_ENDPOINTS.LOGIN(), info),
          {
            loading: "Please  wait, while we log you in...",
            error: (err) =>
              err.response?.data.error ?? "Login failed, try again later.",
            success: "You have been successfully logged in."
          }
        );

        const { token, refreshToken } = data;
        // navigate(`${DASHBOARD_ROUTE}`);
        if (data.success) {
          authStore.loading();
          authStore.setCurrentStep("login");
          authStore.setUserEmail(data.user.email);
          localStorage.setItem("isUserDetails", JSON.stringify(data.user));
          localStorage.setItem("token", JSON.stringify(token));
          localStorage.setItem("refreshToken", JSON.stringify(refreshToken));
          authStore.setInitialAuth(data.authorization);
          helpers.resetForm();
        }
      } catch (err) {
        if (err.response.data) {
          // toast.error(err.response.data.error);
        } else {
          // toast.error(err.message);
        }
      }
    },
    validationSchema: Yup.object({
      username: Yup.string().max(50).required().label("Username"),
      password: Yup.string()
        .min(8)
        .required("Password is required.")
        .label("Password")
    })
  });
  useEffect(() => {
    const status = authStore.loggedInStatus();
    if (status) {
      navigate(`${DASHBOARD_ROUTE}`);
    }
  }, [navigate]);

  return (
    <Authcontainer style={{ backgroundImage: `url(${bg})` }}>
      <AuthContent>
        <Link to="/">
          <Img src={AppLogo} alt="LPJ" />
        </Link>
        <LoginContainer>
          <LoginDiv onSubmit={formik.handleSubmit}>
            <AuthCard>
              <LoginFormContainer>
                <AuthHeader text="LogIn" />
                <TextInput
                  label="Username"
                  name="username"
                  id="username"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.username}
                  error={formik.errors.username}
                  visible={formik.touched.username}
                  placeholder="e.g iamprincekuro1"
                  mt="0.5rem"
                />
                <TextInput
                  label="Password"
                  type="password"
                  name="password"
                  id="password"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.password}
                  error={formik.errors.password}
                  visible={formik.touched.password}
                  placeholder="Enter your password"
                />
                <Div width="100%" display="flex" justify="flex-end">
                  <AuthBottomLink
                    link={FORGOT_PASSWORD_ROUTE}
                    text=""
                    linktext="Forgot Password"
                  />
                </Div>
                <AuthButton title="Login" />
                {/* <AuthButton title={authStore.isLoading ? "Loading..." : "Login"} /> */}
                <AuthBottomLink
                  link={CREATE_ACCOUNT_ROUTE}
                  text="Don't have an account?"
                  linktext="Create Account"
                />
              </LoginFormContainer>
            </AuthCard>
          </LoginDiv>
        </LoginContainer>
      </AuthContent>
    </Authcontainer>
  );
}

export default observer(LoginComponent);
