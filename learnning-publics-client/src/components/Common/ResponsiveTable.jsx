import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { useMediaQuery } from 'hooks/use-media-query/use-media-query';

/**
 * Mobile-First Responsive Table Component
 * Automatically switches between table and card layouts based on screen size
 * Uses Learning Publics brand colors and modern design patterns
 */

const ResponsiveTable = ({ 
  data = [], 
  columns = [], 
  actions = [], 
  title = "",
  emptyMessage = "No data available",
  loading = false,
  onRowClick = null,
  className = ""
}) => {
  const [expandedRows, setExpandedRows] = useState(new Set());
  const isMobile = useMediaQuery('(max-width: 768px)');
  
  const toggleRowExpansion = (rowId) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(rowId)) {
      newExpanded.delete(rowId);
    } else {
      newExpanded.add(rowId);
    }
    setExpandedRows(newExpanded);
  };

  // Mobile Card Layout
  const MobileCardLayout = () => (
    <div className="space-y-4">
      {data.map((row, index) => {
        const rowId = row.id || index;
        const isExpanded = expandedRows.has(rowId);
        
        return (
          <motion.div
            key={rowId}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden"
          >
            {/* Card Header - Always Visible */}
            <div 
              className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => {
                if (onRowClick) onRowClick(row);
                toggleRowExpansion(rowId);
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  {/* Primary column (usually title/name) */}
                  {columns[0] && (
                    <h3 className="text-lg font-semibold text-blue-900 truncate">
                      {row[columns[0].accessor]}
                    </h3>
                  )}
                  {/* Secondary column (usually subtitle/description) */}
                  {columns[1] && (
                    <p className="text-sm text-gray-600 truncate mt-1">
                      {row[columns[1].accessor]}
                    </p>
                  )}
                </div>
                
                {/* Expand/Collapse Icon */}
                <div className="ml-4 flex-shrink-0">
                  {isExpanded ? (
                    <ChevronUpIcon className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronDownIcon className="w-5 h-5 text-gray-400" />
                  )}
                </div>
              </div>
            </div>

            {/* Expanded Content */}
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="border-t border-gray-100"
                >
                  <div className="p-4 space-y-3">
                    {/* Additional columns */}
                    {columns.slice(2).map((column) => (
                      <div key={column.accessor} className="flex justify-between items-start">
                        <span className="text-sm font-medium text-gray-700 capitalize">
                          {column.Header}:
                        </span>
                        <span className="text-sm text-gray-900 text-right max-w-[60%]">
                          {row[column.accessor]}
                        </span>
                      </div>
                    ))}
                    
                    {/* Actions */}
                    {actions.length > 0 && (
                      <div className="pt-3 border-t border-gray-100">
                        <div className="flex flex-wrap gap-2">
                          {actions.map((action, actionIndex) => (
                            <button
                              key={actionIndex}
                              onClick={(e) => {
                                e.stopPropagation();
                                action.onClick(row);
                              }}
                              className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-red-600 text-white hover:bg-red-700 active:bg-red-800 transition-colors touch-manipulation min-h-[44px]"
                            >
                              {action.icon && <span className="mr-2">{action.icon}</span>}
                              {action.label}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        );
      })}
    </div>
  );

  // Desktop Table Layout
  const DesktopTableLayout = () => (
    <div className="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-200">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {columns.map((column) => (
              <th
                key={column.accessor}
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                {column.Header}
              </th>
            ))}
            {actions.length > 0 && (
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Actions
              </th>
            )}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((row, index) => (
            <motion.tr
              key={row.id || index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className={`hover:bg-gray-50 transition-colors ${
                onRowClick ? 'cursor-pointer' : ''
              }`}
              onClick={() => onRowClick && onRowClick(row)}
            >
              {columns.map((column) => (
                <td
                  key={column.accessor}
                  className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                >
                  {row[column.accessor]}
                </td>
              ))}
              {actions.length > 0 && (
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    {actions.map((action, actionIndex) => (
                      <button
                        key={actionIndex}
                        onClick={(e) => {
                          e.stopPropagation();
                          action.onClick(row);
                        }}
                        className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-red-600 text-white hover:bg-red-700 active:bg-red-800 transition-colors"
                      >
                        {action.icon && <span className="mr-2">{action.icon}</span>}
                        {action.label}
                      </button>
                    ))}
                  </div>
                </td>
              )}
            </motion.tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  // Loading State
  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className="bg-white rounded-lg shadow-md border border-gray-200 p-4 animate-pulse"
          >
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  // Empty State
  if (data.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8 text-center">
        <p className="text-gray-500 text-lg">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {title && (
        <h2 className="text-xl font-bold text-blue-900 mb-4">{title}</h2>
      )}
      
      {isMobile ? <MobileCardLayout /> : <DesktopTableLayout />}
    </div>
  );
};

export default ResponsiveTable;
