import React from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { MainContainer } from 'globalStyles';
import SEOHead from 'components/SEO/SEOHead';

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 0;
`;

const ContentWrapper = styled(MainContainer)`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 1rem;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #64748b;
  max-width: 800px;
  margin: 0 auto;
`;

const BoardSection = styled.section`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const SectionTitle = styled.h2`
  font-size: 1.8rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 2rem;
  text-align: center;
  border-bottom: 2px solid #fbbf24;
  padding-bottom: 0.5rem;
`;

const MembersGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
`;

const MemberCard = styled.div`
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;

const MemberPhoto = styled.div`
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
`;

const MemberName = styled.h3`
  font-size: 1.3rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 0.5rem;
`;

const MemberTitle = styled.p`
  font-weight: 600;
  color: #059669;
  margin-bottom: 0.5rem;
`;

const MemberAffiliation = styled.p`
  color: #64748b;
  margin-bottom: 1rem;
  font-size: 0.95rem;
`;

const MemberExpertise = styled.div`
  margin-bottom: 1rem;
`;

const ExpertiseLabel = styled.span`
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
`;

const ExpertiseTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
`;

const ExpertiseTag = styled.span`
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 500;
`;

const MemberContact = styled.div`
  font-size: 0.9rem;
  color: #64748b;
`;

const EditorialBoardPage = () => {
  const editorInChief = {
    name: "Dr. Sarah Johnson",
    title: "Editor-in-Chief",
    affiliation: "University of California, Davis - Department of Environmental Science",
    expertise: ["Environmental Policy", "Climate Change", "Sustainable Agriculture"],
    email: "<EMAIL>",
    initials: "SJ"
  };

  const associateEditors = [
    {
      name: "Prof. Michael Chen",
      title: "Associate Editor",
      affiliation: "Cornell University - School of Agriculture",
      expertise: ["Crop Science", "Plant Genetics", "Biotechnology"],
      email: "<EMAIL>",
      initials: "MC"
    },
    {
      name: "Dr. Elena Rodriguez",
      title: "Associate Editor",
      affiliation: "University of São Paulo - Environmental Studies",
      expertise: ["Biodiversity", "Conservation Biology", "Ecosystem Services"],
      email: "<EMAIL>",
      initials: "ER"
    },
    {
      name: "Prof. James Wilson",
      title: "Associate Editor",
      affiliation: "Oxford University - Department of Geography",
      expertise: ["Land Use", "Remote Sensing", "GIS Applications"],
      email: "<EMAIL>",
      initials: "JW"
    }
  ];

  const editorialBoard = [
    {
      name: "Dr. Amara Okafor",
      title: "Editorial Board Member",
      affiliation: "University of Ibadan - Agricultural Economics",
      expertise: ["Agricultural Economics", "Food Security", "Rural Development"],
      email: "<EMAIL>",
      initials: "AO"
    },
    {
      name: "Prof. Liu Wei",
      title: "Editorial Board Member",
      affiliation: "Beijing Agricultural University - Soil Science",
      expertise: ["Soil Chemistry", "Nutrient Management", "Precision Agriculture"],
      email: "<EMAIL>",
      initials: "LW"
    },
    {
      name: "Dr. Maria Santos",
      title: "Editorial Board Member",
      affiliation: "University of Barcelona - Marine Biology",
      expertise: ["Marine Ecosystems", "Aquaculture", "Coastal Management"],
      email: "<EMAIL>",
      initials: "MS"
    },
    {
      name: "Prof. David Thompson",
      title: "Editorial Board Member",
      affiliation: "University of Melbourne - Forestry",
      expertise: ["Forest Management", "Carbon Sequestration", "Agroforestry"],
      email: "<EMAIL>",
      initials: "DT"
    },
    {
      name: "Dr. Fatima Al-Zahra",
      title: "Editorial Board Member",
      affiliation: "American University of Beirut - Water Resources",
      expertise: ["Water Management", "Irrigation", "Drought Resilience"],
      email: "<EMAIL>",
      initials: "FA"
    },
    {
      name: "Prof. Robert Anderson",
      title: "Editorial Board Member",
      affiliation: "University of Edinburgh - Renewable Energy",
      expertise: ["Bioenergy", "Renewable Resources", "Energy Policy"],
      email: "<EMAIL>",
      initials: "RA"
    }
  ];

  const MemberComponent = ({ member }) => (
    <MemberCard>
      <MemberPhoto>{member.initials}</MemberPhoto>
      <MemberName>{member.name}</MemberName>
      <MemberTitle>{member.title}</MemberTitle>
      <MemberAffiliation>{member.affiliation}</MemberAffiliation>
      <MemberExpertise>
        <ExpertiseLabel>Research Areas:</ExpertiseLabel>
        <ExpertiseTags>
          {member.expertise.map((area, index) => (
            <ExpertiseTag key={index}>{area}</ExpertiseTag>
          ))}
        </ExpertiseTags>
      </MemberExpertise>
      <MemberContact>
        📧 {member.email}
      </MemberContact>
    </MemberCard>
  );

  return (
    <>
      <SEOHead
        title="Editorial Board - Expert Academic Leadership"
        description="Meet the distinguished editorial board of Learning Publics Journal. Our international team of experts ensures high-quality peer review and academic excellence."
        keywords="editorial board, academic editors, peer review, journal leadership, research experts"
        canonicalUrl="/editorial-board"
      />
      
      <PageContainer>
        <ContentWrapper>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Header>
              <Title>Editorial Board</Title>
              <Subtitle>
                Our distinguished international editorial board comprises leading experts in agriculture and environmental studies, 
                ensuring the highest standards of academic excellence and rigorous peer review.
              </Subtitle>
            </Header>

            <BoardSection>
              <SectionTitle>Editor-in-Chief</SectionTitle>
              <MembersGrid>
                <MemberComponent member={editorInChief} />
              </MembersGrid>
            </BoardSection>

            <BoardSection>
              <SectionTitle>Associate Editors</SectionTitle>
              <MembersGrid>
                {associateEditors.map((member, index) => (
                  <MemberComponent key={index} member={member} />
                ))}
              </MembersGrid>
            </BoardSection>

            <BoardSection>
              <SectionTitle>Editorial Board Members</SectionTitle>
              <MembersGrid>
                {editorialBoard.map((member, index) => (
                  <MemberComponent key={index} member={member} />
                ))}
              </MembersGrid>
            </BoardSection>

            <BoardSection>
              <SectionTitle>Join Our Editorial Team</SectionTitle>
              <div style={{ textAlign: 'center', color: '#374151', lineHeight: '1.7' }}>
                <p style={{ marginBottom: '1rem' }}>
                  We are always looking for qualified experts to join our editorial board. If you are interested in 
                  contributing to the advancement of agricultural and environmental research, we would love to hear from you.
                </p>
                <p>
                  <strong>Requirements:</strong> PhD in relevant field, active research record, peer review experience
                </p>
                <p style={{ marginTop: '1rem' }}>
                  <strong>Contact:</strong> <a href="mailto:<EMAIL>" style={{ color: '#1e40af' }}><EMAIL></a>
                </p>
              </div>
            </BoardSection>
          </motion.div>
        </ContentWrapper>
      </PageContainer>
    </>
  );
};

export default EditorialBoardPage;
