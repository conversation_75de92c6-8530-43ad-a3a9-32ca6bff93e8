import slugify from 'slugify';

/**
 * SEO utility functions for the frontend
 */

export const generateSlug = (title) => {
  return slugify(title, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });
};

export const truncateDescription = (text, maxLength = 160) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

export const generateArticleStructuredData = (article) => {
  return {
    "@context": "https://schema.org",
    "@type": "ScholarlyArticle",
    "headline": article.title,
    "description": article.metaDescription || article.description,
    "author": {
      "@type": "Person",
      "name": article.author,
      "email": article.email
    },
    "datePublished": article.createdAt,
    "dateModified": article.updatedAt,
    "publisher": {
      "@type": "Organization",
      "name": "Learning Publics",
      "url": process.env.REACT_APP_FRONTEND_URL || "https://learningpublics.com"
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${process.env.REACT_APP_FRONTEND_URL || 'https://learningpublics.com'}/articles/${article.slug}`
    },
    "keywords": article.keywords?.join(', '),
    "articleSection": article.category?.name,
    "inLanguage": "en-US",
    "isAccessibleForFree": true,
    "creativeWorkStatus": "Published"
  };
};

export const generateBreadcrumbStructuredData = (breadcrumbs) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": `${process.env.REACT_APP_FRONTEND_URL || 'https://learningpublics.com'}${crumb.url}`
    }))
  };
};

export const generateJournalStructuredData = (journal) => {
  return {
    "@context": "https://schema.org",
    "@type": "Periodical",
    "name": journal.title,
    "description": journal.metaDescription || journal.description,
    "publisher": {
      "@type": "Organization",
      "name": "Learning Publics"
    },
    "url": `${process.env.REACT_APP_FRONTEND_URL || 'https://learningpublics.com'}/journals/${journal.slug}`,
    "issn": journal.issn,
    "inLanguage": "en-US"
  };
};

export const generateCategoryStructuredData = (category, articles) => {
  return {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": `${category.name} Articles`,
    "description": category.metaDescription || category.description,
    "url": `${process.env.REACT_APP_FRONTEND_URL || 'https://learningpublics.com'}/articles/category/${category.slug}`,
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": articles.length,
      "itemListElement": articles.map((article, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "ScholarlyArticle",
          "headline": article.title,
          "url": `${process.env.REACT_APP_FRONTEND_URL || 'https://learningpublics.com'}/articles/${article.slug}`
        }
      }))
    }
  };
};

export const generateOpenGraphTags = (article) => {
  const baseUrl = process.env.REACT_APP_FRONTEND_URL || 'https://learningpublics.com';
  
  return {
    'og:title': article.title,
    'og:description': article.metaDescription || article.description,
    'og:type': 'article',
    'og:url': `${baseUrl}/articles/${article.slug}`,
    'og:site_name': 'Learning Publics',
    'article:author': article.author,
    'article:published_time': article.createdAt,
    'article:modified_time': article.updatedAt,
    'article:section': article.category?.name,
    'article:tag': article.keywords?.join(',')
  };
};

export const generateTwitterCardTags = (article) => {
  return {
    'twitter:card': 'summary_large_image',
    'twitter:title': article.title,
    'twitter:description': article.metaDescription || article.description,
    'twitter:site': '@LearningPublics'
  };
};

export const generateAcademicMetaTags = (article) => {
  const baseUrl = process.env.REACT_APP_FRONTEND_URL || 'https://learningpublics.com';
  const tags = {
    // Google Scholar and academic search engines
    'citation_title': article.title,
    'citation_author': article.author,
    'citation_publication_date': new Date(article.createdAt).toISOString().split('T')[0],
    'citation_journal_title': 'Learning Publics Journal of Agriculture and Environmental Studies',
    'citation_issn': '2026-5654',
    'citation_pdf_url': `${baseUrl}/api/v1/author/articles/pdf/${article.slug}`,
    'citation_abstract_html_url': `${baseUrl}/articles/${article.slug}`,
    'citation_language': 'en',
    'citation_keywords': article.keywords?.join('; ') || '',

    // Dublin Core metadata
    'DC.Title': article.title,
    'DC.Creator': article.author,
    'DC.Subject': article.keywords?.join(', ') || '',
    'DC.Description': article.description || '',
    'DC.Publisher': 'Learning Publics',
    'DC.Date': new Date(article.createdAt).toISOString().split('T')[0],
    'DC.Type': 'Text.Serial.Journal',
    'DC.Format': 'application/pdf',
    'DC.Identifier': article.doi || `${baseUrl}/articles/${article.slug}`,
    'DC.Language': 'en',
    'DC.Rights': 'Creative Commons Attribution 4.0 International License',

    // PRISM metadata for academic publishers
    'prism.publicationName': 'Learning Publics Journal of Agriculture and Environmental Studies',
    'prism.issn': '2026-5654',
    'prism.publicationDate': new Date(article.createdAt).toISOString().split('T')[0],
    'prism.copyright': 'Learning Publics',
    'prism.rightsAgent': '<EMAIL>',

    // Additional academic metadata
    'bepress_citation_title': article.title,
    'bepress_citation_author': article.author,
    'bepress_citation_journal_title': 'Learning Publics Journal',
    'bepress_citation_pdf_url': `${baseUrl}/api/v1/author/articles/pdf/${article.slug}`,
    'bepress_citation_abstract_html_url': `${baseUrl}/articles/${article.slug}`,

    // Highwire Press tags (used by many academic publishers)
    'citation_journal_abbrev': 'Learn. Publics J.',
    'citation_publisher': 'Learning Publics',
    'citation_online_date': new Date(article.createdAt).toISOString().split('T')[0]
  };

  // Add volume and issue if available
  if (article.volume) {
    tags['citation_volume'] = article.volume;
    tags['prism.volume'] = article.volume;
    tags['bepress_citation_volume'] = article.volume;
  }

  if (article.issue) {
    tags['citation_issue'] = article.issue;
    tags['prism.number'] = article.issue;
    tags['bepress_citation_issue'] = article.issue;
  }

  if (article.pages) {
    const pageRange = article.pages.split('-');
    tags['citation_firstpage'] = pageRange[0];
    if (pageRange[1]) {
      tags['citation_lastpage'] = pageRange[1];
    }
    tags['prism.startingPage'] = pageRange[0];
    if (pageRange[1]) {
      tags['prism.endingPage'] = pageRange[1];
    }
  }

  if (article.doi) {
    tags['citation_doi'] = article.doi;
    tags['prism.doi'] = article.doi;
    tags['bepress_citation_doi'] = article.doi;
  }

  return tags;
};

export const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export const generateCanonicalUrl = (path) => {
  const baseUrl = process.env.REACT_APP_FRONTEND_URL || 'https://learningpublics.com';
  return `${baseUrl}${path}`;
};

export const extractKeywords = (text, maxKeywords = 10) => {
  if (!text) return [];
  
  // Simple keyword extraction - remove common words and extract meaningful terms
  const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'];
  
  const words = text.toLowerCase()
    .replace(/[^\w\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 3 && !commonWords.includes(word));
  
  // Count word frequency
  const wordCount = {};
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });
  
  // Sort by frequency and return top keywords
  return Object.entries(wordCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, maxKeywords)
    .map(([word]) => word);
};

export const validateSlug = (slug) => {
  // Check if slug is valid (lowercase, no spaces, no special chars except hyphens)
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
};

export const generateMetaDescription = (content, maxLength = 160) => {
  if (!content) return '';
  
  // Remove HTML tags and extra whitespace
  const cleanContent = content
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim();
  
  if (cleanContent.length <= maxLength) {
    return cleanContent;
  }
  
  // Find the last complete sentence within the limit
  const truncated = cleanContent.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  
  if (lastSentence > maxLength * 0.7) {
    return cleanContent.substring(0, lastSentence + 1);
  }
  
  // If no good sentence break, truncate at word boundary
  const lastSpace = truncated.lastIndexOf(' ');
  return cleanContent.substring(0, lastSpace) + '...';
};
