import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import ArticleCard from 'components/Articles/ArticleCard';
import LoadingSpinner from 'components/Common/LoadingSpinner';
import ErrorMessage from 'components/Common/ErrorMessage';
import { generateCanonicalUrl } from 'utils/seo';

const ArticlesSearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');

  const query = searchParams.get('q') || '';

  useEffect(() => {
    setSearchQuery(query);
    if (query) {
      searchArticles(query);
    }
  }, [query]);

  const searchArticles = async (searchTerm) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`/api/articles/search?q=${encodeURIComponent(searchTerm)}`);
      
      if (!response.ok) {
        throw new Error('Search failed');
      }
      
      const data = await response.json();
      setArticles(data.articles);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setSearchParams({ q: searchQuery.trim() });
    }
  };

  const canonicalUrl = generateCanonicalUrl('/articles/search');
  const pageTitle = query 
    ? `Search Results for "${query}" | Learning Publics`
    : 'Search Articles | Learning Publics';
  
  const pageDescription = query
    ? `Search results for "${query}" in our academic articles collection.`
    : 'Search through our collection of academic articles and research papers.';

  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <link rel="canonical" href={canonicalUrl} />
        <meta name="robots" content="noindex, follow" />
      </Helmet>

      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Search Articles
          </h1>
          
          <form onSubmit={handleSearch} className="max-w-2xl">
            <div className="flex gap-2">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search articles..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Search
              </button>
            </div>
          </form>
        </div>

        {query && (
          <div className="mb-6">
            <p className="text-gray-600">
              {loading ? 'Searching...' : `Search results for "${query}"`}
              {!loading && articles.length > 0 && ` (${articles.length} results)`}
            </p>
          </div>
        )}

        {loading && <LoadingSpinner />}
        {error && <ErrorMessage message={error} />}

        {!loading && !error && query && (
          <>
            {articles.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {articles.map((article) => (
                  <ArticleCard key={article._id} article={article} />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  No results found
                </h3>
                <p className="text-gray-600 mb-4">
                  No articles found for "{query}". Try different keywords or browse our categories.
                </p>
              </div>
            )}
          </>
        )}

        {!query && (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Start your search
            </h3>
            <p className="text-gray-600">
              Enter keywords to search through our academic articles collection.
            </p>
          </div>
        )}
      </div>
    </>
  );
};

export default ArticlesSearchPage;
