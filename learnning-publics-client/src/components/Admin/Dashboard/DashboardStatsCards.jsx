import React from 'react';
import { motion } from 'framer-motion';
import { Card<PERSON>ont, StatusProp } from './Dashboard.style';
import { MdRateReview } from 'react-icons/md';
import { SiSimpleanalytics } from 'react-icons/si';
import { HiViewGridAdd } from 'react-icons/hi';
import authStore from 'mobx/AuthStore';
import { observer } from 'mobx-react-lite';
import { fadeIn, staggerContainer } from 'design-system/animations';

/**
 * Optimized Dashboard Statistics Cards Component
 * Displays article counts and statistics with Learning Publics branding
 */
const DashboardStatsCards = observer(({ data, userRole }) => {
  const statsData = [
    {
      id: 'published',
      title: 'PUBLISHED',
      count: authStore.publishedArticles?.length || 0,
      status: 'published',
      icon: <SiSimpleanalytics />,
      color: '#dc2626', // Learning Publics red
      description: 'Published articles'
    },
    {
      id: 'review',
      title: 'IN REVIEW',
      count: authStore.reviewedArticles?.review?.length || 0,
      status: 'review',
      icon: <MdRateReview />,
      color: '#1e3a8a', // Learning Publics blue
      description: 'Articles under review'
    },
    {
      id: 'resources',
      title: 'RESOURCES',
      count: data?.resources?.course?.length || 0,
      status: 'published',
      icon: <HiViewGridAdd />,
      color: '#dc2626',
      description: 'Available resources'
    },
    {
      id: 'courses',
      title: 'COURSES',
      count: data?.courses?.plans?.length || 0,
      status: 'review',
      icon: <SiSimpleanalytics />,
      color: '#1e3a8a',
      description: 'Available courses'
    }
  ];

  return (
    <motion.div
      variants={staggerContainer}
      initial="initial"
      animate="animate"
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"
    >
      {statsData.map((stat, index) => (
        <motion.div
          key={stat.id}
          variants={fadeIn}
          custom={index}
          whileHover={{ 
            scale: 1.02,
            boxShadow: "0 10px 25px rgba(0,0,0,0.1)"
          }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <StatsCard stat={stat} />
        </motion.div>
      ))}
    </motion.div>
  );
});

/**
 * Individual Statistics Card Component
 */
const StatsCard = ({ stat }) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden border-l-4 hover:shadow-lg transition-shadow duration-200"
         style={{ borderLeftColor: stat.color }}>
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 uppercase tracking-wide">
              {stat.title}
            </p>
            <p className="text-3xl font-bold text-gray-900 mt-2">
              {stat.count.toLocaleString()}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              {stat.description}
            </p>
          </div>
          <div 
            className="flex-shrink-0 p-3 rounded-full"
            style={{ 
              backgroundColor: `${stat.color}15`,
              color: stat.color
            }}
          >
            <div className="text-2xl">
              {stat.icon}
            </div>
          </div>
        </div>
        
        {/* Progress indicator */}
        <div className="mt-4">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Status</span>
            <span className="capitalize">{stat.status}</span>
          </div>
          <div className="mt-1 w-full bg-gray-200 rounded-full h-1">
            <div 
              className="h-1 rounded-full transition-all duration-300"
              style={{ 
                backgroundColor: stat.color,
                width: stat.count > 0 ? '100%' : '0%'
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Legacy CardCont wrapper for backward compatibility
 */
export const LegacyStatsCards = observer(() => {
  return (
    <CardCont>
      <div className="card" key={authStore.publishedArticles?.articleId}>
        <StatusProp status={"published"}>
          {authStore.publishedArticles?.icon}
          <SiSimpleanalytics />
        </StatusProp>
        <div className="content">
          <h4>PUBLISHED</h4>
          <span> {authStore.publishedArticles?.length}</span>
        </div>
      </div>
      <div className="card" key={authStore.reviewedArticles?.articleId}>
        <StatusProp status={"review"}>
          {authStore.reviewedArticles.review?.icon}
          <MdRateReview />
        </StatusProp>
        <div className="content">
          <h4>REVIEW</h4>
          <span> {authStore.reviewedArticles.review?.length}</span>
        </div>
      </div>
    </CardCont>
  );
});

/**
 * Enhanced Stats Summary Component with animations
 */
export const StatsOverview = observer(({ data }) => {
  const totalArticles = (authStore.publishedArticles?.length || 0) + 
                       (authStore.reviewedArticles?.review?.length || 0);
  
  const totalResources = (data?.resources?.course?.length || 0) + 
                        (data?.courses?.plans?.length || 0);

  const stats = [
    {
      label: 'Total Articles',
      value: totalArticles,
      change: '+12%',
      trend: 'up',
      color: '#dc2626'
    },
    {
      label: 'Total Resources',
      value: totalResources,
      change: '+8%',
      trend: 'up',
      color: '#1e3a8a'
    },
    {
      label: 'Active Users',
      value: '1,234',
      change: '+5%',
      trend: 'up',
      color: '#059669'
    },
    {
      label: 'Monthly Views',
      value: '45.2K',
      change: '+15%',
      trend: 'up',
      color: '#7c3aed'
    }
  ];

  return (
    <motion.div
      className="bg-white rounded-lg shadow-md p-6 mb-6"
      {...fadeIn}
    >
      <h3 className="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
        Learning Publics Journal Overview
      </h3>
      
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.label}
            className="text-center p-4 rounded-lg bg-gray-50"
            whileHover={{ scale: 1.05 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <div 
              className="text-2xl font-bold mb-1"
              style={{ color: stat.color }}
            >
              {stat.value}
            </div>
            <div className="text-sm text-gray-600 mb-2">
              {stat.label}
            </div>
            <div className="flex items-center justify-center text-xs">
              <span 
                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  stat.trend === 'up' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {stat.trend === 'up' ? '↗' : '↘'} {stat.change}
              </span>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
});

export default DashboardStatsCards;
