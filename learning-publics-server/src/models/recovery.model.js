const { Schema, model } = require("mongoose");

const RecoveryLinkSchema = new Schema(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: "Account",
      require: true,
    },
    expiryTime: {
      type: Date,
      require: true,
    },
    uniqueString: {
      type: String,
      required: true,
    },
  },
  {timestamps: true},
);
const RecoveryLinkModel = model("RecoveryLink", RecoveryLinkSchema);
module.exports = RecoveryLinkModel;