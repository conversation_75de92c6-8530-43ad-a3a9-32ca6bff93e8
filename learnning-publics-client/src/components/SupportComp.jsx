import React, { useState } from "react";
import styled from "styled-components";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import Colors from "utils/colors";
import { PAYSTACK_CONFIG } from "config/paystack";

// Styled components
const SupportSection = styled.section`
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 0;
  padding-top: 8rem;

  @media (max-width: 768px) {
    padding-top: 6rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
`;

const HeroSection = styled.div`
  text-align: center;
  margin-bottom: 4rem;
  background: white;
  border-radius: 1rem;
  padding: 3rem 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h1`
  font-size: 3rem;
  font-weight: 700;
  color: ${Colors.primary};
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const Subtitle = styled.p`
  font-size: 1.25rem;
  color: #64748b;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 2rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: ${Colors.secondary};
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const DonationContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
`;

const DonationOption = styled(motion.div)`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: ${Colors.secondary};
  }

  ${props => props.featured && `
    border-color: ${Colors.secondary};
    transform: scale(1.05);

    &::before {
      content: 'Most Popular';
      position: absolute;
      top: 1rem;
      right: -2rem;
      background: ${Colors.secondary};
      color: white;
      padding: 0.5rem 3rem;
      font-size: 0.75rem;
      font-weight: 600;
      transform: rotate(45deg);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  `}
`;

const TierIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`;

const TierName = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 1rem;
`;

const Amount = styled.div`
  font-size: 2.5rem;
  font-weight: 700;
  color: ${Colors.secondary};
  margin-bottom: 0.5rem;
`;

const Period = styled.span`
  font-size: 1rem;
  color: #64748b;
  font-weight: 400;
`;

const Description = styled.p`
  font-size: 1rem;
  color: #64748b;
  line-height: 1.5;
  margin: 1.5rem 0;
`;

const FeatureList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 1.5rem 0;
`;

const Feature = styled.li`
  padding: 0.5rem 0;
  color: #374151;
  font-size: 0.875rem;

  &::before {
    content: '✓';
    color: ${Colors.secondary};
    font-weight: bold;
    margin-right: 0.5rem;
  }
`;

const ImpactText = styled.p`
  background: linear-gradient(135deg, ${Colors.secondary}, ${Colors.primary});
  color: white;
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  margin: 1rem 0;
`;

const DonateButton = styled.button`
  width: 100%;
  background: linear-gradient(135deg, ${Colors.secondary}, ${Colors.primary});
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ImpactSection = styled.div`
  background: linear-gradient(135deg, ${Colors.primary}, ${Colors.secondary});
  color: white;
  padding: 4rem 2rem;
  border-radius: 1rem;
  text-align: center;
  margin: 3rem 0;
`;

const ImpactGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`;

const ImpactCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 1rem;
  backdrop-filter: blur(10px);
`;

const ImpactNumber = styled.div`
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
`;

const ImpactLabel = styled.div`
  font-size: 1rem;
  opacity: 0.9;
`;

const TestimonialSection = styled.div`
  background: white;
  padding: 3rem 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin: 3rem 0;
`;

const TestimonialGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`;

const TestimonialCard = styled.div`
  background: #f8fafc;
  padding: 2rem;
  border-radius: 1rem;
  border-left: 4px solid ${Colors.secondary};
`;

const TestimonialText = styled.p`
  font-style: italic;
  color: #374151;
  margin-bottom: 1rem;
  line-height: 1.6;
`;

const TestimonialAuthor = styled.div`
  font-weight: 600;
  color: ${Colors.primary};
`;

const TestimonialRole = styled.div`
  font-size: 0.875rem;
  color: #64748b;
`;

const FAQSection = styled.div`
  background: white;
  padding: 3rem 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin: 3rem 0;
`;

const FAQItem = styled.div`
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem 0;

  &:last-child {
    border-bottom: none;
  }
`;

const FAQQuestion = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 0.5rem;
`;

const FAQAnswer = styled.p`
  color: #64748b;
  line-height: 1.6;
`;

const ContactSection = styled.div`
  background: white;
  padding: 3rem 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin: 3rem 0;
`;

const ContactForm = styled.form`
  display: grid;
  gap: 1.5rem;
  max-width: 600px;
  margin: 0 auto;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const Label = styled.label`
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 0.5rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${Colors.secondary};
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const TextArea = styled.textarea`
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  min-height: 120px;
  resize: vertical;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${Colors.secondary};
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  background: white;
  transition: border-color 0.3s ease;

  &:focus {
    outline: none;
    border-color: ${Colors.secondary};
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
`;

const SubmitButton = styled.button`
  background: linear-gradient(135deg, ${Colors.secondary}, ${Colors.primary});
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// Support Us page component
const SupportComp = () => {
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    organization: '',
    inquiryType: 'general',
    message: ''
  });
  const [isSubmittingContact, setIsSubmittingContact] = useState(false);

  const supportTiers = [
    {
      id: 'community',
      name: "Community Supporter",
      icon: "🌱",
      amount: 25,
      period: "month",
      description: "Support our mission to democratize academic publishing and make research accessible to all.",
      features: [
        "Early access to new articles",
        "Monthly research newsletter",
        "Community forum access",
        "Digital supporter badge",
        "Tax-deductible receipt"
      ],
      impact: "Supports 1 researcher from developing countries",
      featured: false
    },
    {
      id: 'advocate',
      name: "Research Advocate",
      icon: "🔬",
      amount: 50,
      period: "month",
      description: "Accelerate breakthrough research in agriculture and environmental studies worldwide.",
      features: [
        "All Community benefits",
        "Quarterly impact reports",
        "Priority customer support",
        "Exclusive webinar access",
        "Research collaboration opportunities",
        "Advanced analytics dashboard"
      ],
      impact: "Funds peer review for 2 articles monthly",
      featured: true
    },
    {
      id: 'champion',
      name: "Academic Champion",
      icon: "🏆",
      amount: 100,
      period: "month",
      description: "Champion the future of open science and sustainable agricultural research.",
      features: [
        "All Advocate benefits",
        "Direct researcher mentorship",
        "Annual conference invitation",
        "Co-authorship opportunities",
        "Grant writing workshops",
        "Editorial board consideration",
        "Lifetime supporter recognition"
      ],
      impact: "Sponsors complete publication process for 1 article",
      featured: false
    },
    {
      id: 'patron',
      name: "Institutional Patron",
      icon: "🏛️",
      amount: 250,
      period: "month",
      description: "Lead institutional support for global agricultural research advancement.",
      features: [
        "All Champion benefits",
        "Institutional branding opportunities",
        "Custom research partnerships",
        "Dedicated account manager",
        "Annual impact assessment",
        "Policy influence opportunities",
        "Legacy naming rights"
      ],
      impact: "Establishes scholarship fund for emerging researchers",
      featured: false
    }
  ];

  const handleDonation = async (tier) => {
    if (isProcessing) return;

    setIsProcessing(true);

    try {
      // Convert amount to kobo (Paystack uses kobo for NGN)
      const amountInKobo = tier.amount * 100 * 411; // Approximate USD to NGN conversion

      // Navigate to payment page with support data
      navigate('/payment', {
        state: {
          type: PAYSTACK_CONFIG.PAYMENT_TYPES.SUPPORT_DONATION,
          amount: amountInKobo,
          originalAmount: tier.amount,
          currency: 'USD',
          description: `${tier.name} - Monthly Support`,
          supportTier: tier.name,
          email: '<EMAIL>', // This should come from user context
          userName: 'Supporter', // This should come from user context
          recurring: true,
          tier: tier
        }
      });
    } catch (error) {
      console.error('Donation error:', error);
      toast.error('Failed to process donation. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleContactFormChange = (e) => {
    const { name, value } = e.target;
    setContactForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleContactSubmit = async (e) => {
    e.preventDefault();
    setIsSubmittingContact(true);

    try {
      // Here you would typically send the contact form to your backend
      // For now, we'll just show a success message
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      toast.success('Thank you for your message! We\'ll get back to you soon.');
      setContactForm({
        name: '',
        email: '',
        organization: '',
        inquiryType: 'general',
        message: ''
      });
    } catch (error) {
      console.error('Contact form error:', error);
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsSubmittingContact(false);
    }
  };

  return (
    <SupportSection>
      <Container>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <HeroSection>
            <Title>Support Academic Excellence</Title>
            <Subtitle>
              Join our mission to democratize academic publishing and accelerate breakthrough research
              in agriculture and environmental studies. Your support directly impacts researchers worldwide.
            </Subtitle>

            <StatsGrid>
              <StatItem>
                <StatNumber>2,500+</StatNumber>
                <StatLabel>Researchers Supported</StatLabel>
              </StatItem>
              <StatItem>
                <StatNumber>50+</StatNumber>
                <StatLabel>Countries Reached</StatLabel>
              </StatItem>
              <StatItem>
                <StatNumber>95%</StatNumber>
                <StatLabel>Open Access Rate</StatLabel>
              </StatItem>
              <StatItem>
                <StatNumber>$2M+</StatNumber>
                <StatLabel>Research Funded</StatLabel>
              </StatItem>
            </StatsGrid>
          </HeroSection>

          <DonationContainer>
            {supportTiers.map((tier, index) => (
              <DonationOption
                key={tier.id}
                featured={tier.featured}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <TierIcon>{tier.icon}</TierIcon>
                <TierName>{tier.name}</TierName>

                <Amount>
                  ${tier.amount}
                  <Period>/{tier.period}</Period>
                </Amount>

                <Description>{tier.description}</Description>

                <FeatureList>
                  {tier.features.map((feature, featureIndex) => (
                    <Feature key={featureIndex}>{feature}</Feature>
                  ))}
                </FeatureList>

                <ImpactText>
                  💡 Impact: {tier.impact}
                </ImpactText>

                <DonateButton
                  onClick={() => handleDonation(tier)}
                  disabled={isProcessing}
                >
                  {isProcessing ? 'Processing...' : `Support with $${tier.amount}/month`}
                </DonateButton>
              </DonationOption>
            ))}
          </DonationContainer>

          <ImpactSection>
            <h2 style={{ fontSize: '2.5rem', fontWeight: '700', marginBottom: '1rem' }}>
              Your Support Creates Real Impact
            </h2>
            <p style={{ fontSize: '1.2rem', opacity: '0.9', marginBottom: '2rem', maxWidth: '600px', margin: '0 auto 2rem' }}>
              See how your contribution directly advances agricultural research and environmental sustainability worldwide.
            </p>

            <ImpactGrid>
              <ImpactCard>
                <ImpactNumber>500+</ImpactNumber>
                <ImpactLabel>Articles Published</ImpactLabel>
              </ImpactCard>
              <ImpactCard>
                <ImpactNumber>85%</ImpactNumber>
                <ImpactLabel>From Developing Countries</ImpactLabel>
              </ImpactCard>
              <ImpactCard>
                <ImpactNumber>1M+</ImpactNumber>
                <ImpactLabel>Research Downloads</ImpactLabel>
              </ImpactCard>
              <ImpactCard>
                <ImpactNumber>150+</ImpactNumber>
                <ImpactLabel>Universities Partnered</ImpactLabel>
              </ImpactCard>
            </ImpactGrid>
          </ImpactSection>

          <TestimonialSection>
            <h2 style={{ fontSize: '2rem', fontWeight: '700', textAlign: 'center', marginBottom: '1rem', color: Colors.primary }}>
              What Our Community Says
            </h2>
            <p style={{ textAlign: 'center', color: '#64748b', marginBottom: '2rem' }}>
              Hear from researchers whose work has been supported by our community
            </p>

            <TestimonialGrid>
              <TestimonialCard>
                <TestimonialText>
                  "Learning Publics gave me the platform to share my research on sustainable farming practices.
                  The support from the community has been incredible, and I've connected with researchers worldwide."
                </TestimonialText>
                <TestimonialAuthor>Dr. Amara Okafor</TestimonialAuthor>
                <TestimonialRole>Agricultural Scientist, University of Lagos</TestimonialRole>
              </TestimonialCard>

              <TestimonialCard>
                <TestimonialText>
                  "The open access model here has democratized research publication. My work on climate-resilient crops
                  reached farmers in 15 countries within months of publication."
                </TestimonialText>
                <TestimonialAuthor>Prof. Maria Santos</TestimonialAuthor>
                <TestimonialRole>Environmental Researcher, São Paulo University</TestimonialRole>
              </TestimonialCard>

              <TestimonialCard>
                <TestimonialText>
                  "As a young researcher, the mentorship and support I received through Learning Publics was invaluable.
                  It's more than a journal - it's a community committed to advancing science."
                </TestimonialText>
                <TestimonialAuthor>Dr. James Chen</TestimonialAuthor>
                <TestimonialRole>Postdoctoral Fellow, Agricultural Innovation Lab</TestimonialRole>
              </TestimonialCard>
            </TestimonialGrid>
          </TestimonialSection>

          <FAQSection>
            <h2 style={{ fontSize: '2rem', fontWeight: '700', textAlign: 'center', marginBottom: '2rem', color: Colors.primary }}>
              Frequently Asked Questions
            </h2>

            <FAQItem>
              <FAQQuestion>How is my donation used?</FAQQuestion>
              <FAQAnswer>
                Your donation directly supports peer review processes, open access publishing, researcher mentorship programs,
                and technology infrastructure. We maintain full transparency with quarterly impact reports for all supporters.
              </FAQAnswer>
            </FAQItem>

            <FAQItem>
              <FAQQuestion>Is my donation tax-deductible?</FAQQuestion>
              <FAQAnswer>
                Yes, Learning Publics is a registered non-profit organization. All donations are tax-deductible,
                and you'll receive a receipt for your records immediately after your contribution.
              </FAQAnswer>
            </FAQItem>

            <FAQItem>
              <FAQQuestion>Can I cancel my monthly support?</FAQQuestion>
              <FAQAnswer>
                Absolutely. You can modify or cancel your monthly support at any time through your supporter dashboard
                or by contacting our support team. No questions asked, no cancellation fees.
              </FAQAnswer>
            </FAQItem>

            <FAQItem>
              <FAQQuestion>How do I track the impact of my support?</FAQQuestion>
              <FAQAnswer>
                Supporters receive regular updates including quarterly impact reports, featured researcher spotlights,
                and access to our impact dashboard showing real-time metrics on research supported and global reach.
              </FAQAnswer>
            </FAQItem>

            <FAQItem>
              <FAQQuestion>Can institutions become supporters?</FAQQuestion>
              <FAQAnswer>
                Yes! We offer institutional support packages with custom benefits including branding opportunities,
                research partnerships, and dedicated account management. Contact us for institutional pricing.
              </FAQAnswer>
            </FAQItem>
          </FAQSection>

          <ContactSection>
            <h2 style={{ fontSize: '2rem', fontWeight: '700', textAlign: 'center', marginBottom: '1rem', color: Colors.primary }}>
              Get in Touch
            </h2>
            <p style={{ textAlign: 'center', color: '#64748b', marginBottom: '2rem' }}>
              Have questions about supporting our mission? Want to explore partnership opportunities? We'd love to hear from you.
            </p>

            <ContactForm onSubmit={handleContactSubmit}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
                <FormGroup>
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    type="text"
                    id="name"
                    name="name"
                    value={contactForm.name}
                    onChange={handleContactFormChange}
                    required
                    placeholder="Your full name"
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={contactForm.email}
                    onChange={handleContactFormChange}
                    required
                    placeholder="<EMAIL>"
                  />
                </FormGroup>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
                <FormGroup>
                  <Label htmlFor="organization">Organization</Label>
                  <Input
                    type="text"
                    id="organization"
                    name="organization"
                    value={contactForm.organization}
                    onChange={handleContactFormChange}
                    placeholder="Your organization (optional)"
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="inquiryType">Inquiry Type</Label>
                  <Select
                    id="inquiryType"
                    name="inquiryType"
                    value={contactForm.inquiryType}
                    onChange={handleContactFormChange}
                  >
                    <option value="general">General Inquiry</option>
                    <option value="partnership">Partnership Opportunity</option>
                    <option value="institutional">Institutional Support</option>
                    <option value="research">Research Collaboration</option>
                    <option value="technical">Technical Support</option>
                    <option value="media">Media Inquiry</option>
                  </Select>
                </FormGroup>
              </div>

              <FormGroup>
                <Label htmlFor="message">Message *</Label>
                <TextArea
                  id="message"
                  name="message"
                  value={contactForm.message}
                  onChange={handleContactFormChange}
                  required
                  placeholder="Tell us about your inquiry, partnership ideas, or how you'd like to support our mission..."
                />
              </FormGroup>

              <SubmitButton type="submit" disabled={isSubmittingContact}>
                {isSubmittingContact ? 'Sending...' : 'Send Message'}
              </SubmitButton>
            </ContactForm>
          </ContactSection>
        </motion.div>
      </Container>
    </SupportSection>
  );
};

export default SupportComp;
