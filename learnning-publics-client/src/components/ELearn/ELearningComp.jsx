import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import Colors from 'utils/colors';
import jmsApp from 'api/jms';
import { USER_PLAN } from 'api/ACTION';
import PaymentCard from 'components/payment/PaymentCard';
import { toast } from 'react-hot-toast';
import textBanner from '../../assets/images/bgBanner.png';

const ELearningContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding-top: 5rem;

  @media (max-width: 768px) {
    padding-top: 4rem;
  }
`;

const HeroSection = styled.div`
  background: linear-gradient(135deg, ${Colors.primary} 0%, #5a6a72 100%);
  color: white;
  padding: 4rem 2rem;
  margin: 2rem;
  border-radius: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
  }
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
`;

const HeroTitle = styled.h1`
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.3rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  line-height: 1.6;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: ${Colors.secondary};
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  opacity: 0.8;
`;

const Section = styled.section`
  padding: 3rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const SectionTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  color: ${Colors.primary};
  text-align: center;
  margin-bottom: 1rem;
`;

const SectionSubtitle = styled.p`
  font-size: 1.2rem;
  color: #64748b;
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const CourseGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
`;

const CourseCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: ${Colors.secondary};
  }
`;

const CourseIcon = styled.div`
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: white;
`;

const CourseTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 1rem;
`;

const CourseDescription = styled.p`
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
`;

const CourseButton = styled.button`
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
`;

const PricingSection = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 3rem 2rem;
  margin: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const PricingGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
`;

export const ELearningComp = () => {
  const navigate = useNavigate();
  const [plans, setPlans] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const courseCategories = [
    {
      icon: '🎓',
      title: 'JAMB Preparation',
      description: 'Comprehensive preparation for Joint Admissions and Matriculation Board examinations with practice tests and study materials.',
      route: '/jamb'
    },
    {
      icon: '📚',
      title: 'WAEC Courses',
      description: 'West African Examinations Council preparation courses covering all major subjects with interactive learning modules.',
      route: '/waec'
    },
    {
      icon: '🔬',
      title: 'Science & Technology',
      description: 'Advanced courses in Physics, Chemistry, Biology, Mathematics, and Computer Science for academic excellence.',
      route: '/courses'
    },
    {
      icon: '📖',
      title: 'Arts & Humanities',
      description: 'Literature, History, Geography, Government, and Language courses designed for comprehensive understanding.',
      route: '/courses'
    },
    {
      icon: '💼',
      title: 'Commercial Studies',
      description: 'Business Studies, Economics, Accounting, and Commerce courses for future business leaders.',
      route: '/courses'
    },
    {
      icon: '🌍',
      title: 'Language Learning',
      description: 'English, Yoruba, Igbo, and Swahili language courses with interactive speaking and writing practice.',
      route: '/courses'
    }
  ];

  useEffect(() => {
    fetchPlans();
  }, []);

  const fetchPlans = async () => {
    try {
      setIsLoading(true);
      const { data } = await jmsApp.get(USER_PLAN.GET_PLAN());
      if (data.success) {
        setPlans(data.plans);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
      toast.error('Failed to load pricing plans');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCourseNavigation = (route) => {
    navigate(route);
  };

  const handleSignUp = () => {
    navigate('/create-account');
  };

  return (
    <ELearningContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <HeroSection>
          <HeroContent>
            <HeroTitle>Learning Publics E-Learning Platform</HeroTitle>
            <HeroSubtitle>
              A comprehensive digital learning environment that facilitates the creation, delivery,
              management, and assessment of educational content. Learn anywhere, anytime with our
              interactive courses and expert-designed curriculum.
            </HeroSubtitle>
            <StatsGrid>
              <StatItem>
                <StatNumber>500+</StatNumber>
                <StatLabel>Courses Available</StatLabel>
              </StatItem>
              <StatItem>
                <StatNumber>10,000+</StatNumber>
                <StatLabel>Students Enrolled</StatLabel>
              </StatItem>
              <StatItem>
                <StatNumber>95%</StatNumber>
                <StatLabel>Success Rate</StatLabel>
              </StatItem>
              <StatItem>
                <StatNumber>24/7</StatNumber>
                <StatLabel>Learning Support</StatLabel>
              </StatItem>
            </StatsGrid>
          </HeroContent>
        </HeroSection>

        <Section>
          <SectionTitle>Course Categories</SectionTitle>
          <SectionSubtitle>
            Explore our comprehensive range of educational programs designed to help you achieve
            academic excellence and career success.
          </SectionSubtitle>

          <CourseGrid>
            {courseCategories.map((course, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <CourseCard>
                  <CourseIcon>{course.icon}</CourseIcon>
                  <CourseTitle>{course.title}</CourseTitle>
                  <CourseDescription>{course.description}</CourseDescription>
                  <CourseButton onClick={() => handleCourseNavigation(course.route)}>
                    Explore Courses
                  </CourseButton>
                </CourseCard>
              </motion.div>
            ))}
          </CourseGrid>
        </Section>

        <PricingSection>
          <SectionTitle>Choose Your Learning Plan</SectionTitle>
          <SectionSubtitle>
            We create monthly pricing packages for all students who are interested in getting
            the most benefit from our courses and educational resources.
          </SectionSubtitle>

          {!isLoading && plans.length > 0 && (
            <PricingGrid>
              {plans.map((plan, index) => (
                <motion.div
                  key={plan.planId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <PaymentCard
                    id={plan.planId}
                    planId={plan.planId}
                    price={plan.price}
                    handler={handleSignUp}
                    type={plan.type}
                    plan={plan}
                  />
                </motion.div>
              ))}
            </PricingGrid>
          )}

          {!isLoading && plans.length === 0 && (
            <div style={{ textAlign: 'center', padding: '3rem', color: '#64748b' }}>
              <h3>Pricing plans will be available soon!</h3>
              <p>Check back later for our comprehensive learning packages.</p>
            </div>
          )}
        </PricingSection>
      </motion.div>
    </ELearningContainer>
  );
};
