{"name": "jms-backend", "version": "1.2.0", "description": "This is the backend API Server for the JMS User and Admin dashboards.", "main": "server.js", "directories": {"doc": "docs", "test": "test"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.33.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-handlebars": "^6.0.2", "express-winston": "^4.2.0", "jsonwebtoken": "^9.0.0", "mongoose": "^6.9.1", "nodemailer": "^6.9.1", "nodemailer-express-handlebars": "^6.1.0", "nodemailer-mailgun-transport": "^2.1.5", "paystack": "^2.0.1", "slugify": "^1.6.6", "uuid": "^9.0.0", "winston": "^3.8.2", "winston-mongodb": "^5.1.1"}, "devDependencies": {"eslint": "^8.33.0", "eslint-plugin-react": "^7.32.2", "morgan": "^1.10.0", "nodemon": "^2.0.20"}, "repository": {"type": "git", "url": "git+https://github.com/Learnuel1/JMS-backend.git"}, "bugs": {"url": "https://github.com/Learnuel1/JMS-backend/issues"}, "homepage": "https://github.com/Learnuel1/JMS-backend#readme"}