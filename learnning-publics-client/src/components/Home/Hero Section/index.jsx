import { Combobox, Transition } from "@headlessui/react";
import { <PERSON><PERSON><PERSON>, <PERSON>Content, HeroOverlay, HomePageTypography } from "./style";
import React, { Fragment, useEffect, useMemo, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

import { ARTICLE_ENDPOINT } from "api/ACTION";
import jmsApp from "api/jms";
import landingPageGg1 from "../../../assets/images/banner.jpg";
import landingPageGg2 from "../../../assets/images/banner2.jpg";
import { useDebounce } from "hooks/use-debounce";
import { useNavigate } from "react-router-dom";
import {
  heroVariants,
  heroTextVariants,
  fadeInUp,
  staggerContainer,
  staggerItem,
  inputVariants
} from "design-system/animations";

const HeroComp = () => {
  const navigate = useNavigate();
  const images = [landingPageGg1, landingPageGg2];
  const [currentImage, setCurrentImage] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [searchInput, setSearchInput] = useState("");
  const [debouncedInput] = useDebounce(searchInput, 200);
  const [matchingArticles, setMatchingArticles] = useState([]);

  const [selectedArticle, setSelectedArticle] = useState();

  const filteredArticles = useMemo(() => {
    const filtered =
      searchInput === ""
        ? matchingArticles
        : matchingArticles.filter((article) =>
            article.title.toLowerCase().includes(searchInput.toLowerCase())
          );

    return filtered;
  }, [matchingArticles, searchInput]);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImage((prevImage) => (prevImage + 1) % images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [images.length]);

  const handleSearch = async () => {
    try {
      setIsLoading(true);
      // const { data } = await jmsApp.get(ARTICLE_ENDPOINT.ARTICLE_PUBLISHED());
      const { data } = await jmsApp.get(
        ARTICLE_ENDPOINT.GET_ARTICLE_BY_TITLE(debouncedInput || "a")
      );

      setIsLoading(false);
      const articles = data?.article;
      setMatchingArticles(articles);
    } catch (error) {
      setMatchingArticles([]);
      setIsLoading(false);
      ////console.error("Failed to fetch articles", error);
      // Handle the error appropriately
    }
  };

  useEffect(() => {
    handleSearch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedInput, navigate]);

  const handleViewArticle = async (article) => {
    // Redirect to the first matching article's category page
    if (article) {
      navigate(`/articles/${article.articleId}`);
    }
  };

  const handleInputChange = (event) => {
    setSearchInput(event.target.value);
  };

  // eslint-disable-next-line no-unused-vars
  const clearSearch = () => {
    setSearchInput("");
    setMatchingArticles([]);
  };

  const handleSelectedArticle = (selectedArticle) => {
    if (selectedArticle) {
      setSelectedArticle(selectedArticle);
      handleViewArticle(selectedArticle);
    }
  };

  const backgroundStyle = {
    backgroundImage: `url(${images[currentImage]})`,
    backgroundSize: "cover",
    backgroundPosition: "center",
    transition: "background-image 1s ease-in-out"
  };

  const ComboBtn = useMemo(() => {
    if (filteredArticles?.length > 0 && isLoading === false) {
      return (
        <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2">
          <svg
            aria-hidden="true"
            className="h-5 w-5 fill-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 512 512"
          >
            <path
              d="M136 208l120-104 120 104M136 304l120 104 120-104"
              stroke="currentColor"
              fill="none"
              strokeWidth="32"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </Combobox.Button>
      );
    } else if (isLoading === true) {
      return (
        <Combobox.Button className="absolute inset-y-0 right-0 flex items-center pr-2 select-none pointer-events-none">
          <svg
            aria-hidden="true"
            className="inline w-5 h-5 text-gray-200 animate-spin  fill-[#46555c]"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </svg>
          <span className="sr-only">Loading...</span>
        </Combobox.Button>
      );
    } else {
      return null;
    }
  }, [filteredArticles?.length, isLoading]);

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={heroVariants}
    >
      <HeroCon style={backgroundStyle}>
        <HeroOverlay
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center"
          }}
        >
          <HeroContent>
            <motion.div
              variants={heroTextVariants}
              initial="hidden"
              animate="visible"
            >
              <HomePageTypography style={{ color: "#f0e8e8" }}>
                Explore world-class research articles.
              </HomePageTypography>
            </motion.div>

          <motion.div
            variants={fadeInUp}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.5 }}
            className="w-4/5 lg:w-2/5 mx-auto flex flex-row justify-center items-baseline gap-x-6 mt-4"
          >
            <Combobox value={selectedArticle} onChange={handleSelectedArticle}>
              <div className="relative h-full w-full sm:w-fit">
                <motion.div
                  variants={fadeInUp}
                  transition={{ delay: 0.6 }}
                >
                  <Combobox.Label className={"text-white sm:my-1"}>
                    Search for topic...
                  </Combobox.Label>
                </motion.div>
                <motion.div
                  variants={inputVariants}
                  initial="rest"
                  whileFocus="focus"
                  className="relative w-full xs:w-32 sm:h-12 h-10 sm:w-[30rem] cursor-default overflow-hidden rounded-lg bg-white text-left shadow-md focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm"
                >
                  <Combobox.Input
                    data-search-input
                    className="w-full border-none py-1 sm:py-1.5 pl-1.5 sm:pl-3 h-full sm:pr-10 text-sm leading-5 text-gray-900 focus:ring-0 transition-all duration-200"
                    displayValue={(article) => {
                      return article.title ?? "Search...";
                    }}
                    onChange={handleInputChange}
                    placeholder="Search for articles, topics, authors..."
                  />
                  {ComboBtn}
                </motion.div>
                <Transition
                  as={Fragment}
                  leave="transition ease-in duration-100"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                  afterLeave={() => setSearchInput("")}
                >
                  <motion.div
                    variants={staggerContainer}
                    initial="hidden"
                    animate="visible"
                  >
                    <Combobox.Options className="absolute max-h-64 w-full overflow-auto rounded bg-white py-2 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-50">
                      {filteredArticles?.length === 0 &&
                      searchInput.trim() !== "" ? (
                        <motion.div
                          variants={staggerItem}
                          className="relative cursor-default select-none px-4 py-2 text-gray-700"
                        >
                          Nothing found.
                        </motion.div>
                      ) : (
                        filteredArticles?.map((article, index) => (
                          <motion.div
                            key={article.articleId}
                            variants={staggerItem}
                            transition={{ delay: index * 0.05 }}
                          >
                            <Combobox.Option
                              onClick={() => {
                                handleViewArticle(article);
                              }}
                              className={({ active }) =>
                                `relative cursor-pointer my-1 select-none py-2 pl-10 pr-4 transition-colors duration-200 ${
                                  active
                                    ? "bg-[#46555c] text-white"
                                    : "text-gray-900 hover:bg-gray-50"
                                }`
                              }
                              value={article}
                            >
                          {({ selected, active }) => (
                            <>
                              <span
                                className={`block truncate ${
                                  selected ? "font-medium" : "font-normal"
                                }`}
                              >
                                {article.title}
                              </span>
                              {selected ? (
                                <span
                                  className={`absolute inset-y-0 left-0 flex items-center pl-3 ${
                                    active ? "text-white" : "text-[#46555c]"
                                  }`}
                                ></span>
                              ) : null}
                            </>
                          )}
                            </Combobox.Option>
                          </motion.div>
                        ))
                      )}
                    </Combobox.Options>
                  </motion.div>
                </Transition>
              </div>
            </Combobox>

            <motion.button
              type="button"
              variants={buttonVariants}
              initial="rest"
              whileHover="hover"
              whileTap="tap"
              className={`duration-100 will-change-auto transition-all ease-in-out border border-solid border-white p-2 sm:p-2.5 rounded text-white w-fit h-fit self-end hover:shadow-sm active:shadow-none hover:shadow-[rgba(255,255,255,0.5)]`}
              onClick={handleSearch}
            >
              Search
            </motion.button>
          </motion.div>
        </HeroContent>
      </HeroOverlay>
    </HeroCon>
    </motion.div>
  );
};

export default HeroComp;
