const { Schema, model } = require("mongoose");

const DownloadSchema = new Schema({
  admin: {
    type: Schema.Types.ObjectId,
    ref: "Account",
    required: true,
    indexed: true,
  },
  article: {
    type:[Schema.Types.ObjectId],
    ref: "Article",
    required: true,
    indexed: true,
  }
},
{timestamps: true}
);
const DownloadModel = model("download", DownloadSchema);
module.exports = DownloadModel;