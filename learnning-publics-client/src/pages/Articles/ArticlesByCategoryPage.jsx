import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import ArticleCard from 'components/Articles/ArticleCard';
import LoadingSpinner from 'components/Common/LoadingSpinner';
import ErrorMessage from 'components/Common/ErrorMessage';
import { generateCanonicalUrl } from 'utils/seo';

const ArticlesByCategoryPage = () => {
  const { categorySlug } = useParams();
  const [articles, setArticles] = useState([]);
  const [category, setCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchCategoryArticles = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/articles/category/${categorySlug}`);
      
      if (!response.ok) {
        throw new Error('Category not found');
      }
      
      const data = await response.json();
      setArticles(data.articles);
      setCategory(data.category);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [categorySlug]);

  useEffect(() => {
    if (categorySlug) {
      fetchCategoryArticles();
    }
  }, [categorySlug, fetchCategoryArticles]);

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message={error} />;
  if (!category) return <ErrorMessage message="Category not found" />;

  const canonicalUrl = generateCanonicalUrl(`/articles/category/${categorySlug}`);
  const pageTitle = `${category.name} Articles | Learning Publics`;
  const pageDescription = category.metaDescription || 
    `Browse academic articles in ${category.name}. Discover research papers and scholarly publications.`;

  return (
    <>
      <Helmet>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <link rel="canonical" href={canonicalUrl} />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:url" content={canonicalUrl} />
      </Helmet>

      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {category.name} Articles
          </h1>
          {category.description && (
            <p className="text-lg text-gray-600">
              {category.description}
            </p>
          )}
        </div>

        {articles.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {articles.map((article) => (
              <ArticleCard key={article._id} article={article} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No articles found
            </h3>
            <p className="text-gray-600">
              No articles are currently available in this category.
            </p>
          </div>
        )}
      </div>
    </>
  );
};

export default ArticlesByCategoryPage;
