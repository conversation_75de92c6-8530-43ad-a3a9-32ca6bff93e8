import React, { useEffect } from "react";
import { observer } from "mobx-react-lite";
import authStore from "mobx/AuthStore";
import VerifyOtp from "./VerifyOtp";
// import PersonalDetails from "./PersonalDetails"
// import SetPassword from "./SetPassword"
import { useNavigate } from "react-router-dom";
import { E_LEARNING_ROUTE } from "routes";
import SignUpComp from "./SignUpComp";

function CreateAccount() {
  const navigate = useNavigate();
  useEffect(() => {
    authStore.getCurrentStep();
    const status = authStore.loggedInStatus();
    if (status) {
      navigate(`${E_LEARNING_ROUTE}`);
    }
  }, [navigate]);
  return <>{authStore.currentStep === "verify" && <SignUpComp />}</>;
}
export default observer(CreateAccount);
