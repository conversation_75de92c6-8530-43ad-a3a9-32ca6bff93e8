import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { MainContainer } from 'globalStyles';
import SEOHead from 'components/SEO/SEOHead';
import PaystackPayment from 'components/Payment/PaystackPayment';
import PaymentService from 'services/paymentService';
import Colors from 'utils/colors';
import { toast } from 'react-hot-toast';

const PaymentContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 0;
  padding-top: 6rem;
  
  @media (max-width: 768px) {
    padding-top: 5rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }
`;

const PaymentWrapper = styled(MainContainer)`
  max-width: 600px;
  margin: 0 auto;
`;

const BackButton = styled.button`
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:hover {
    background: #e5e7eb;
    transform: translateX(-2px);
  }
`;

const SuccessContainer = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const SuccessIcon = styled.div`
  font-size: 4rem;
  color: #16a34a;
  margin-bottom: 2rem;
`;

const SuccessTitle = styled.h2`
  font-size: 2rem;
  font-weight: 700;
  color: ${Colors.primary};
  margin-bottom: 1rem;
`;

const SuccessMessage = styled.p`
  color: #64748b;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
`;

const SuccessDetails = styled.div`
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
`;

const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailLabel = styled.span`
  color: #64748b;
  font-weight: 500;
`;

const DetailValue = styled.span`
  color: ${Colors.primary};
  font-weight: 600;
`;

const ActionButton = styled.button`
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 0.5rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
`;

const ErrorContainer = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #fecaca;
`;

const ErrorIcon = styled.div`
  font-size: 3rem;
  color: #dc2626;
  margin-bottom: 1rem;
`;

const ErrorTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 1rem;
`;

const ErrorMessage = styled.p`
  color: #64748b;
  margin-bottom: 2rem;
`;

const PaymentPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [paymentStatus, setPaymentStatus] = useState('pending'); // pending, success, error
  const [paymentResult, setPaymentResult] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const paymentData = location.state;

  useEffect(() => {
    // Redirect if no payment data
    if (!paymentData) {
      toast.error('No payment information found');
      navigate('/');
      return;
    }

    // Create payment record if amount > 0
    if (paymentData.amount > 0) {
      createPaymentRecord();
    }
  }, [paymentData, navigate]);

  const createPaymentRecord = async () => {
    try {
      const result = await PaymentService.createPaymentRecord(paymentData);
      if (!result.success) {
        console.error('Failed to create payment record:', result.error);
      }
    } catch (error) {
      console.error('Payment record creation error:', error);
    }
  };

  const handlePaymentSuccess = async (paymentResult) => {
    setIsProcessing(true);
    
    try {
      let result;
      
      if (paymentResult.amount === 0) {
        // Handle free submission
        result = await PaymentService.processFreeSubmission({
          articleId: paymentData.articleId,
          email: paymentData.email,
          userName: paymentData.userName
        });
      } else {
        // Handle paid submission
        if (paymentData.type === 'support_donation') {
          result = await PaymentService.processSupportPayment(
            paymentResult.reference,
            {
              supportTier: paymentData.supportTier,
              amount: paymentData.amount,
              email: paymentData.email,
              userName: paymentData.userName,
              recurring: paymentData.recurring || false
            }
          );
        } else {
          result = await PaymentService.processJournalPayment(
            paymentResult.reference,
            {
              type: paymentData.type,
              amount: paymentData.amount,
              articleId: paymentData.articleId,
              email: paymentData.email,
              userName: paymentData.userName
            }
          );
        }
      }

      if (result.success) {
        setPaymentStatus('success');
        setPaymentResult({
          ...paymentResult,
          backendResult: result.data
        });
        toast.success(paymentResult.amount === 0 ? 'Submission completed!' : 'Payment successful!');
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      setPaymentStatus('error');
      setPaymentResult({ error: error.message });
      toast.error('Payment processing failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaymentClose = () => {
    navigate(-1); // Go back to previous page
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleViewSubmissions = () => {
    navigate('/dashboard'); // Assuming there's a dashboard for submissions
  };

  if (!paymentData) {
    return null; // Will redirect in useEffect
  }

  if (isProcessing) {
    return (
      <PaymentContainer>
        <PaymentWrapper>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center"
          >
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-800 mb-2">Processing...</h2>
              <p className="text-gray-600">Please wait while we process your submission.</p>
            </div>
          </motion.div>
        </PaymentWrapper>
      </PaymentContainer>
    );
  }

  if (paymentStatus === 'success') {
    return (
      <>
        <SEOHead
          title="Payment Successful - Learning Publics Journal"
          description="Your payment has been processed successfully."
        />
        <PaymentContainer>
          <PaymentWrapper>
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <SuccessContainer>
                <SuccessIcon>✅</SuccessIcon>
                <SuccessTitle>
                  {paymentResult?.amount === 0 ? 'Submission Successful!' : 'Payment Successful!'}
                </SuccessTitle>
                <SuccessMessage>
                  {paymentResult?.amount === 0 
                    ? 'Your manuscript has been submitted successfully. Our editorial team will review it and contact you within 2-3 business days.'
                    : 'Thank you for your payment. Your transaction has been processed successfully.'
                  }
                </SuccessMessage>
                
                <SuccessDetails>
                  <DetailRow>
                    <DetailLabel>Reference:</DetailLabel>
                    <DetailValue>{paymentResult?.reference}</DetailValue>
                  </DetailRow>
                  <DetailRow>
                    <DetailLabel>Service:</DetailLabel>
                    <DetailValue>{paymentData.description}</DetailValue>
                  </DetailRow>
                  {paymentData.articleTitle && (
                    <DetailRow>
                      <DetailLabel>Article:</DetailLabel>
                      <DetailValue>{paymentData.articleTitle}</DetailValue>
                    </DetailRow>
                  )}
                  <DetailRow>
                    <DetailLabel>Amount:</DetailLabel>
                    <DetailValue>
                      {paymentResult?.amount === 0 ? 'FREE' : `₦${paymentResult?.amount?.toLocaleString()}`}
                    </DetailValue>
                  </DetailRow>
                  <DetailRow>
                    <DetailLabel>Status:</DetailLabel>
                    <DetailValue>Completed</DetailValue>
                  </DetailRow>
                </SuccessDetails>
                
                <div>
                  <ActionButton onClick={handleBackToHome}>
                    Back to Home
                  </ActionButton>
                  <ActionButton onClick={handleViewSubmissions}>
                    View Submissions
                  </ActionButton>
                </div>
              </SuccessContainer>
            </motion.div>
          </PaymentWrapper>
        </PaymentContainer>
      </>
    );
  }

  if (paymentStatus === 'error') {
    return (
      <>
        <SEOHead
          title="Payment Error - Learning Publics Journal"
          description="There was an error processing your payment."
        />
        <PaymentContainer>
          <PaymentWrapper>
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <ErrorContainer>
                <ErrorIcon>❌</ErrorIcon>
                <ErrorTitle>Payment Error</ErrorTitle>
                <ErrorMessage>
                  {paymentResult?.error || 'There was an error processing your payment. Please try again.'}
                </ErrorMessage>
                <ActionButton onClick={handlePaymentClose}>
                  Try Again
                </ActionButton>
              </ErrorContainer>
            </motion.div>
          </PaymentWrapper>
        </PaymentContainer>
      </>
    );
  }

  return (
    <>
      <SEOHead
        title="Complete Payment - Learning Publics Journal"
        description="Complete your payment to finalize your journal submission or support."
        keywords="payment, journal submission, academic publishing, paystack"
        canonicalUrl="/payment"
      />
      
      <PaymentContainer>
        <PaymentWrapper>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <BackButton onClick={handlePaymentClose}>
              ← Back
            </BackButton>
            
            <PaystackPayment
              paymentData={paymentData}
              onSuccess={handlePaymentSuccess}
              onClose={handlePaymentClose}
              userEmail={paymentData.email}
              userName={paymentData.userName}
            />
          </motion.div>
        </PaymentWrapper>
      </PaymentContainer>
    </>
  );
};

export default PaymentPage;
