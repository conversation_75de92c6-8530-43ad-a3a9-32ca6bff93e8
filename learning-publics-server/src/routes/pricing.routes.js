const express = require('express');
const router = express.Router();
const pricingController = require('../controllers/pricing.controller');
const { authenticateToken, requireAdmin, requireSuperAdmin } = require('../middleware/auth');

// Public routes - Get current pricing
router.get('/service/:serviceType', pricingController.getServicePricing);

// Admin routes - Require authentication and admin role
router.use(authenticateToken);
router.use(requireAdmin);

// Get all pricing configurations
router.get('/admin/configs', pricingController.getAllPricingConfigs);

// Create new pricing configuration
router.post('/admin/configs', pricingController.createPricingConfig);

// Update pricing configuration
router.put('/admin/configs/:id', pricingController.updatePricingConfig);

// Schedule price change
router.post('/admin/configs/:id/schedule', pricingController.schedulePriceChange);

// Set promotional pricing
router.post('/admin/configs/:id/promotion', pricingController.setPromotionalPricing);

// Toggle service pricing (free/paid)
router.patch('/admin/service/:serviceType/toggle', pricingController.toggleServicePricing);

// Execute scheduled pricing changes (can be called manually or by cron)
router.post('/admin/execute-scheduled', pricingController.executeScheduledPricing);

// Get pricing analytics
router.get('/admin/analytics', pricingController.getPricingAnalytics);

// Super Admin only routes
router.use(requireSuperAdmin);

// Delete pricing configuration
router.delete('/admin/configs/:id', pricingController.deletePricingConfig);

module.exports = router;
