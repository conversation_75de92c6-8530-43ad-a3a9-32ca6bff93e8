const { PAYSTACK_SECRETE_KEY } = require("../config/env");

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/transaction/initialize',
  method: 'POST',
  headers: {
    Authorization: `Bearer ${PAYSTACK_SECRETE_KEY}`,
    'Content-Type': 'application/json'
  }
}
 
const transOptions = (reference) => {
 return{
   hostname: 'api.paystack.co',
  port: 443,
  path: `'/transaction/verify/:${reference}'`,
  method: 'GET',
  headers: {
    Authorization: `Bearer ${PAYSTACK_SECRETE_KEY}`
  }}
}

module.exports = {
  options,
  transOptions
};