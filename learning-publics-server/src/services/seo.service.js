const ArticleModel = require('../models/article.model');
const CategoryModel = require('../models/category.model');
const slugify = require('slugify');

/**
 * SEO Service for managing SEO-related operations
 */

class SEOService {
  
  /**
   * Generate unique slug for articles
   */
  static async generateArticleSlug(title, articleId = null) {
    let baseSlug = slugify(title, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g
    });
    
    let slug = baseSlug;
    let counter = 1;
    
    while (true) {
      const existingArticle = await ArticleModel.findOne({ 
        slug: slug,
        ...(articleId && { _id: { $ne: articleId } })
      });
      
      if (!existingArticle) {
        break;
      }
      
      slug = `${baseSlug}-${counter}`;
      counter++;
    }
    
    return slug;
  }

  /**
   * Generate unique slug for categories
   */
  static async generateCategorySlug(name, categoryId = null) {
    let baseSlug = slugify(name, {
      lower: true,
      strict: true,
      remove: /[*+~.()'"!:@]/g
    });
    
    let slug = baseSlug;
    let counter = 1;
    
    while (true) {
      const existingCategory = await CategoryModel.findOne({ 
        slug: slug,
        ...(categoryId && { _id: { $ne: categoryId } })
      });
      
      if (!existingCategory) {
        break;
      }
      
      slug = `${baseSlug}-${counter}`;
      counter++;
    }
    
    return slug;
  }

  /**
   * Update article SEO fields
   */
  static async updateArticleSEO(articleId, seoData) {
    const { title, metaDescription, keywords, customSlug } = seoData;
    
    const updateData = {};
    
    // Generate new slug if title changed or custom slug provided
    if (title || customSlug) {
      const slugSource = customSlug || title;
      updateData.slug = await this.generateArticleSlug(slugSource, articleId);
    }
    
    if (metaDescription) {
      updateData.metaDescription = metaDescription.length > 160 
        ? metaDescription.substring(0, 157) + '...'
        : metaDescription;
    }
    
    if (keywords) {
      updateData.keywords = Array.isArray(keywords) ? keywords : keywords.split(',').map(k => k.trim());
    }
    
    // Update canonical URL if slug changed
    if (updateData.slug) {
      updateData.canonicalUrl = `${process.env.FRONTEND_URL || 'https://learningpublics.com'}/articles/${updateData.slug}`;
    }
    
    return await ArticleModel.findByIdAndUpdate(articleId, updateData, { new: true });
  }

  /**
   * Track article view for SEO analytics
   */
  static async trackArticleView(articleSlug, searchKeyword = null) {
    const article = await ArticleModel.findOne({ slug: articleSlug });
    
    if (!article) {
      throw new Error('Article not found');
    }
    
    // Increment view count
    article.viewCount += 1;
    article.lastViewed = new Date();
    
    // Track search keyword if provided
    if (searchKeyword) {
      const existingKeyword = article.searchKeywords.find(k => k.keyword === searchKeyword);
      if (existingKeyword) {
        existingKeyword.count += 1;
      } else {
        article.searchKeywords.push({ keyword: searchKeyword, count: 1 });
      }
    }
    
    await article.save();
    return article;
  }

  /**
   * Get SEO-optimized article data
   */
  static async getArticleForSEO(slug) {
    const article = await ArticleModel.findBySlug(slug);
    
    if (!article) {
      return null;
    }
    
    // Generate breadcrumbs
    const breadcrumbs = [
      { name: 'Home', url: '/' },
      { name: 'Articles', url: '/articles' }
    ];
    
    if (article.category) {
      breadcrumbs.push({
        name: article.category.name,
        url: `/articles/category/${article.category.slug}`
      });
    }
    
    breadcrumbs.push({
      name: article.title,
      url: `/articles/${article.slug}`
    });
    
    return {
      ...article.toObject(),
      breadcrumbs,
      structuredData: this.generateArticleStructuredData(article)
    };
  }

  /**
   * Generate structured data for articles
   */
  static generateArticleStructuredData(article) {
    return {
      "@context": "https://schema.org",
      "@type": "ScholarlyArticle",
      "headline": article.title,
      "description": article.metaDescription || article.description,
      "author": {
        "@type": "Person",
        "name": article.author,
        "email": article.email
      },
      "datePublished": article.createdAt,
      "dateModified": article.updatedAt,
      "publisher": {
        "@type": "Organization",
        "name": "Learning Publics",
        "url": process.env.FRONTEND_URL || "https://learningpublics.com"
      },
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": article.canonicalUrl
      },
      "keywords": article.keywords?.join(', '),
      "articleSection": article.category?.name,
      "inLanguage": "en-US",
      "url": article.canonicalUrl,
      "isAccessibleForFree": true,
      "creativeWorkStatus": "Published"
    };
  }

  /**
   * Get popular articles for SEO
   */
  static async getPopularArticles(limit = 10) {
    return await ArticleModel.findSEOOptimized()
      .limit(limit)
      .select('title slug viewCount category createdAt');
  }

  /**
   * Get articles by category for SEO
   */
  static async getArticlesByCategory(categorySlug, page = 1, limit = 20) {
    const category = await CategoryModel.findBySlug(categorySlug);
    
    if (!category) {
      throw new Error('Category not found');
    }
    
    const skip = (page - 1) * limit;
    
    const articles = await ArticleModel.findSEOOptimized({ category: category._id })
      .skip(skip)
      .limit(limit)
      .select('title slug description metaDescription viewCount createdAt');
    
    const total = await ArticleModel.countDocuments({ 
      category: category._id, 
      status: 'published',
      slug: { $exists: true, $ne: null }
    });
    
    return {
      articles,
      category,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Search articles with SEO optimization
   */
  static async searchArticles(query, page = 1, limit = 20) {
    const skip = (page - 1) * limit;
    
    const searchQuery = {
      $and: [
        { status: 'published' },
        { slug: { $exists: true, $ne: null } },
        {
          $or: [
            { title: { $regex: new RegExp(query, 'i') } },
            { description: { $regex: new RegExp(query, 'i') } },
            { keywords: { $in: [new RegExp(query, 'i')] } }
          ]
        }
      ]
    };
    
    const articles = await ArticleModel.find(searchQuery)
      .populate('category', 'name slug')
      .skip(skip)
      .limit(limit)
      .sort({ viewCount: -1, createdAt: -1 })
      .select('title slug description metaDescription viewCount createdAt category');
    
    const total = await ArticleModel.countDocuments(searchQuery);
    
    // Track search analytics
    for (const article of articles) {
      await this.trackArticleView(article.slug, query);
    }
    
    return {
      articles,
      query,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * Generate sitemap data
   */
  static async generateSitemapData() {
    const articles = await ArticleModel.findSEOOptimized()
      .select('slug updatedAt');
    
    const categories = await CategoryModel.find({ slug: { $exists: true, $ne: null } })
      .select('slug updatedAt');
    
    return {
      articles: articles.map(article => ({
        url: `/articles/${article.slug}`,
        lastmod: article.updatedAt,
        changefreq: 'weekly',
        priority: 0.8
      })),
      categories: categories.map(category => ({
        url: `/articles/category/${category.slug}`,
        lastmod: category.updatedAt,
        changefreq: 'weekly',
        priority: 0.6
      }))
    };
  }
}

module.exports = SEOService;
