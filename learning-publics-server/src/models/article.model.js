const { Schema, model } = require("mongoose");
const { CONSTANTS } = require("../config");
const slugify = require("slugify");

const ArticleSchema = new Schema({
  articleId: {
    type: String,
    trim: true,
    indexed: true,
    },
  author: {
    type: String,
    required: true,
    trim: true,
    indexed: true
  },
  email: {
    type: String,
    required: true,
    trim: true
  },
  phone: {
    type: String,
    required: true,
    trim: true,
  },
  country: {
    type: String,
    trim: true,
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true,
  },
  status: {
    type: String,
    required: true,
    enum: CONSTANTS.ARTICLE_STATUS,
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: "Categorie",
    required: true,
  },
  admin: {
    type: Schema.Types.ObjectId,
    ref: "Account",
    index: true,
  },
  format: {
    type: String,
    enum: CONSTANTS.ARTICLE_FORMAT,
  },
  publicId: {
    type: String,
    required: true,
  },
  articleUrl: {
    type: String,
    required: true,
  },
  coverPage:[{}],
  volume: {
    type: String,
  },
  editors:[{}],
  // comment:[{}],

  // SEO Fields
  slug: {
    type: String,
    unique: true,
    trim: true,
    lowercase: true,
    index: true,
    sparse: true // Allow null values for existing articles
  },
  metaDescription: {
    type: String,
    trim: true,
    maxlength: 160
  },
  keywords: [{
    type: String,
    trim: true
  }],
  canonicalUrl: {
    type: String,
    trim: true
  },

  // Comprehensive Submission Fields
  abstract: {
    type: String,
    trim: true
  },
  manuscriptType: {
    type: String,
    trim: true,
    enum: ['original-research', 'review', 'case-study', 'short-communication', 'letter', 'editorial']
  },
  institution: {
    type: String,
    trim: true
  },
  orcid: {
    type: String,
    trim: true
  },
  coAuthors: {
    type: String,
    trim: true
  },
  wordCount: {
    type: Number,
    min: 0
  },
  references: {
    type: Number,
    min: 0
  },
  figures: {
    type: Number,
    min: 0
  },
  tables: {
    type: Number,
    min: 0
  },
  supplementaryFiles: {
    type: String,
    trim: true
  },
  fundingInfo: {
    type: String,
    trim: true
  },
  conflictOfInterest: {
    type: String,
    trim: true
  },
  coverLetter: {
    type: String,
    trim: true
  },
  ethicsStatement: {
    type: String,
    trim: true
  },
  dataAvailability: {
    type: String,
    trim: true
  },

  // Declarations
  originalWork: {
    type: Boolean,
    default: false
  },
  noConflict: {
    type: Boolean,
    default: false
  },
  ethicsApproval: {
    type: Boolean,
    default: false
  },
  consentToPublish: {
    type: Boolean,
    default: false
  },
  agreeToTerms: {
    type: Boolean,
    default: false
  },

  // SEO Analytics
  viewCount: {
    type: Number,
    default: 0,
    min: 0
  },
  lastViewed: {
    type: Date
  },
  searchKeywords: [{
    keyword: String,
    count: { type: Number, default: 1 }
  }],

  // Social Media
  socialShares: {
    facebook: { type: Number, default: 0 },
    twitter: { type: Number, default: 0 },
    linkedin: { type: Number, default: 0 }
  }
},
{timestamps: true}
);

// Indexes for SEO optimization
ArticleSchema.index({ slug: 1 });
ArticleSchema.index({ status: 1, createdAt: -1 });
ArticleSchema.index({ category: 1, status: 1 });
ArticleSchema.index({ keywords: 1 });
ArticleSchema.index({ viewCount: -1 });
ArticleSchema.index({ 'title': 'text', 'description': 'text', 'keywords': 'text' });

// Generate unique slug before saving
ArticleSchema.pre('save', async function(next) {
  if (this.isModified('title') && !this.slug) {
    this.slug = await generateUniqueSlug(this.title, this._id);
  }

  // Auto-generate meta description if not provided
  if (this.isModified('description') && !this.metaDescription) {
    this.metaDescription = this.description.length > 160
      ? this.description.substring(0, 157) + '...'
      : this.description;
  }

  // Generate canonical URL
  if (this.slug && !this.canonicalUrl) {
    this.canonicalUrl = `${process.env.FRONTEND_URL || 'https://learningpublics.com'}/articles/${this.slug}`;
  }

  next();
});

// Helper function to generate unique slug
async function generateUniqueSlug(title, articleId = null) {
  let baseSlug = slugify(title, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });

  let slug = baseSlug;
  let counter = 1;

  while (true) {
    const existingArticle = await model("Article").findOne({
      slug: slug,
      ...(articleId && { _id: { $ne: articleId } })
    });

    if (!existingArticle) {
      break;
    }

    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}

// Instance method to increment view count using atomic operations
ArticleSchema.methods.incrementViewCount = function() {
  // Use atomic update to avoid version conflicts
  return this.constructor.findByIdAndUpdate(
    this._id,
    {
      $inc: { viewCount: 1 },
      $set: { lastViewed: new Date() }
    },
    { new: true }
  );
};

// Static method to find by slug
ArticleSchema.statics.findBySlug = function(slug) {
  return this.findOne({ slug: slug, status: 'published' })
    .populate('category', 'name slug')
    .populate('admin', 'name');
};

// Static method to get SEO-optimized articles
ArticleSchema.statics.findSEOOptimized = function(filters = {}) {
  return this.find({
    status: 'published',
    slug: { $exists: true, $ne: null },
    ...filters
  })
  .populate('category', 'name slug')
  .populate('admin', 'name')
  .sort({ viewCount: -1, createdAt: -1 });
};

const ArticleModel = model("Article", ArticleSchema);
module.exports = ArticleModel;