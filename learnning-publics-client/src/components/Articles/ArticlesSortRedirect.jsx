import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

const ArticlesSortRedirect = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  useEffect(() => {
    const sortBy = searchParams.get('by');
    const params = new URLSearchParams();
    
    // Convert old sort parameters to new format
    if (sortBy === 'author') {
      params.set('sortBy', 'author');
      params.set('sortOrder', 'asc');
    } else if (sortBy === 'title') {
      params.set('sortBy', 'title');
      params.set('sortOrder', 'asc');
    }
    
    // Redirect to new unified articles page
    const newUrl = params.toString() ? `/articles?${params.toString()}` : '/articles';
    navigate(newUrl, { replace: true });
  }, [navigate, searchParams]);

  return (
    <div className="flex justify-center items-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to articles...</p>
      </div>
    </div>
  );
};

export default ArticlesSortRedirect;
