import React from 'react';
import { Link, useParams, useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

const ArticleNotFound = () => {
  const { slug } = useParams();
  const location = useLocation();

  return (
    <>
      <Helmet>
        <title>Article Not Found - Learning Publics Journal</title>
        <meta name="description" content="The requested article could not be found. Browse our latest articles or search for specific content." />
        <meta name="robots" content="noindex, nofollow" />
      </Helmet>

      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 text-center">
          <div>
            <div className="mx-auto h-24 w-24 text-gray-400">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            
            <h1 className="mt-6 text-3xl font-extrabold text-gray-900">
              Article Not Found
            </h1>
            
            <p className="mt-2 text-sm text-gray-600">
              {slug ? (
                <>The article "<span className="font-medium">{slug.replace(/-/g, ' ')}</span>" could not be found.</>
              ) : (
                'The requested article could not be found.'
              )}
            </p>
            
            <p className="mt-4 text-sm text-gray-500">
              This article may have been moved, deleted, or the URL might be incorrect.
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex flex-col space-y-3">
              <Link
                to="/articles"
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Browse All Articles
              </Link>
              
              <Link
                to="/articles/search"
                className="group relative w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Search Articles
              </Link>
              
              <Link
                to="/"
                className="group relative w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Go to Homepage
              </Link>
            </div>

            {/* Suggested actions */}
            <div className="mt-8 border-t border-gray-200 pt-6">
              <h3 className="text-sm font-medium text-gray-900 mb-4">
                What you can do:
              </h3>
              <ul className="text-sm text-gray-600 space-y-2 text-left">
                <li className="flex items-start">
                  <span className="flex-shrink-0 h-1.5 w-1.5 bg-gray-400 rounded-full mt-2 mr-3"></span>
                  Check the URL for any typos
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 h-1.5 w-1.5 bg-gray-400 rounded-full mt-2 mr-3"></span>
                  Browse our latest articles
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 h-1.5 w-1.5 bg-gray-400 rounded-full mt-2 mr-3"></span>
                  Use the search function to find specific content
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 h-1.5 w-1.5 bg-gray-400 rounded-full mt-2 mr-3"></span>
                  Contact us if you believe this is an error
                </li>
              </ul>
            </div>

            {/* Debug info for development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-6 p-4 bg-gray-100 rounded-lg text-left">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Debug Info:</h4>
                <div className="text-xs text-gray-600 space-y-1">
                  <div><strong>Slug:</strong> {slug || 'N/A'}</div>
                  <div><strong>Path:</strong> {location.pathname}</div>
                  <div><strong>Search:</strong> {location.search || 'N/A'}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ArticleNotFound;
