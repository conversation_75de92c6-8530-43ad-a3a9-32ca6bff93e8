import "./App.css";
import React, { Suspense } from "react";

import {
  ABOUT_ROUTE,
  ACCOUNT_ROUTE,
  ADMIN_COURSE_ROUTE,
  ADMIN_DASHBOARD_ROUTE,
  ADMIN_PLAN_ROUTE,
  ADMI<PERSON>_SETTINGS,
  ADMIN_PRICING_ROUTE,
  ADMIN_ANALYTICS_ROUTE,
  ADMIN_SIGNUP_ROUTE,
  AGRIC_ROUTE,
  ARTICLES_ROUTE,
  ARTICLE_ROUTE,
  BLOG_ROUTE,
  CHECKOUT_ROUTE,
  COURSES_ROUTE,
  COURSE_ROUTE,
  CREATE_ACCOUNT_ROUTE,
  DASHBOARD_ROUTE,
  <PERSON><PERSON>ARN_ROUTE,
  ENGINE_ROUTE,
  FORGOT_PASSWORD_ROUTE,
  HOME_ROUTE,
  HSS_ROUTE,
  JAMB_ROUTE,
  JOURNAL_ROUTE,
  LOGIN_ADMIN_ROUTE,
  LOGIN_ROUTE,
  MED_ROUTE,
  MGT_ROUTE,
  PAYMENT_ROUTE,
  RESET_PASSWORD_ROUTE,
  SFR_ROUTE,
  SIDEBAR_ARTICLE_ROUTE,
  SIDEBAR_AUTHOR_ROUTE,
  SIDEBAR_EDITORIAL_ROUTE,
  STAFF_ROUTE,
  SUBMIT_ROUTE,
  SUPPORT_ROUTE,
  UNAUTHORIZED_ROUTE,
  VERIFY_ARTICLE_ROUTE,
  VERIFY_INVITE_ROUTE,
  VERIFY_OTP_ROUTE,
  VERIFY_RECOVERY_ROUTE,
  WAEC_ROUTE,
  PUBLISHING_GUIDE_ROUTE,
  // New SEO-friendly routes
  ARTICLES_BASE_ROUTE,
  ARTICLE_BY_SLUG_ROUTE,
  ARTICLES_BY_CATEGORY_ROUTE,
  ARTICLES_SEARCH_ROUTE,
  JOURNALS_BASE_ROUTE,
  JOURNAL_BY_SLUG_ROUTE,
  JOURNALS_BY_CATEGORY_ROUTE,
  LEGACY_ARTICLE_ROUTE
} from "routes";
import { BrowserRouter, Route, Routes } from "react-router-dom";

import AboutPage from "pages/AboutPage";
import AccountPg from "pages/AccountPg";
import AdminCoursePg from "pages/AdminCoursePg";
import AdminDashboardPg from "pages/AdminDashboardPg";
import AdminPricingPg from "pages/AdminPricingPg";
import AdminAnalyticsPg from "pages/AdminAnalyticsPg";
import AdminRegisterPg from "pages/AdminRegister";
import AdminSettings from "components/Admin/settings";
import AgricPage from "pages/Articles/AgricPage";
import ArticlePg from "pages/ArticlePg";
import ArticlesSortRedirect from "components/Articles/ArticlesSortRedirect";
import ArticlesPg from "pages/ArticlesPg";
import ArticlesViewPage from "pages/ArticlesViewPage";
import Author from "components/Home/side-bar/author";
import BlogPage from "./pages/BlogPage";
import BuyCourseProvider from "components/context/epaymentContext";
import CheckoutPg from "pages/checkoutPg";
import CoursePg from "pages/CoursePg";
import CoursesPg from "pages/CoursesPg";
import DashboardPg from "pages/DashboardPg";
import EditorialTeam from "components/Home/side-bar/editorial-team";
import Elearn from "components/e-learn/Elearn";
import EnginePage from "pages/EnginePage";
import ForgotPasswordPg from "pages/ForgotPasswordPg";
import { GlobalStyle } from "globalStyles";
import HomeComp from "components/Home";
import HomePage from "./pages";
import HumanSocialPage from "pages/HumanSocialPage";
import JambCoursesPg from "pages/JambCoursesPg";
import JournalPage from "./pages/JournalPage";
import JournalsViewPage from "pages/JournalsViewPage";
import LoginPage from "pages/Login";
import ManagementPage from "pages/ManagementPage";
import MedResearchPage from "pages/MedResearchPage";
import PageNotFound from "pages/Pg404";
import PaymentPg from "pages/PaymentPg";
import PlanPg from "pages/PlanPg";
import { ProtectedRoute } from "components/protectedRoutes/ProtectedRoute";
import PublishingGuidePage from "pages/PublishingGuidePage";
import ResetPg from "pages/ResetPg";
import ScienceFrontierPage from "pages/ScienceFrontierPage";
import SignupPage from "pages/signup";
import StaffsPg from "pages/StaffsPg";
import SubmitPage from "pages/submit";
import SupportPage from "./pages/SupportPage";
import { Toaster } from "react-hot-toast";
import Unauthorized from "pages/Unauthorized";
import VerifyInvitePg from "pages/VerifyInvite";
import VerifyOtp from "components/Auth/CreateAccount/VerifyOtp";
import VerifyRecoveryPg from "pages/VerifyRecoveryPg";
import VerifyView from "components/Articles/VerifyView";
import WaecCorsesPg from "pages/WaecCorsesPg";

// New footer pages
import AuthorGuidelinesPage from "pages/AuthorGuidelines";
import PrivacyPolicyPage from "pages/PrivacyPolicy";
import EditorialBoardPage from "pages/EditorialBoard";
import TermsOfServicePage from "pages/TermsOfService";
import AboutUsPage from "pages/AboutUs";

// Layout components for modern routing (not lazy-loaded as they're core)
import ArticlesLayout from "components/Layout/ArticlesLayout";
import JournalsLayout from "components/Layout/JournalsLayout";

// Error Boundary and Loading Components
import ErrorBoundary from "components/ErrorBoundary/ErrorBoundary";
import LoadingSpinner, { ArticleLoadingSkeleton, ArticlesListLoadingSkeleton } from "components/Common/LoadingSpinner";
import ScrollToTop from "components/Common/ScrollToTop";

// Lazy-loaded Modern page components
const ArticlesListPage = React.lazy(() => import("pages/Articles/ArticlesListPage"));
const UnifiedArticlesPage = React.lazy(() => import("pages/Articles/UnifiedArticlesPage"));
const ArticleViewPage = React.lazy(() => import("pages/Articles/ArticleViewPage"));
const ArticlesByCategoryPage = React.lazy(() => import("pages/Articles/ArticlesByCategoryPage"));
const ArticlesSearchPage = React.lazy(() => import("pages/Articles/ArticlesSearchPage"));
const JournalsListPage = React.lazy(() => import("pages/Journals/JournalsListPage"));
const JournalViewPage = React.lazy(() => import("pages/Journals/JournalViewPage"));
const JournalsByCategoryPage = React.lazy(() => import("pages/Journals/JournalsByCategoryPage"));
const LegacyArticleRedirect = React.lazy(() => import("components/Articles/LegacyArticleRedirect"));

// import { useState, useEffect } from "react";

// import ELearningPage from "./pages/ELearningPage";

// import { AuthRoutes } from "components/protectedRoutes/UserAuthRoute";

// import LoginAdmin from "components/Auth/LoginAdmin";

// import ArticlesComp from "components/Articles/ArticlesComp";

function App() {
  return (
    <ErrorBoundary>
      <div
        style={{
          background: "linear-gradient(to bottom right, #ffffff, #f4f4f4)"
        }}
      >
        <Toaster />

        <GlobalStyle />
        <BuyCourseProvider>
          <BrowserRouter>
            <ScrollToTop />
          <Routes>
            <Route
              path={HOME_ROUTE}
              element={<HomePage />}
              errorElement={
                <div className="w-full flex item-center justify-center font-bold text-2xl">
                  <p>Something went wrong!!</p>
                </div>
              }
            >
              <Route index element={<HomeComp />} />
              <Route path={SIDEBAR_AUTHOR_ROUTE} element={<Author />} />
              <Route path={SIDEBAR_ARTICLE_ROUTE} element={<ArticlesSortRedirect />} />
              <Route
                path={SIDEBAR_EDITORIAL_ROUTE}
                element={<EditorialTeam />}
              />
            </Route>
            <Route path={JOURNAL_ROUTE} element={<JournalPage />} />
            <Route path={VERIFY_ARTICLE_ROUTE} element={<VerifyView />} />
            <Route path={SUBMIT_ROUTE} element={<SubmitPage />} />
            <Route path={SUPPORT_ROUTE} element={<SupportPage />} />
            <Route path={ELEARN_ROUTE} element={<Elearn />} />
            <Route path={BLOG_ROUTE} element={<BlogPage />} />
            <Route path={ABOUT_ROUTE} element={<AboutPage />} />
            <Route path={PUBLISHING_GUIDE_ROUTE} element={<PublishingGuidePage />} />

            {/* Modern SEO-friendly Article Routes with Lazy Loading */}
            <Route path={ARTICLES_BASE_ROUTE} element={
              <ErrorBoundary>
                <ArticlesLayout />
              </ErrorBoundary>
            }>
              <Route index element={
                <Suspense fallback={<ArticlesListLoadingSkeleton />}>
                  <UnifiedArticlesPage />
                </Suspense>
              } />
              <Route path={ARTICLE_BY_SLUG_ROUTE.replace('/articles/', '')} element={
                <Suspense fallback={<ArticleLoadingSkeleton />}>
                  <ArticleViewPage />
                </Suspense>
              } />
              <Route path={ARTICLES_BY_CATEGORY_ROUTE.replace('/articles/', '')} element={
                <Suspense fallback={<ArticlesListLoadingSkeleton />}>
                  <ArticlesByCategoryPage />
                </Suspense>
              } />
              <Route path={ARTICLES_SEARCH_ROUTE.replace('/articles/', '')} element={
                <Suspense fallback={<ArticlesListLoadingSkeleton />}>
                  <ArticlesSearchPage />
                </Suspense>
              } />
            </Route>

            {/* Modern SEO-friendly Journal Routes with Lazy Loading */}
            <Route path={JOURNALS_BASE_ROUTE} element={
              <ErrorBoundary>
                <JournalsLayout />
              </ErrorBoundary>
            }>
              <Route index element={
                <Suspense fallback={<ArticlesListLoadingSkeleton />}>
                  <JournalsListPage />
                </Suspense>
              } />
              <Route path={JOURNAL_BY_SLUG_ROUTE.replace('/journals/', '')} element={
                <Suspense fallback={<ArticleLoadingSkeleton />}>
                  <JournalViewPage />
                </Suspense>
              } />
              <Route path={JOURNALS_BY_CATEGORY_ROUTE.replace('/journals/', '')} element={
                <Suspense fallback={<ArticlesListLoadingSkeleton />}>
                  <JournalsByCategoryPage />
                </Suspense>
              } />
            </Route>

            {/* Legacy routes for backward compatibility */}
            <Route path="journals/:id" element={<JournalsViewPage />} />
            <Route path="articles/:id" element={<ArticlesViewPage />} />
            <Route path={LEGACY_ARTICLE_ROUTE} element={
              <Suspense fallback={<LoadingSpinner text="Redirecting..." />}>
                <LegacyArticleRedirect />
              </Suspense>
            } />

            <Route path={SFR_ROUTE} element={<ScienceFrontierPage />} />

            <Route path={MGT_ROUTE} element={<ManagementPage />} />
            <Route path={ENGINE_ROUTE} element={<EnginePage />} />
            <Route path={AGRIC_ROUTE} element={<AgricPage />} />
            <Route path={MED_ROUTE} element={<MedResearchPage />} />
            <Route path={HSS_ROUTE} element={<HumanSocialPage />} />

            {/* New footer pages */}
            <Route path="/guidelines" element={<AuthorGuidelinesPage />} />
            <Route path="/support" element={<SupportPage />} />
            <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
            <Route path="/editorial-board" element={<EditorialBoardPage />} />
            <Route path="/terms-of-service" element={<TermsOfServicePage />} />
            <Route path="/about" element={<AboutUsPage />} />

            <Route
              path={CREATE_ACCOUNT_ROUTE}
              element={
                // <AuthRoutes>
                <SignupPage />
                /* </AuthRoutes> */
              }
            />
            <Route
              path={VERIFY_OTP_ROUTE}
              element={
                // <AuthRoutes>
                <VerifyOtp />
                /* </AuthRoutes> */
              }
            />
            <Route
              path={LOGIN_ROUTE}
              element={
                // <AuthRoutes>
                <LoginPage />
                /* </AuthRoutes> */
              }
            />
            <Route
              path={LOGIN_ADMIN_ROUTE}
              element={
                // <AuthRoutes>
                <LoginPage />
                /* </AuthRoutes> */
              }
            />
            <Route
              path={ADMIN_SIGNUP_ROUTE}
              element={
                // <AuthRoutes>
                <AdminRegisterPg />
                /* </AuthRoutes> */
              }
            />
            <Route
              path={DASHBOARD_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={["user", "editor", "sub", "super", "formatter"]}
                >
                  <DashboardPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={FORGOT_PASSWORD_ROUTE}
              element={
                // <AuthRoutes>
                <ForgotPasswordPg />
                /* </AuthRoutes> */
              }
            />
            <Route
              path={VERIFY_RECOVERY_ROUTE}
              element={
                // <AuthRoutes>
                <VerifyRecoveryPg />
                /* </AuthRoutes> */
              }
            />
            <Route
              path={RESET_PASSWORD_ROUTE}
              element={
                // <AuthRoutes>
                <ResetPg />
                /* </AuthRoutes> */
              }
            />
            <Route
              path={COURSES_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={[
                    "user",
                    "editor",
                    "sub",
                    "super",
                    "formatter",
                    "e-learning"
                  ]}
                >
                  <CoursesPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={JAMB_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={[
                    "user",
                    "editor",
                    "sub",
                    "super",
                    "formatter",
                    "e-learning"
                  ]}
                >
                  <JambCoursesPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={WAEC_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={[
                    "user",
                    "editor",
                    "sub",
                    "super",
                    "formatter",
                    "e-learning"
                  ]}
                >
                  <WaecCorsesPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={PAYMENT_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={[
                    "user",
                    "editor",
                    "sub",
                    "super",
                    "formatter",
                    "e-learning"
                  ]}
                >
                  <PaymentPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={ACCOUNT_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={[
                    "user",
                    "editor",
                    "sub",
                    "super",
                    "formatter",
                    "e-learning"
                  ]}
                >
                  <AccountPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={CHECKOUT_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={[
                    "user",
                    "editor",
                    "sub",
                    "super",
                    "formatter",
                    "e-learning"
                  ]}
                >
                  <CheckoutPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={ADMIN_DASHBOARD_ROUTE}
              element={
                // <ProtectedRoute allowedRoles={["editor","sub","super","formatter"]}>
                <AdminDashboardPg />
                // </ProtectedRoute>
              }
            />
            <Route
              path={ARTICLES_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={[
                    "editor",
                    "sub",
                    "super",
                    "formatter",
                    "e-learning"
                  ]}
                >
                  <ArticlesPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={ADMIN_PLAN_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={[
                    "editor",
                    "sub",
                    "super",
                    "formatter",
                    "e-learning"
                  ]}
                >
                  <PlanPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={ADMIN_SETTINGS}
              element={
                <ProtectedRoute
                  allowedRoles={["editor", "sub", "super", "formatter"]}
                >
                  <AdminSettings />
                </ProtectedRoute>
              }
            />
            <Route
              path={COURSE_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={["user", "editor", "sub", "super", "formatter"]}
                >
                  <CoursePg />
                </ProtectedRoute>
              }
            />
            {/* <Route
              path={ACCOUNT_ROUTE}
              element={
                <ProtectedRoute allowedRoles={['user',"editor","sub","super","formatter"]}>
                  <Account />
                </ProtectedRoute>
              }
            /> */}

            <Route
              path={ARTICLE_ROUTE}
              element={
                <ProtectedRoute
                  allowedRoles={["editor", "sub", "super", "formatter"]}
                >
                  <ArticlePg />
                </ProtectedRoute>
              }
            />
            <Route
              path={ADMIN_COURSE_ROUTE}
              element={
                // <ProtectedRoute allowedRoles={["editor","sub","super","formatter"]}>
                <AdminCoursePg />
                // </ProtectedRoute>
              }
            />
            <Route
              path={ADMIN_PRICING_ROUTE}
              element={
                <ProtectedRoute allowedRoles={["super", "editor"]}>
                  <AdminPricingPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={ADMIN_ANALYTICS_ROUTE}
              element={
                <ProtectedRoute allowedRoles={["super", "editor"]}>
                  <AdminAnalyticsPg />
                </ProtectedRoute>
              }
            />
            <Route
              path={STAFF_ROUTE}
              element={
                // <ProtectedRoute allowedRoles={["super"]}>
                <StaffsPg />
                // </ProtectedRoute>
              }
            />
            <Route
              path={VERIFY_INVITE_ROUTE}
              element={
                // <ProtectedRoute allowedRoles={["user", "editor","sub","super","formatter"]}>
                <VerifyInvitePg />
                //  </ProtectedRoute>
              }
            />
            <Route path={UNAUTHORIZED_ROUTE} element={<Unauthorized />} />
            <Route path="*" element={<PageNotFound />} />
          </Routes>
        </BrowserRouter>
      </BuyCourseProvider>
    </div>
    </ErrorBoundary>
  );
}

export default App;
