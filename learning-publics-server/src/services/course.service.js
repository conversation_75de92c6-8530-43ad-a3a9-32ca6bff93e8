const CourseModel = require("../models/course.model");
const PlanModel = require("../models/plan.model");

exports.create = async (details) => {
  try {
    return await CourseModel.create({...details});
  } catch (error) {
    return {error};
  }
} 

exports.delete = async (id) => {
  try {
    const exist = await CourseModel.findByIdAndRemove({_id:id});
    if(!exist) return {error: "Course does not exist"};
    return exist;
  } catch (error) {
    return {error};
  }
}
exports.course = async () => {
  try {
    return await CourseModel.find({});
  } catch (error) {
    return {error};
  }
}
exports.courseByID = async (courseId) => {
  try {
    return await CourseModel.findOne({_id:courseId}).exec();
  } catch (error) {
    return {error};
  }
}
exports.update = async (id, details) => {
  try {
    const exist = await CourseModel.findByIdAndUpdate({_id:id}, {...details}, {returnOriginal:false});
    if (!exist) return {error: "Course does not exist"};
    return exist;
  } catch (error) {
    return {error};
  }
};
exports.notInPlan = async () => {
  try{
    const notInUse =[];
    const courseIds =[];
    const courses = await CourseModel.find({}).exec();
    if(!courses || courses.length === 0) return {error:"No course found"}
    const planCourse = await PlanModel.find({}).exec(); 
    if(planCourse.length > 0){
      courses.map((cur) => {
        planCourse.forEach((element)=>{   
          if(element.courses.includes(cur._id) === false){
            if(courseIds.includes(cur._id) === false){
              notInUse.push(cur);
              courseIds.push(cur._id);
            }
          }
        })
      })
    }else{
      return courses
    }  
    return notInUse;
  }catch(error){
    return {error};
  }
}
