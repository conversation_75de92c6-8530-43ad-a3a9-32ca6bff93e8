const ACTIONS = {  
  MESSAGE: "msg",
  EMAIL: "email",
  COMPLETED: { success: true, msg: "Operation Successful" },
  RESET_PASSWORD: "Password Recovery",
  INCOMPLETE_REG: "Existing incomplete registration",
};

const PLANS = {
  BEGINNER: "beginner",
  PERSONAL: "personal",
  PROFESSIONAL: "professional",
  ENTREPRENEUR: "entrepreneur",
};

const ERROR_FIELD = {
  INVALID_EMAIL: "Email is Invalid",
  JWT_EXPIRED: "jwt expired",
  EXPIRED_TOKEN: "Token has expired",
  NOT_FOUND: "No Record Found",
  ACCOUNT_NOT_FOUND: "Account was not found",
  INVALID_LINK: "Invalid Link",
  INVITE_EXPIRED: "Invitation have expire",
  INVALID_INVITE: "Invite link is invalid",
  REG_FAILED: "Registration Mail Failed",
  INVALID_OTP: "Invalid OTP",
  INVALID_TOKEN: "Invalid Token",
  TOKEN_NOT_FOUND: "Token not found",
  ARTICLE_UPLOAD_MAIL: "Article upload mail failed",
  PLAN_FAIL: "Plan Creation Failed",
};

const META = {
  CATEGORY_SERVICE: "category-service",
  ACCOUNT_SERVICE: "account-service",
  ARTICLE_SERVICE: "article-service",
  CLOUDINARY_SERVICE: "article-service",
  MAIL_SERVICE: "email-service",
  COURSE_SERVICE: "course-service",
  PLAN_SERVICE: "plan-service",
  DOWNLOAD_SERVICE: "download-service",
  PAYSTACK_SERVICE: "paystack-service",
  PAYSTACK_PLAN_SERVICE: "paystack-course-service",
  AUTH_SERVICE: "auth-service",
};

module.exports = {
  ERROR_FIELD,
  PLANS,
  ACTIONS,
  META,
};
