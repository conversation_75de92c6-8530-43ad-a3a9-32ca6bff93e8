import React, { useState, useEffect } from 'react';
import { usePaystackPayment } from 'react-paystack';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { toast } from 'react-hot-toast';
import Colors from 'utils/colors';
import { 
  PAYSTACK_CONFIG, 
  formatCurrency, 
  generatePaymentReference, 
  validatePaystackConfig,
  toNaira 
} from 'config/paystack';

const PaymentContainer = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
`;

const PaymentHeader = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const PaymentTitle = styled.h2`
  font-size: 1.8rem;
  font-weight: 700;
  color: ${Colors.primary};
  margin-bottom: 0.5rem;
`;

const PaymentSubtitle = styled.p`
  color: #64748b;
  font-size: 1rem;
`;

const PaymentDetails = styled.div`
  background: #f8fafc;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #e2e8f0;
`;

const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  
  &:last-child {
    margin-bottom: 0;
    padding-top: 1rem;
    border-top: 2px solid #e2e8f0;
    font-weight: 700;
    font-size: 1.1rem;
  }
`;

const DetailLabel = styled.span`
  color: #374151;
  font-weight: 500;
`;

const DetailValue = styled.span`
  color: ${Colors.primary};
  font-weight: 600;
`;

const PaymentButton = styled.button`
  width: 100%;
  background: linear-gradient(135deg, #16a34a, #15803d);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const SecurityInfo = styled.div`
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
  text-align: center;
`;

const SecurityText = styled.p`
  color: #92400e;
  font-size: 0.875rem;
  margin: 0;
  
  .icon {
    margin-right: 0.5rem;
  }
`;

const FreeNotice = styled.div`
  background: #dcfce7;
  border: 1px solid #16a34a;
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-align: center;
  margin-bottom: 2rem;
`;

const FreeNoticeText = styled.p`
  color: #15803d;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
`;

const PaystackPayment = ({ 
  paymentData, 
  onSuccess, 
  onClose, 
  userEmail, 
  userName 
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentConfig, setPaymentConfig] = useState(null);

  useEffect(() => {
    if (!validatePaystackConfig()) {
      toast.error('Payment system not properly configured');
      return;
    }

    const config = {
      reference: generatePaymentReference(paymentData.type, paymentData.userId),
      email: userEmail || paymentData.email || '<EMAIL>',
      amount: paymentData.amount, // Amount should already be in kobo
      currency: PAYSTACK_CONFIG.CURRENCY,
      publicKey: PAYSTACK_CONFIG.publicKey,
      channels: PAYSTACK_CONFIG.CHANNELS,
      metadata: {
        [PAYSTACK_CONFIG.METADATA_KEYS.PAYMENT_TYPE]: paymentData.type,
        [PAYSTACK_CONFIG.METADATA_KEYS.USER_ID]: paymentData.userId || 'anonymous',
        [PAYSTACK_CONFIG.METADATA_KEYS.ARTICLE_ID]: paymentData.articleId || '',
        [PAYSTACK_CONFIG.METADATA_KEYS.SUPPORT_TIER]: paymentData.supportTier || '',
        [PAYSTACK_CONFIG.METADATA_KEYS.JOURNAL_SECTION]: 'learning_publics_journal',
        custom_fields: [
          {
            display_name: "Payment Type",
            variable_name: "payment_type",
            value: paymentData.description || paymentData.type
          },
          {
            display_name: "User Name",
            variable_name: "user_name",
            value: userName || 'Anonymous User'
          }
        ]
      }
    };

    setPaymentConfig(config);
  }, [paymentData, userEmail, userName]);

  const initializePayment = usePaystackPayment(paymentConfig || {});

  const handlePayment = () => {
    if (!paymentConfig) {
      toast.error('Payment configuration error');
      return;
    }

    setIsProcessing(true);

    initializePayment(
      // Success callback
      (reference) => {
        setIsProcessing(false);
        toast.success('Payment successful!');
        
        // Call success handler with payment details
        if (onSuccess) {
          onSuccess({
            reference: reference.reference,
            status: 'success',
            transaction: reference.transaction,
            paymentData: paymentData,
            amount: toNaira(paymentData.amount),
            currency: PAYSTACK_CONFIG.CURRENCY
          });
        }
      },
      // Close callback (user cancelled or error)
      () => {
        setIsProcessing(false);
        toast.info('Payment cancelled');
        if (onClose) {
          onClose();
        }
      }
    );
  };

  const isFreePayment = !paymentData.amount || paymentData.amount === 0;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.3 }}
    >
      <PaymentContainer>
        <PaymentHeader>
          <PaymentTitle>
            {isFreePayment ? 'Confirm Submission' : 'Complete Payment'}
          </PaymentTitle>
          <PaymentSubtitle>
            {isFreePayment 
              ? 'Your submission is free of charge' 
              : 'Secure payment powered by Paystack'
            }
          </PaymentSubtitle>
        </PaymentHeader>

        {isFreePayment && (
          <FreeNotice>
            <FreeNoticeText>
              🎉 Article submission is currently FREE! 
              No payment required.
            </FreeNoticeText>
          </FreeNotice>
        )}

        <PaymentDetails>
          <DetailRow>
            <DetailLabel>Service:</DetailLabel>
            <DetailValue>{paymentData.description || paymentData.type}</DetailValue>
          </DetailRow>
          
          {paymentData.articleTitle && (
            <DetailRow>
              <DetailLabel>Article:</DetailLabel>
              <DetailValue>{paymentData.articleTitle}</DetailValue>
            </DetailRow>
          )}
          
          {paymentData.supportTier && (
            <DetailRow>
              <DetailLabel>Support Tier:</DetailLabel>
              <DetailValue>{paymentData.supportTier}</DetailValue>
            </DetailRow>
          )}
          
          <DetailRow>
            <DetailLabel>Email:</DetailLabel>
            <DetailValue>{userEmail || paymentData.email}</DetailValue>
          </DetailRow>
          
          <DetailRow>
            <DetailLabel>Amount:</DetailLabel>
            <DetailValue>
              {isFreePayment 
                ? 'FREE' 
                : formatCurrency(toNaira(paymentData.amount))
              }
            </DetailValue>
          </DetailRow>
        </PaymentDetails>

        <PaymentButton
          onClick={isFreePayment ? () => onSuccess?.({ 
            reference: 'FREE_SUBMISSION',
            status: 'success',
            amount: 0,
            currency: PAYSTACK_CONFIG.CURRENCY,
            paymentData: paymentData
          }) : handlePayment}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              Processing...
            </>
          ) : (
            <>
              {isFreePayment ? (
                <>
                  ✓ Confirm Submission
                </>
              ) : (
                <>
                  🔒 Pay {formatCurrency(toNaira(paymentData.amount))}
                </>
              )}
            </>
          )}
        </PaymentButton>

        {!isFreePayment && (
          <SecurityInfo>
            <SecurityText>
              <span className="icon">🔒</span>
              Your payment is secured by Paystack with 256-bit SSL encryption. 
              We never store your card details.
            </SecurityText>
          </SecurityInfo>
        )}
      </PaymentContainer>
    </motion.div>
  );
};

export default PaystackPayment;
