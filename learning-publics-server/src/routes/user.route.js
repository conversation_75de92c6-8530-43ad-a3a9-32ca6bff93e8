const express = require('express');
const Controller = require('../controllers');
const { userRequired, adminRequired, notELearningRole } = require('../middlewares/auth.middleware');
const userRoute = express.Router();

userRoute.get("/forgot-password", Controller.AccountController.forgotPassword);
userRoute.post('/recovery-mail', Controller.AccountController.sendRecoverMail);
userRoute.get('/verify-reset', Controller.AccountController.verifyPasswordReset);
userRoute.post('/reset-password', Controller.AccountController.resetPassword);
userRoute.post('/invite', adminRequired,notELearningRole, Controller.AccountController.sendInvitation);
userRoute.post('/verify-invite', Controller.AccountController.verifyAdminRegistration); 
userRoute.patch("/update", userRequired, Controller.AccountController.updateUser); 
userRoute.get("/info", userRequired, Controller.AccountController.info); 
 
module.exports = {
  userRoute,
};
