import styled from "styled-components"
import Colors from "utils/colors"

export const TextInputContainer = styled.div`
	width: ${({ width }) => (width ? width : "100%")};
	display: flex;
	flex-direction: column;
	margin-top: ${({ mt }) => (mt ? mt : "1rem")};

	label {
		color: ${({labelColor}) => labelColor ? labelColor : `${Colors.black}`};
		font-weight: 500;
		font-size: 14px;
		margin-bottom: 0.25rem;
		/* Learning Publics brand color for labels */
		color: #1e3a8a; /* blue-900 */
	}
`
export const InputText = styled.input`
	width: 100%;
	/* Mobile-first responsive height with minimum touch target */
	height: 48px;
	min-height: 44px; /* Minimum touch target for accessibility */
	border: 2px solid #46555C;
	border-radius: 8px;
	padding: 0px 16px;
	margin-top: 0.5rem;
	outline: none;
	font-size: 16px; /* Prevents zoom on iOS */
	transition: all 0.2s ease-in-out;

	/* Learning Publics brand colors for focus states */
	:focus {
		border-color: #dc2626; /* red-600 */
		box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
		background-color: ${Colors.white};
	}

	:active {
		background-color: ${Colors.white};
	}
	:valid {
		background-color: ${Colors.white};
	}

	/* Mobile-optimized placeholder */
	::placeholder {
		color: ${Colors.muted};
		font-size: 16px;
	}

	/* Touch-friendly hover states */
	@media (hover: hover) {
		:hover {
			border-color: #1e3a8a; /* blue-900 */
		}
	}

	/* Disabled state */
	:disabled {
		background-color: #f3f4f6;
		border-color: #d1d5db;
		cursor: not-allowed;
		opacity: 0.6;
	}
`
export const ErrorMsgSpan = styled.p`
	color: #dc2626; /* Learning Publics red-600 */
	font-size: 14px;
	font-weight: 500;
	margin-top: ${({ mt }) => (mt ? mt : "8px")};
	margin-bottom: ${({ mt }) => (mt ? mt : "0px")};
	line-height: 1.4;
	/* Mobile-friendly error text */
	padding: 4px 8px;
	background-color: rgba(220, 38, 38, 0.05);
	border-radius: 4px;
	border-left: 3px solid #dc2626;
`
