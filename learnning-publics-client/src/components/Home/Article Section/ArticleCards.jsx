import React from "react";
import { ArtBody, ArtCardsDiv, ArtSubHead } from "./style";

const ArticleCards = ({
  title = "Global Journal of Computer Science and Technology",
  subTitle = "authored by james",
  body = "Body text"
}) => {
  return (
    <div className="w-full flex flex-col items-center max-w-lg mx-auto p-4 sm:p-6 bg-white rounded-lg shadow-md">
      {/* Mobile-optimized title with Learning Publics brand colors */}
      <h2 className="text-lg sm:text-xl font-bold text-blue-900 text-center leading-tight mb-3 px-2">
        {title}
      </h2>

      {/* Author with improved mobile styling */}
      <h3 className="text-sm sm:text-base font-semibold text-white bg-red-600 px-3 py-1 rounded-full mb-4">
        {subTitle}
      </h3>

      {/* Body text with mobile-first responsive typography */}
      <p className="text-sm sm:text-base text-gray-600 text-center leading-relaxed line-clamp-4">
        {body}
      </p>
    </div>
  );
};

export default ArticleCards;
