#!/bin/bash

# Create E-Learning System Directory Structure
echo "Creating E-Learning System Directory Structure..."

# Create main directory
mkdir -p learning-publics-elearning
cd learning-publics-elearning

# Create root files
cat > README.md << 'EOF'
# Learning Publics E-Learning System

A comprehensive e-learning platform for JSS1-SS3 students, language learning, and exam preparation.

## Modules

1. **Student & Guardian App** - Student learning interface and guardian monitoring
2. **Teacher App** - Classroom management and content creation
3. **Admin & Staff App** - System administration and management
4. **Student & Guardian API** - Backend services for students and guardians
5. **Admin & Staff API** - Backend services for administration

## Quick Start

```bash
# Install dependencies for all modules
npm run install:all

# Start development environment
npm run dev:all

# Start specific module
npm run dev:student
npm run dev:teacher
npm run dev:admin
```

## Technology Stack

- **Frontend**: React.js, Next.js, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express.js, MongoDB, Redis
- **Mobile**: React Native
- **Infrastructure**: Docker, AWS/Azure

## Educational Coverage

- **JSS1-SS3**: Core subjects and curriculum
- **Exam Preparation**: WAEC, JAMB, NECO, Post-UTME
- **Language School**: English, Yoruba, Igbo, Swahili
- **Interactive Learning**: Videos, quizzes, assignments, progress tracking
EOF

cat > package.json << 'EOF'
{
  "name": "learning-publics-elearning",
  "version": "1.0.0",
  "description": "Comprehensive E-Learning Platform",
  "scripts": {
    "install:all": "npm run install:student && npm run install:teacher && npm run install:admin && npm run install:student-api && npm run install:admin-api",
    "install:student": "cd student-guardian-app/frontend && npm install",
    "install:teacher": "cd teacher-app/frontend && npm install",
    "install:admin": "cd admin-staff-app/frontend && npm install",
    "install:student-api": "cd student-guardian-api && npm install",
    "install:admin-api": "cd admin-staff-api && npm install",
    "dev:all": "concurrently \"npm run dev:student\" \"npm run dev:teacher\" \"npm run dev:admin\" \"npm run dev:student-api\" \"npm run dev:admin-api\"",
    "dev:student": "cd student-guardian-app/frontend && npm run dev",
    "dev:teacher": "cd teacher-app/frontend && npm run dev",
    "dev:admin": "cd admin-staff-app/frontend && npm run dev",
    "dev:student-api": "cd student-guardian-api && npm run dev",
    "dev:admin-api": "cd admin-staff-api && npm run dev",
    "build:all": "npm run build:student && npm run build:teacher && npm run build:admin",
    "build:student": "cd student-guardian-app/frontend && npm run build",
    "build:teacher": "cd teacher-app/frontend && npm run build",
    "build:admin": "cd admin-staff-app/frontend && npm run build",
    "test:all": "npm run test:student && npm run test:teacher && npm run test:admin && npm run test:student-api && npm run test:admin-api",
    "test:student": "cd student-guardian-app/frontend && npm test",
    "test:teacher": "cd teacher-app/frontend && npm test",
    "test:admin": "cd admin-staff-app/frontend && npm test",
    "test:student-api": "cd student-guardian-api && npm test",
    "test:admin-api": "cd admin-staff-api && npm test"
  },
  "devDependencies": {
    "concurrently": "^8.2.0"
  },
  "workspaces": [
    "student-guardian-app/frontend",
    "teacher-app/frontend",
    "admin-staff-app/frontend",
    "student-guardian-api",
    "admin-staff-api",
    "shared/*"
  ]
}
EOF

cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # Databases
  mongodb:
    image: mongo:6.0
    container_name: elearning-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:7-alpine
    container_name: elearning-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # APIs
  student-guardian-api:
    build: ./student-guardian-api
    container_name: student-guardian-api
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=*****************************************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongodb
      - redis

  admin-staff-api:
    build: ./admin-staff-api
    container_name: admin-staff-api
    restart: unless-stopped
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=**************************************************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - mongodb
      - redis

  # Frontend Apps
  student-app:
    build: ./student-guardian-app/frontend
    container_name: student-app
    restart: unless-stopped
    ports:
      - "3003:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3001
    depends_on:
      - student-guardian-api

  teacher-app:
    build: ./teacher-app/frontend
    container_name: teacher-app
    restart: unless-stopped
    ports:
      - "3004:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3001
    depends_on:
      - student-guardian-api

  admin-app:
    build: ./admin-staff-app/frontend
    container_name: admin-app
    restart: unless-stopped
    ports:
      - "3005:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3002
    depends_on:
      - admin-staff-api

volumes:
  mongodb_data:
  redis_data:
EOF

cat > .env.example << 'EOF'
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/elearning
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
JWT_EXPIRE=24h
JWT_REFRESH_EXPIRE=7d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Payment Configuration
PAYSTACK_SECRET_KEY=sk_test_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_test_your_paystack_public_key

# File Upload Configuration
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-key
CLOUDINARY_API_SECRET=your-cloudinary-secret

# API Configuration
STUDENT_API_PORT=3001
ADMIN_API_PORT=3002
STUDENT_APP_PORT=3003
TEACHER_APP_PORT=3004
ADMIN_APP_PORT=3005

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Features
ENABLE_NOTIFICATIONS=true
ENABLE_ANALYTICS=true
ENABLE_OFFLINE_MODE=true
EOF

cat > .gitignore << 'EOF'
# Dependencies
node_modules/
*/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
build/
dist/
*/build/
*/dist/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Database
*.db
*.sqlite

# Uploads
uploads/
temp/

# Docker
.docker/

# Cache
.cache/
.parcel-cache/
EOF

echo "✅ Root structure created successfully!"

# Create Module 1: Student & Guardian App
echo "Creating Module 1: Student & Guardian App..."
mkdir -p student-guardian-app/frontend/src/{components,pages,services,hooks,utils,assets}
mkdir -p student-guardian-app/frontend/src/components/{student,guardian,shared,language-school}
mkdir -p student-guardian-app/frontend/src/components/student/{Dashboard,Courses,Assignments,Progress,Exams,Profile}
mkdir -p student-guardian-app/frontend/src/components/guardian/{Dashboard,StudentProgress,Reports,Communication,Billing}
mkdir -p student-guardian-app/frontend/src/components/shared/{Auth,Navigation,Notifications,Common}
mkdir -p student-guardian-app/frontend/src/components/language-school/{English,Yoruba,Igbo,Swahili}
mkdir -p student-guardian-app/frontend/public
mkdir -p student-guardian-app/mobile-app/src

# Create Module 2: Teacher App
echo "Creating Module 2: Teacher App..."
mkdir -p teacher-app/frontend/src/{components,pages,services,utils}
mkdir -p teacher-app/frontend/src/components/{classroom,content,communication,analytics}
mkdir -p teacher-app/frontend/src/components/classroom/{Dashboard,ClassManagement,LessonPlanning,Assignments,Grading,Reports}
mkdir -p teacher-app/frontend/src/components/content/{CourseCreation,MaterialUpload,QuizBuilder,VideoLessons}
mkdir -p teacher-app/frontend/src/components/communication/{StudentChat,ParentCommunication,Announcements,Forums}
mkdir -p teacher-app/frontend/src/components/analytics/{StudentProgress,ClassPerformance,Reports}
mkdir -p teacher-app/mobile-app/src

# Create Module 3: Admin & Staff App
echo "Creating Module 3: Admin & Staff App..."
mkdir -p admin-staff-app/frontend/src/{components,pages,services,utils}
mkdir -p admin-staff-app/frontend/src/components/{super-admin,admin,editor,staff}
mkdir -p admin-staff-app/frontend/src/components/super-admin/{SystemManagement,UserManagement,SchoolManagement,Analytics,Settings}
mkdir -p admin-staff-app/frontend/src/components/admin/{SchoolDashboard,TeacherManagement,StudentManagement,CourseManagement,Reports}
mkdir -p admin-staff-app/frontend/src/components/editor/{ContentReview,CurriculumManagement,QualityAssurance,Publishing}
mkdir -p admin-staff-app/frontend/src/components/staff/{Support,Moderation,TechnicalSupport,CustomerService}
mkdir -p admin-staff-app/mobile-app

# Create Module 4: Student & Guardian API
echo "Creating Module 4: Student & Guardian API..."
mkdir -p student-guardian-api/src/{controllers,models,routes,middleware,services,utils,config}
mkdir -p student-guardian-api/src/controllers/{student,guardian,language-school}
mkdir -p student-guardian-api/{tests,docs}

# Create Module 5: Admin & Staff API
echo "Creating Module 5: Admin & Staff API..."
mkdir -p admin-staff-api/src/{controllers,models,routes,middleware,services,utils,config}
mkdir -p admin-staff-api/src/controllers/{super-admin,admin,editor,staff}
mkdir -p admin-staff-api/{tests,docs}

# Create Shared Libraries
echo "Creating Shared Libraries..."
mkdir -p shared/{ui-components,utils,constants,types,services}

# Create Database & Documentation
mkdir -p database/{migrations,seeds,schemas}
mkdir -p docs/{api,user-guides,development}

# Create Deployment
mkdir -p deployment/{docker,kubernetes,scripts}

echo "🎉 E-Learning System structure created successfully!"
echo ""
echo "Next steps:"
echo "1. Run: chmod +x create-elearning-structure.sh"
echo "2. Run: ./create-elearning-structure.sh"
echo "3. Navigate to learning-publics-elearning directory"
echo "4. Copy .env.example to .env and configure"
echo "5. Run: npm install"
echo "6. Start development: npm run dev:all"
EOF

chmod +x create-elearning-structure.sh

echo "E-Learning structure script created successfully!"
