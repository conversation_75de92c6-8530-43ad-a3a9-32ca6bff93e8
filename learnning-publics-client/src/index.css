@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced scrollbar styles for the entire application */
* {
  scrollbar-width: thin;
  scrollbar-color: #dc2626 #f3f4f6;
}

*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background: #dc2626;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: #b91c1c;
}

*::-webkit-scrollbar-corner {
  background: #f3f4f6;
}


@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
      display: none;
  }
 /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
      -ms-overflow-style: none;  /* IE and Edge */
      scrollbar-width: none;  /* Firefox */
  }

  /* Shimmer animation for skeleton loading */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Scrollable content utilities */
  .scrollable-content {
    overflow-y: auto;
    max-height: 400px;
  }

  .scrollable-modal {
    overflow-y: auto;
    max-height: 80vh;
  }

  .scrollable-dropdown {
    overflow-y: auto;
    max-height: 300px;
  }

  /* Custom scrollbar for specific components */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #3b82f6 #e5e7eb;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #e5e7eb;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #3b82f6;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #2563eb;
  }
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}


/* @tailwind base;
@tailwind components;
@tailwind utilities; */