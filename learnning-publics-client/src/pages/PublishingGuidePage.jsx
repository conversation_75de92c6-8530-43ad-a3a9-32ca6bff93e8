import React from 'react';
import { Link } from 'react-router-dom';
import Mainlayout from 'layout/MainLayout';
import { ArtData } from 'components/Home/Article Section/artCardData';

const PublishingGuidePage = () => {
  return (
    <Mainlayout>
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Publishing Guide
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Learn how to submit and publish your research with Learning Publics. 
              Follow our step-by-step process to get your work reviewed and published.
            </p>
          </div>

          {/* Breadcrumb */}
          <nav className="flex mb-8" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link 
                  to="/" 
                  className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
                >
                  Home
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <span className="mx-2 text-gray-400">/</span>
                  <span className="text-sm font-medium text-gray-500">Publishing Guide</span>
                </div>
              </li>
            </ol>
          </nav>

          {/* Publishing Process Steps */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Publishing Process
            </h2>
            
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {ArtData?.map((step, index) => (
                <div 
                  key={step.id} 
                  className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100 hover:shadow-md transition-shadow duration-200"
                >
                  <div className="flex items-center mb-4">
                    <div className="flex-shrink-0 w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg">
                      {index + 1}
                    </div>
                    <div className="ml-4">
                      {step.icon && (
                        <img 
                          src={step.icon} 
                          alt={step.text || `Step ${index + 1}`} 
                          className="w-8 h-8 object-contain"
                        />
                      )}
                    </div>
                  </div>
                  <p className="text-gray-700 font-medium leading-relaxed">
                    {step.para}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Detailed Instructions */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Detailed Instructions
            </h2>
            
            <div className="space-y-6">
              <div className="border-l-4 border-blue-500 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Step 1: Prepare Your Submission
                </h3>
                <p className="text-gray-700 mb-3">
                  Before submitting your article, ensure you have:
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>A complete manuscript in PDF format</li>
                  <li>Abstract and keywords</li>
                  <li>Author information and affiliations</li>
                  <li>Proper citations and references</li>
                </ul>
              </div>

              <div className="border-l-4 border-green-500 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Step 2: Submit for Review
                </h3>
                <p className="text-gray-700 mb-3">
                  Our editorial team will review your submission for:
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Academic quality and originality</li>
                  <li>Proper formatting and structure</li>
                  <li>Relevance to journal scope</li>
                  <li>Ethical compliance</li>
                </ul>
              </div>

              <div className="border-l-4 border-purple-500 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Step 3: Publication Process
                </h3>
                <p className="text-gray-700 mb-3">
                  Once approved, your article will be:
                </p>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Formatted according to journal standards</li>
                  <li>Assigned a unique identifier</li>
                  <li>Published online with full metadata</li>
                  <li>Made available for citation and download</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg p-8 text-center">
            <h2 className="text-2xl font-bold text-white mb-4">
              Ready to Submit Your Research?
            </h2>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Join thousands of researchers who have published their work with Learning Publics. 
              Start your submission process today.
            </p>
            <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
              <Link
                to="/submit"
                className="inline-block bg-white text-blue-600 font-semibold px-8 py-3 rounded-lg hover:bg-gray-100 transition-colors duration-200"
              >
                Submit Article
              </Link>
              <Link
                to="/journals"
                className="inline-block border-2 border-white text-white font-semibold px-8 py-3 rounded-lg hover:bg-white hover:text-blue-600 transition-colors duration-200"
              >
                Browse Journals
              </Link>
            </div>
          </div>

          {/* Back to Home */}
          <div className="text-center mt-8">
            <Link 
              to="/" 
              className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium"
            >
              ← Back to Homepage
            </Link>
          </div>
        </div>
      </div>
    </Mainlayout>
  );
};

export default PublishingGuidePage;
