import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import Navbar from 'components/Navbar';
import Footer from 'components/Footer';
import Breadcrumbs from 'components/Common/Breadcrumbs';
import MobileBottomNav from 'components/Navigation/MobileBottomNav';
import { generateCanonicalUrl } from 'utils/seo';

const JournalsLayout = () => {
  const location = useLocation();
  const canonicalUrl = generateCanonicalUrl(location.pathname);

  // Generate breadcrumbs based on current path
  const generateBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [{ name: 'Home', url: '/' }];

    if (pathSegments[0] === 'journals') {
      breadcrumbs.push({ name: 'Journals', url: '/journals' });
      
      if (pathSegments[1] === 'category' && pathSegments[2]) {
        breadcrumbs.push({ 
          name: 'Category', 
          url: `/journals/category/${pathSegments[2]}` 
        });
      } else if (pathSegments[1] && pathSegments[1] !== 'category') {
        // Individual journal - breadcrumb will be added by the journal component
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <>
      <Helmet>
        <link rel="canonical" href={canonicalUrl} />
        <meta name="robots" content="index, follow" />
      </Helmet>
      
      <div className="min-h-screen flex flex-col">
        <Navbar />
        
        <main className="flex-grow">
          {/* Breadcrumbs for navigation */}
          {breadcrumbs.length > 1 && (
            <div className="bg-gray-50 py-4">
              <div className="container mx-auto px-4">
                <Breadcrumbs items={breadcrumbs} />
              </div>
            </div>
          )}
          
          {/* Main content area */}
          <div className="container mx-auto px-4 py-8">
            <Outlet />
          </div>
        </main>

        <Footer />
        <MobileBottomNav />
      </div>
    </>
  );
};

export default JournalsLayout;
