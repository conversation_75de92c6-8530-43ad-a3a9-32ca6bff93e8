import React from 'react';
import { DocumentTextIcon, BookOpenIcon, AcademicCapIcon } from '@heroicons/react/24/outline';

const ArticlePlaceholder = ({ title, category, className = "" }) => {
  // Generate a consistent color based on the title or category
  const getPlaceholderColor = (text) => {
    const colors = [
      'from-blue-500 to-blue-600',
      'from-green-500 to-green-600',
      'from-purple-500 to-purple-600',
      'from-red-500 to-red-600',
      'from-indigo-500 to-indigo-600',
      'from-pink-500 to-pink-600',
      'from-yellow-500 to-yellow-600',
      'from-teal-500 to-teal-600'
    ];
    
    const hash = text?.split('').reduce((a, b) => a + b.charCodeAt(0), 0) || 0;
    return colors[hash % colors.length];
  };

  const getIcon = (category) => {
    const categoryLower = category?.toLowerCase() || '';
    
    if (categoryLower.includes('agriculture') || categoryLower.includes('climate')) {
      return <BookOpenIcon className="w-12 h-12 text-white/80" />;
    } else if (categoryLower.includes('education') || categoryLower.includes('learning')) {
      return <AcademicCapIcon className="w-12 h-12 text-white/80" />;
    } else {
      return <DocumentTextIcon className="w-12 h-12 text-white/80" />;
    }
  };

  const gradientColor = getPlaceholderColor(title || category);

  return (
    <div className={`relative overflow-hidden bg-gradient-to-br ${gradientColor} ${className}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="grid grid-cols-6 gap-2 h-full opacity-20">
            {[...Array(24)].map((_, i) => (
              <div key={i} className="bg-white/20 rounded"></div>
            ))}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full p-6 text-center">
        {/* Icon */}
        <div className="mb-4">
          {getIcon(category)}
        </div>

        {/* Title Preview */}
        {title && (
          <div className="space-y-2">
            <div className="h-2 bg-white/60 rounded w-3/4 mx-auto"></div>
            <div className="h-2 bg-white/40 rounded w-1/2 mx-auto"></div>
            <div className="h-2 bg-white/20 rounded w-2/3 mx-auto"></div>
          </div>
        )}

        {/* Learning Publics Badge */}
        <div className="absolute top-3 right-3 bg-white/20 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full font-medium">
          Article
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-white/10 rounded-full translate-y-8 -translate-x-8"></div>
      </div>
    </div>
  );
};

export default ArticlePlaceholder;
