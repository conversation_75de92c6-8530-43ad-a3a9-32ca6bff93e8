import React from 'react';
import { motion } from 'framer-motion';
import { fadeIn } from 'design-system/animations';
import ErrorBoundary from './ErrorBoundary';

// Article-specific error boundary with contextual fallback
export const ArticleErrorBoundary = ({ children, articleId }) => {
  const ArticleFallback = ({ error, retry }) => (
    <motion.div 
      className="p-6 bg-red-50 border border-red-200 rounded-lg text-center max-w-md mx-auto"
      {...fadeIn}
    >
      <div className="text-red-600 mb-4">
        <svg className="mx-auto h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-red-800 mb-2">Article Loading Error</h3>
      <p className="text-red-700 text-sm mb-4">
        Unable to load this article. This might be a temporary issue.
      </p>
      <div className="space-y-2">
        <button
          onClick={retry}
          className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry Loading Article
        </button>
        <button
          onClick={() => window.history.back()}
          className="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
        >
          Go Back
        </button>
      </div>
    </motion.div>
  );

  return (
    <ErrorBoundary 
      fallback={ArticleFallback} 
      componentName={`Article-${articleId || 'Unknown'}`}
    >
      {children}
    </ErrorBoundary>
  );
};

// Admin dashboard error boundary with admin-specific actions
export const AdminErrorBoundary = ({ children, section }) => {
  const AdminFallback = ({ error, retry }) => (
    <motion.div 
      className="p-6 bg-red-50 border border-red-200 rounded-lg"
      {...fadeIn}
    >
      <div className="flex items-center mb-4">
        <div className="text-red-600 mr-3">
          <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 18.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-red-800">Admin Panel Error</h3>
          <p className="text-red-700 text-sm">Error in {section || 'admin section'}</p>
        </div>
      </div>
      <p className="text-red-700 text-sm mb-4">
        There was an issue loading this admin section. Please try again or contact support if the problem persists.
      </p>
      <div className="flex space-x-3">
        <button
          onClick={retry}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
        <button
          onClick={() => window.location.href = '/admin/dashboard'}
          className="px-4 py-2 bg-blue-900 text-white rounded hover:bg-blue-800 transition-colors"
        >
          Back to Dashboard
        </button>
      </div>
    </motion.div>
  );

  return (
    <ErrorBoundary 
      fallback={AdminFallback} 
      componentName={`Admin-${section || 'Unknown'}`}
    >
      {children}
    </ErrorBoundary>
  );
};

// Form error boundary with form-specific recovery options
export const FormErrorBoundary = ({ children, formName }) => {
  const FormFallback = ({ error, retry }) => (
    <motion.div 
      className="p-4 bg-red-50 border border-red-200 rounded-lg"
      {...fadeIn}
    >
      <div className="flex items-center mb-3">
        <div className="text-red-600 mr-2">
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="text-base font-semibold text-red-800">Form Error</h3>
      </div>
      <p className="text-red-700 text-sm mb-3">
        There was an issue with the {formName || 'form'}. Your data may have been preserved.
      </p>
      <button
        onClick={retry}
        className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
      >
        Reload Form
      </button>
    </motion.div>
  );

  return (
    <ErrorBoundary 
      fallback={FormFallback} 
      componentName={`Form-${formName || 'Unknown'}`}
      minimal={true}
    >
      {children}
    </ErrorBoundary>
  );
};

// PDF viewer error boundary with PDF-specific recovery
export const PDFErrorBoundary = ({ children, pdfUrl }) => {
  const PDFFallback = ({ error, retry }) => (
    <motion.div 
      className="p-6 bg-red-50 border border-red-200 rounded-lg text-center"
      {...fadeIn}
    >
      <div className="text-red-600 mb-4">
        <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-red-800 mb-2">PDF Loading Error</h3>
      <p className="text-red-700 text-sm mb-4">
        Unable to load the PDF document. This might be due to a network issue or the file format.
      </p>
      <div className="space-y-2">
        <button
          onClick={retry}
          className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Retry Loading PDF
        </button>
        <button
          onClick={() => window.history.back()}
          className="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
        >
          Go Back to Article
        </button>
      </div>
    </motion.div>
  );

  return (
    <ErrorBoundary 
      fallback={PDFFallback} 
      componentName={`PDF-${pdfUrl ? 'WithURL' : 'NoURL'}`}
    >
      {children}
    </ErrorBoundary>
  );
};

// Data table error boundary for admin tables
export const TableErrorBoundary = ({ children, tableName }) => {
  const TableFallback = ({ error, retry }) => (
    <motion.div 
      className="p-4 bg-red-50 border border-red-200 rounded-lg"
      {...fadeIn}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <div className="text-red-600 mr-2">
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-sm font-semibold text-red-800">Table Loading Error</h3>
        </div>
        <button
          onClick={retry}
          className="px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      </div>
      <p className="text-red-700 text-xs">
        Unable to load {tableName || 'table'} data. Please try again.
      </p>
    </motion.div>
  );

  return (
    <ErrorBoundary 
      fallback={TableFallback} 
      componentName={`Table-${tableName || 'Unknown'}`}
      minimal={true}
    >
      {children}
    </ErrorBoundary>
  );
};
