/* eslint-disable no-unused-vars */
const buildUser = (userObj) => {
  const { _id, __v, password, refreshToken, ...data } = userObj;
  data.id = _id
  return data;
};
const buildUserRole = (userObj) => {
  const { _id, __v, password, refreshToken, username, createdAt, updatedAt, ...data } = userObj;
  data.id =_id;
  return data;
};
const buildTemporalUser = (userObj) => {
  const { _id, __v, createdAt, updatedAt, refreshToken, ...data } = userObj;
  return data;
};
const commonReponse = (msg, data, field = "data", others = {}, op = true) => {
  const response = {
    success: op,
    msg,
    [field]: data,
    ...others,
  };
  return response;
};
const buildProfile = (profileObj) => {
  const { id, userId, ...data } = profileObj;
  return data;
};
const buildPlan = (planObj) => {
  const { id, userId, ...data } = planObj;
  return data;
};
const buildCategory = (categoryObj) => {
  const { _id, __v, createdAt, updatedAt, admin, ...data } = categoryObj;
  data.id = _id;
  return data;
};
const buildAdminCategory = (categoryObj) => {
  const { _id, __v, admin, ...data } = categoryObj;
  data.id = _id; 
  const {username, ...others} =buildUser(admin);
  data.admin = others
  data.admin.id = admin._id;
  return data;
};
const buildArticle = (articleObj) => {
  const {_id, __v, admin, ...data} = articleObj;
  if(data?.articleId) {
    const {articleId, ...all} = data;
    all.id = articleId;
    all.articleId = _id;
    all.adminId = admin;
    return all
  }else{
    data.articleId = _id;
    data.adminId = admin;
    return data;
  }
    
};
const buildSearchArticle = (articleObj) => {
  const {_id, __v, admin, ...data} = articleObj;
  data.articleId = _id;
  
  data.admin = {name:admin.name};
  return data;
};
const buildArticleReview = (articleObj) => {
  const {_id, __v, format,email, admin, ...data} = articleObj;
  data.articleId = _id;
  
  return data;
};
const buildCourse = (courseObj) => {
  const {_id, __v, admin, ...data} = courseObj;
  data.courseId =_id;
  return data;
}
const buildViewCourse = (courseObj) => {
  const {_id, __v, admin, ...data} = courseObj;
  data.courseId =_id;
  return data;
}
const buildPlanView = (planObj) => {
  const {_id,__v, view,...data} = planObj;
  data.planId=_id;
  return data;
}
const buildDownload = (downloadObj) => {
  const {_id,__v, article,admin,...data} = downloadObj;
  data.article = article[0].title;
  data.articleId = article[0]._id;
  data.author = article[0].author;
  data.status = article[0].status;
  data.id =_id;
  return data;
}
const planView = (planObj) => {
  const {_id,...data} = planObj;
  data.planId = _id;
  return data;
}
const buildUserPlan = (userPlanObj) => {
  const { _id, __v,updatedAt,account,planInfo,...data} = userPlanObj;
  const {id,...others} = planInfo[0];
  const plan = others
  plan.planId = id 
return plan;
}
const buildAuditor = (auditorObj) => {
  const {_id, username, ...data} = auditorObj;
  data.id =_id;
  return data;
}
module.exports = {
  buildUser,
  commonReponse,
  buildProfile,
  buildPlan,
  buildTemporalUser,
  buildCategory,
  buildAdminCategory,
  buildArticle,
  buildCourse, 
  buildViewCourse,
  buildPlanView,
  buildDownload, 
  buildUserPlan,
  buildArticleReview,
  buildSearchArticle,
  buildUserRole,
  buildAuditor,
};
