const { Schema, model } = require("mongoose");
const slugify = require("slugify");

const CategorySchema = new Schema({
  name: {
    type: String,
    required: true, // Fixed typo: require -> required
    trim: true,
    unique: true,
  },
  admin: {
    type: Schema.Types.ObjectId,
    ref: "Account",
    required: true
  },
  image:[{}],

  // SEO Fields for Categories
  slug: {
    type: String,
    unique: true,
    trim: true,
    lowercase: true,
    index: true,
    sparse: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  metaDescription: {
    type: String,
    trim: true,
    maxlength: 160
  },
  keywords: [{
    type: String,
    trim: true
  }],

  // Category Analytics
  articleCount: {
    type: Number,
    default: 0,
    min: 0
  },
  viewCount: {
    type: Number,
    default: 0,
    min: 0
  }
},
{timestamps: true}
);

// Generate unique slug before saving
CategorySchema.pre('save', async function(next) {
  if (this.isModified('name') && !this.slug) {
    this.slug = await generateUniqueCategorySlug(this.name, this._id);
  }

  // Auto-generate meta description if not provided
  if (this.isModified('description') && !this.metaDescription && this.description) {
    this.metaDescription = this.description.length > 160
      ? this.description.substring(0, 157) + '...'
      : this.description;
  }

  next();
});

// Helper function to generate unique category slug
async function generateUniqueCategorySlug(name, categoryId = null) {
  let baseSlug = slugify(name, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });

  let slug = baseSlug;
  let counter = 1;

  while (true) {
    const existingCategory = await model("Categorie").findOne({
      slug: slug,
      ...(categoryId && { _id: { $ne: categoryId } })
    });

    if (!existingCategory) {
      break;
    }

    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}

// Static method to find by slug
CategorySchema.statics.findBySlug = function(slug) {
  return this.findOne({ slug: slug });
};

// Indexes for performance
CategorySchema.index({ slug: 1 });
CategorySchema.index({ name: 1 });
CategorySchema.index({ articleCount: -1 });

const CategoryModel = model("Categorie", CategorySchema);
module.exports = CategoryModel;