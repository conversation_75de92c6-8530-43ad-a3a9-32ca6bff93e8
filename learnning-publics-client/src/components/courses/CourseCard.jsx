// import { TiTick } from "react-icons/ti";
// import { Count, PaymentCont } from "./CourseCard.style";

import { ClipLoader } from "react-spinners";
import { override } from "utils/admin-roles";
import { useState } from "react";

// import { Pay } from "utils/assets";
// import { Type } from "./courses.style";

// const paymentOptions = [
//   {
//     offers: [
//       '3 HD video lessons & tutorials',
//       '1 Official exam',
//       '100 Practice questions',
//       '1 Month subscriptions',
//       '1 Free book',
//     ],
//   },
// ]

export default function CourseCard({
  type,
  handler,
  url,
  buttonText,
  title,
  category,
  course,
  plan,

  checkingCourse
}) {
  // eslint-disable-next-line no-unused-vars
  const textDisplay = (val) => {
    switch (val.toUpperCase()) {
      case "WAEC":
        return "W";
      case "JAMB":
        return "J";
      case "combined":
        return "C";
      default:
        return "F";
    }
  };

  // eslint-disable-next-line no-unused-vars
  const [isHovered, setIsHovered] = useState(false);

  // const buttonStyle = {
  //   backgroundColor: isHovered ? "transparent" : "#46555c",
  //   border: isHovered ? "2px solid #46555c" : "none",
  //   color: isHovered ? "#46555c" : "#fff",
  //   padding: "6px 10px",
  //   cursor: "pointer",
  //   transition: "background-color 0.3s, border 0.3s",
  //   whiteSpace: "nowrap",
  // };

  return (
    // <div className=" w-8 h-8 bg-green-700">
    // <div className="min-w-[300px] h-[180px]    bg-green-700 !border border-[#9C4DF4] border-solid p-4 overflow-clip shadow-lg ">
    <div
      className={`min-w-[300px] h-[180px] relative course !border border-[#9C4DF4] border-solid p-4 overflow-clip shadow-lg ${
        checkingCourse.id !== "" ? " pointer-events-none" : ""
      } `}
    >
      <div className="text-[10px] absolute top-0 right-0 bg-[#9C4DF4]/50 items-center justify-center overflow-wrap font-bold py-[2px] px-2 w-[10rem] text-center text-white  uppercase transform translate-x-[25%] translate-y-[50%] rotate-45 origin-center ">
        <p className=" w-3/5 mx-auto text-center overflow-hidden text-ellipsis">
          {type}
        </p>
      </div>
      {/* <div className=" w-24 sm:w-28 rounded h-28 sm:h-36 flex bg-[#9C4DF4]/50 items-center justify-center text-lg  sm:text-2xl text-white  uppercase">{type}</div> */}

      <div className="flex flex-col items-stretch justify-between ">
        <div className=" w-full flex-col items-start space-y-1">
          <p className=" font-bold text-lg  text-start  pr-8">{title}</p>

          <p className=" uppercase font-medium text-sm"> {category} </p>
          <p className=" uppercase font-medium text-sm">
            modules : {course.module || 1}{" "}
          </p>
        </div>
        <div>
          <button
            onClick={handler}
            disabled={checkingCourse.id !== ""}
            type="button"
            className=" disabled:pointer-events-none capitalize whitespace-nowrap text-[#9C4DF4] flex !gap-x-1 items-center !p-1 transition-colors duration-300 ease-in-out border border-[#9C4DF4] hover:bg-[#9C4DF4] hover:text-white"
            // style={buttonStyle}
            onMouseOver={() => setIsHovered(true)}
            onMouseOut={() => setIsHovered(false)}
          >
            {checkingCourse.id === course.id &&
              checkingCourse.module === course.module && (
                <div className="absolute w-full h-full top-0 left-0 flex items-center justify-center bg-slate-200/50 backdrop-blur-sm">
                  <ClipLoader
                    color="#46555C"
                    loading={
                      checkingCourse.id === course.id &&
                      checkingCourse.module === course.module
                    }
                    cssOverride={override}
                    size={30}
                    aria-label="Loading Spinner"
                    data-testid="loader"
                  />
                </div>
              )}
            View {course.completed ? "completed" : ""} Resource&nbsp;
            {buttonText}
          </button>
        </div>
      </div>
    </div>
  );
}
