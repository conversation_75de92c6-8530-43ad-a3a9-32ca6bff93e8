const mongoose = require('mongoose');
const ArticleModel = require('./src/models/article.model');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.DB_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Function to update specific article dates
const updateSpecificArticleDates = async () => {
  try {
    console.log('Starting article date updates...\n');

    // 1. Update "Knowledge, Attitude and Practices" to 2015
    const knowledgeUpdate = await ArticleModel.collection.findOneAndUpdate(
      {
        title: { $regex: /KNOWLEDGE.*ATTITUDE.*PRACTICES/i }
      },
      {
        $set: {
          createdAt: new Date('2015-06-15T10:00:00.000Z'),
          updatedAt: new Date('2015-06-15T10:00:00.000Z')
        }
      },
      { returnDocument: 'after' }
    );

    if (knowledgeUpdate.value) {
      console.log(`✅ Updated "${knowledgeUpdate.value.title}" to 2015`);
      console.log(`   New date: ${knowledgeUpdate.value.createdAt}\n`);
    } else {
      console.log('❌ Knowledge, Attitude and Practices article not found\n');
    }

    // 2. Update "Portrayal of Male and Female Characters" to 2023
    const portrayalUpdate = await ArticleModel.collection.findOneAndUpdate(
      {
        title: { $regex: /Portrayal.*Male.*Female.*Characters.*Romance/i }
      },
      {
        $set: {
          createdAt: new Date('2023-08-20T14:30:00.000Z'),
          updatedAt: new Date('2023-08-20T14:30:00.000Z')
        }
      },
      { returnDocument: 'after' }
    );

    if (portrayalUpdate.value) {
      console.log(`✅ Updated "${portrayalUpdate.value.title}" to 2023`);
      console.log(`   New date: ${portrayalUpdate.value.createdAt}\n`);
    } else {
      console.log('❌ Portrayal of Male and Female Characters article not found\n');
    }

    // Verify all articles and their dates
    console.log('📋 Current article dates:');
    console.log('=' .repeat(80));
    
    const allArticles = await ArticleModel.find({}, { 
      title: 1, 
      author: 1, 
      createdAt: 1, 
      updatedAt: 1 
    }).sort({ createdAt: 1 });

    allArticles.forEach((article, index) => {
      const year = new Date(article.createdAt).getFullYear();
      const formattedDate = new Date(article.createdAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
      console.log(`${index + 1}. "${article.title}"`);
      console.log(`   Author: ${article.author}`);
      console.log(`   Date: ${formattedDate} (${year})`);
      console.log('   ' + '-'.repeat(60));
    });

    console.log('\n✅ Article date update process completed!');

  } catch (error) {
    console.error('❌ Error updating article dates:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
};

// Run the script
const main = async () => {
  await connectDB();
  await updateSpecificArticleDates();
};

main().catch(console.error);
