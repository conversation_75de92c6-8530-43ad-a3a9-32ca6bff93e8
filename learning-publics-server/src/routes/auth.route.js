const express = require('express'); 
const Controller = require('../controllers'); 
const { adminRequired, userRequired, notELearningRole } = require('../middlewares/auth.middleware');

const authRoute = express.Router();

authRoute.post('/register', Controller.AccountController.registerUser);
authRoute.post('/create', Controller.AccountController.registerAdmin);
authRoute.post('/login', Controller.AuthController.login);
authRoute.post('/default', Controller.AuthController.defaultAdminAccount);
authRoute.post('/logout', Controller.AuthController.logout);
authRoute.post('/refreshtoken', Controller.AuthController.handleRefreshToken);
authRoute.post('/logout/all', Controller.AuthController.logoutAll);
authRoute.patch('/reset-password', userRequired, Controller.AuthController.resetLogin);
authRoute.get('/users', adminRequired,notELearningRole, Controller.AccountController.getAccounts);
authRoute.get('/admins', adminRequired,notELearningRole, Controller.AccountController.getAdminAccounts);
authRoute.delete('/user', adminRequired,notELearningRole, Controller.AuthController.deleteAccount); 
authRoute.delete('/admin', adminRequired,notELearningRole, Controller.AuthController.deleteAdminAccount);
 
authRoute.post('/check', Controller.AuthController.checkUser);
authRoute.post('/send-otp', Controller.AccountController.temporalUserOpt);
authRoute.post('/register-verify', Controller.AccountController.createAccount);
authRoute.get('/admin/:', adminRequired,notELearningRole, Controller.AccountController.accountByRole).get("/editor",Controller.AccountController.editorAccount )
module.exports = {
  authRoute,
};
