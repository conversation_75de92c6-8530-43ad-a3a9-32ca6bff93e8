const { mongoose } = require('mongoose');
const Plan = require('../models/plan.model'); 
const CourseModel = require('../models/course.model');

const getCourse = async (course) =>{
    try{
        const courses = [];
        course.forEach(el=>{
            courses.push(mongoose.Types.ObjectId(el));
        });
        return await CourseModel.find({_id:[...courses]},"-__v -admin -createdAt -updatedAt"); 
    }catch(error){
        throw new Error(error)
    }
}
exports.create = async(details) => {
    try {
        const courses = await getCourse(details.courses);
        if(!courses || courses.length === 0) return {error: "Resources does not exist"}
        const info = [];
        courses.forEach((cur) => {
            const {_id,...data} = cur.toObject();
            data.id =_id;
            data.module = details?.module;
            data.completed= false;
            info.push(data);
        });
        const plan = await Plan.create({...details});
        plan.courses = info;
        plan.view = [{
            module: details?.module,
            count: info?.length || 1,
            completed: false,
            viewed: 0
        }]
        plan.save();
        return plan;
    } catch (error) {
        return { error }
    }
} 
exports.planById = async (id) => {
    try{
        return await Plan.findById({_id:id}).exec()
    }catch(error){
        return {error};
    }
}
exports.planByType = async (type) => {
    try{
        return await Plan.findOne({type}).exec()
    }catch(error){
        return {error};
    }
}
exports.plans = async () => {
    try{
        return await Plan.find({}).exec();
    }catch(error) {
        return {error};
    }
}
exports.delete = async (_id) => {
    try{
        return await Plan.findByIdAndDelete({_id}).exec();
    }catch(error) {
        return {error};
 
    }
}
exports.update = async(id, details) => {
    try {  
        const courses = await getCourse(details.courses);
        if(!courses || courses.length === 0) return {error: "Resources does not exist"}
        const info = [];
        courses.forEach((cur) => {
            const {_id,...data} = cur.toObject();
            data.id =_id
            data.module = parseInt(details.module);
            data.completed= false;
            info.push(data);
        });
        const update = await Plan.findById({_id:id}).exec(); 
        const findModule = update.courses.filter(x => parseInt(x.module) === parseInt(details.module));
        if(!findModule || findModule.length === 0){
        // add new module
        update.courses.push (...info);
     }else{
        const otherModules = update.courses.filter(x => parseInt(x.module) !== parseInt(details.module));
        update.courses = [...otherModules, ...info]
     }
     update.type = details?.type ? details.type : update.type;
     update.major = details?.major ? details.major : update.major;
     update.price = details?.price ? details.price : update.price;
     update.description = details?.description ? details.description : update.description;
    update.save();
    return update;
    } catch (error) {
        return { error }
    }
}
exports.removeCourse = async(id, courses) => {
    try {    
        return await Plan.findOneAndUpdate({_id:id}, {
            $set:{
                "courses":[...courses],
            }
        }, { returnOriginal: false });
        
    } catch (error) {
        return { error }
    }
}
exports.addCourse = async(id, courses) => {
    try {  
        const find = await getCourse(courses);
        if(!find || find.length === 0) return {error: "Course does not exist"} 
        const info = [];
        courses.forEach((cur) => {
            const {_id,...data} = cur.toObject();
            data.id =_id
            data.module = details?.module;
            data.completed= false;
            info.push(data);
        }); 
        // insert the course object directly
        const exist = await Plan.findById({_id:id});
        const findModule = exist.courses.find(x => parseInt(x.module) === parseInt(courses.module));
        if(!findModule) return {error: `Module ${courses.module} does not exist`};
        const otherModules = exist.courses.filter(x => parseIt(x.module) !== parseInt(courses.module));
        otherModules.push(info)
        exist.courses = [...otherModules];
        exist.save();
        return exist;
    } catch (error) {
        return { error }
    }
}
exports.updateView = async(id, details, courses) => {
    try {  
         console.log(details, courses)
        const update = await Plan.findById({_id:id}).exec(); 
    return update;
    } catch (error) {
        return { error }
    }
}
