const express = require("express");
const sitemapRoute = express.Router();
const Controller = require("../controllers");

// Main sitemap index
sitemapRoute.get("/sitemap.xml", Controller.SitemapController.getSitemapIndex);

// Individual sitemaps
sitemapRoute.get("/sitemap-articles.xml", Controller.SitemapController.getArticlesSitemap);
sitemapRoute.get("/sitemap-categories.xml", Controller.SitemapController.getCategoriesSitemap);
sitemapRoute.get("/sitemap-static.xml", Controller.SitemapController.getStaticPagesSitemap);

// Robots.txt
sitemapRoute.get("/robots.txt", Controller.SitemapController.getRobotsTxt);

// RSS Feeds
sitemapRoute.get("/rss.xml", Controller.SitemapController.getRSSFeed);
sitemapRoute.get("/feed.xml", Controller.SitemapController.getRSSFeed);
sitemapRoute.get("/atom.xml", Controller.SitemapController.getAtomFeed);

module.exports = {
  sitemapRoute,
};
