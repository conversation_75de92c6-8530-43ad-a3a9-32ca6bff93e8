import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { MainContainer } from 'globalStyles';
import SEOHead from 'components/SEO/SEOHead';
import { ARTICLE_ENDPOINT } from 'api/ACTION';
import jmsApp from 'api/jms';
import { toast } from 'react-hot-toast';

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 0;
`;

const ContentWrapper = styled(MainContainer)`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 1rem;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
`;

const ArticlesGrid = styled.div`
  display: grid;
  gap: 2rem;
`;

const ArticleCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-4px);
  }
`;

const ArticleTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 1rem;

  a {
    color: inherit;
    text-decoration: none;

    &:hover {
      color: #3b82f6;
    }
  }
`;

const ArticleMeta = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #64748b;
`;

const ArticleDescription = styled.p`
  color: #374151;
  line-height: 1.6;
  margin-bottom: 1rem;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2rem;
  color: #64748b;
`;

const Pagination = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 3rem;
`;

const PageButton = styled.button`
  padding: 0.5rem 1rem;
  border: 2px solid #e5e7eb;
  background: ${props => props.active ? '#1e40af' : 'white'};
  color: ${props => props.active ? 'white' : '#374151'};
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1e40af;
    background: ${props => props.active ? '#1e40af' : '#f3f4f6'};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const JournalsByCategoryPage = () => {
  const { categorySlug } = useParams();
  const [articles, setArticles] = useState([]);
  const [category, setCategory] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const articlesPerPage = 10;

  useEffect(() => {
    fetchCategoryArticles();
  }, [categorySlug, currentPage]);

  const fetchCategoryArticles = async () => {
    try {
      setLoading(true);

      // Use the slug-based endpoint for journal categories
      const response = await jmsApp.get(`/author/articles/category/${categorySlug}`, {
        params: {
          page: currentPage,
          limit: articlesPerPage
        }
      });

      if (response.data) {
        // Handle different response structures
        const responseData = response.data.data || response.data;

        if (responseData && responseData.articles) {
          setArticles(responseData.articles);
          setCategory(responseData.category);

          // Handle pagination
          if (responseData.pagination) {
            setTotalPages(responseData.pagination.pages);
          } else {
            setTotalPages(Math.ceil((responseData.total || responseData.articles.length) / articlesPerPage));
          }
        } else if (Array.isArray(responseData)) {
          // If response is directly an array of articles
          setArticles(responseData);
          setTotalPages(Math.ceil(responseData.length / articlesPerPage));
        } else {
          setArticles([]);
          setCategory({ name: categorySlug, slug: categorySlug });
        }
      } else {
        setArticles([]);
        setCategory({ name: categorySlug, slug: categorySlug });
      }
    } catch (error) {
      console.error('Error fetching category articles:', error);
      toast.error(`Failed to load articles: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <PageContainer>
        <ContentWrapper>
          <LoadingSpinner>Loading articles...</LoadingSpinner>
        </ContentWrapper>
      </PageContainer>
    );
  }

  return (
    <>
      <SEOHead
        title={`${category?.name || categorySlug} Articles - Learning Publics Journal`}
        description={`Browse research articles in ${category?.name || categorySlug}. High-quality peer-reviewed academic papers in agriculture and environmental studies.`}
        keywords={`${category?.name || categorySlug}, research articles, academic papers, agriculture, environmental studies`}
        canonicalUrl={`/journals/category/${categorySlug}`}
      />

      <PageContainer>
        <ContentWrapper>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Header>
              <Title>{category?.name || categorySlug} Articles</Title>
              <Subtitle>
                {category?.description || `Explore research articles in ${category?.name || categorySlug}`}
              </Subtitle>
            </Header>

            <ArticlesGrid>
              {articles.length > 0 ? (
                articles.map((article) => (
                  <ArticleCard key={article._id}>
                    <ArticleTitle>
                      <Link to={`/articles/${article.slug}`}>
                        {article.title}
                      </Link>
                    </ArticleTitle>
                    <ArticleMeta>
                      <span>By {article.author}</span>
                      <span>•</span>
                      <span>{formatDate(article.createdAt)}</span>
                      {article.viewCount && (
                        <>
                          <span>•</span>
                          <span>{article.viewCount} views</span>
                        </>
                      )}
                    </ArticleMeta>
                    <ArticleDescription>
                      {article.description || 'No description available.'}
                    </ArticleDescription>
                    <Link
                      to={`/articles/${article.slug}`}
                      style={{
                        color: '#1e40af',
                        textDecoration: 'none',
                        fontWeight: '600'
                      }}
                    >
                      Read Full Article →
                    </Link>
                  </ArticleCard>
                ))
              ) : (
                <div style={{ textAlign: 'center', padding: '3rem', color: '#64748b' }}>
                  <h3>No articles found in this category</h3>
                  <p>Check back later for new publications.</p>
                </div>
              )}
            </ArticlesGrid>

            {totalPages > 1 && (
              <Pagination>
                <PageButton
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </PageButton>

                {[...Array(totalPages)].map((_, index) => (
                  <PageButton
                    key={index + 1}
                    active={currentPage === index + 1}
                    onClick={() => setCurrentPage(index + 1)}
                  >
                    {index + 1}
                  </PageButton>
                ))}

                <PageButton
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </PageButton>
              </Pagination>
            )}
          </motion.div>
        </ContentWrapper>
      </PageContainer>
    </>
  );
};

export default JournalsByCategoryPage;
