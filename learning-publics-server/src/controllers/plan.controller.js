const { APIError } = require("../utils/apiError");
const { CONSTANTS } = require("../config"); 
const {  getPlans, deletePlan, getPlanById, getUserPlanById, generateTempRef, getUserPlans, getTempReference, createUserPlan, getUserById, getUserPlanByReference, updatePlan, removeCourse, addCourse } = require("../services");
const { createPlan } = require('../services')
const responsBuilder = require("../utils/responsBuilder");
const { ERROR_FIELD, META } = require("../utils/actions");
const logger = require("../logger");
const { options } = require("../utils/paystack.auth");
const { PAYSTACK_SECRETE_KEY, PAYSTACK_CALL_BACK_URL } = require("../config/env");
const { paymentSuccessMailHandler } = require("../utils/mailer");
const https = require('https'); 
const { default: mongoose } = require("mongoose");
const { requiredAdminRole } = require("../middlewares/auth.middleware");


exports.createPlan = async (req, res, next) => {
    try {
      if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !== CONSTANTS.ACCOUNT_TYPE[6])  return next(APIError.unauthorized());
        const { courses, major, type, price, modules, description } = req.body;
        // ensure every field is present
        if (!courses || courses.length === 0) return next(APIError.badRequest('Course ID required'));
        if (!price) return next(APIError.badRequest('Plan price is required'));
        if (!major) return next(APIError.badRequest('Major is required'));
        if (!type) return next(APIError.badRequest('Type is required'));
        if (!description) return next(APIError.badRequest('Description is required'));
        if (!modules) return next(APIError.badRequest('Module is required'));
        const descLimit = Array.from(description.trim().split(" "));
      if(descLimit.length > 200) return next(APIError.badRequest('Description exceed word limit'));
        // const planDuration = duration.slice(-3);
      // if (!CONSTANTS.PLAN_TYPE.includes(type.toLowerCase())) return next(APIError.badRequest('Invalid Plan Type'));
      //   if (!CONSTANTS.PLAN_DURATION.includes(planDuration.toUpperCase())) return next(APIError.badRequest('Invalid Plan Duration'));
      //   if(!CONSTANTS.COURSE_TYPE.includes(major.toLowerCase())) return next(APIError.badRequest('Invalid Plan major')) ; 
        // // create plan
        const details  = { courses, major, type, price, module:modules , description};
        // details.module = modules
        //check if the plan is a free plan and already exist
        // if(type.toLowerCase() === CONSTANTS.PLAN_TYPE[0].toLowerCase()){
        //   //check if the plan type exist
        //   const freeExist = await getPlanByType(type.toLowerCase());
        //   if(freeExist) return next(APIError.badRequest("Course already exist"));
        // }
        const plan = await createPlan(details);
        if (!plan) return next(APIError.badRequest(ERROR_FIELD.PLAN_FAIL,400));
        if (plan.error) return next(APIError.badRequest(plan.error, 400));
        logger.info("Course created successfully", {meta:META.PLAN_SERVICE});
        res.status(201).json({
            success: true,
            msg: 'Course created successfully'
        });
    } catch (error) {
        next(error);
    }
} 
exports.viewPlans = async (req, res, next) => {
    try {
        const plans = await getPlans();
        if (!plans || plans.length === 0) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
        if (plans.error) return next(APIError.customError(plans.error, 400));
        const data = plans.map((cur)=> {
            return responsBuilder.buildPlanView(cur.toObject());
        })
        data.forEach(async(element)=> {
            for(let x=0; x < element.courses.length; x++){
                const build = responsBuilder.buildCourse(element.courses[x]);
                const {createdAt, updatedAt, resourceUrl, resourceId, type, ...more} = build;
                element.courses[x] = more; 
            }
            element.count=element.courses.length;
        }) 
        const response = responsBuilder.commonReponse("Found", data, "plans");
        res.status(200).json(response);
    } catch (error) {
        next(error);
    }
}
exports.viewPlansById = async (req, res, next) => {
    try {
        const {planId } =req.query;
        if(!planId) return next(APIError.badRequest("Course ID is required"));
        const plans = await getPlanById(planId);
        if (!plans || plans.length === 0) return next(APIError.notFound(ERROR_FIELD.NOT_FOUND));
        if (plans.error) return next(APIError.customError(plans.error, 400));
        const data =   responsBuilder.buildPlanView(plans.toObject());
        const courseArr = [];
        data.courses.forEach(async(element)=> { 
                const build = responsBuilder.buildCourse(element); 
                courseArr.push(build); 
            element.count ++;
        }) 
        data.courses = courseArr;
        const response = responsBuilder.commonReponse("Found", data, "plans");
        res.status(200).json(response);
    } catch (error) {
        next(error);
    }
}
exports.deletePlanById = async (req, res, next) => {
    try {
      if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !== CONSTANTS.ACCOUNT_TYPE[6])  return next(APIError.unauthorized());
        const { planId } = req.query;
        if (!planId) return next(APIError.badRequest("Plan ID is required"));
        const deletedPlan = await deletePlan(planId);
        if (!deletedPlan) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
        if (deletedPlan. error) return next(APIError.customError(deletedPlan.error, 400));
        res.status(200).json({success: true, msg: "Plan was deleted successfully"});
    } catch (error) {
        next(error);
    }
}
 

exports.planByID = async(req, res, next) => {
    try { 
      if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !== CONSTANTS.ACCOUNT_TYPE[6])  return next(APIError.unauthorized());
        const { planId } = req.query
        const plan = await getPlanById(planId)
        if(!plan || plan.length === 0) return next(APIError.customError(`Plan with id ${planId} does not exist `))
        if(plan.error) return next(APIError.badRequest(plan.error));
        // build response data structure
        const data = plan.map((cur) => {
            return responsBuilder.buildPlan(cur.toObject())
        })
        const response = responsBuilder.commonReponse("Found", data, "plans");
        res.status(200).json(response);
    } catch (error) {
        return { error }
    }
}
exports.updatePlan = async(req, res, next) => {
    try { 
      if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !== CONSTANTS.ACCOUNT_TYPE[6])  return next(APIError.unauthorized());
        const { planId } = req.query;
        const plan = await getPlanById(planId);
        if(!plan || plan.length === 0) return next(APIError.customError(`Plan with id ${planId} does not exist `, 404));
        if(plan.error) return next(APIError.badRequest(plan.error));
        const details = {};

        for(const key in req.body){
          if(key === "modules") details.module = parseInt(req.body[key])
         else details[key] = req.body[key];
        } 
        if(details.description){
          const descLimit = Array.from(details.description.trim().split(" "));
        if(descLimit.length > 200) return next(APIError.badRequest('Description exceed word limit'));
        } 
        // if(details.type){
        //   if (!CONSTANTS.PLAN_TYPE.includes(details.type.toLowerCase())) return next(APIError.badRequest('Invalid Plan type'));
        // }
        // if(details.duration){ 
        //   const planDuration = details.duration.slice(-3);
        //   if (!CONSTANTS.PLAN_DURATION.includes(planDuration.toUpperCase())) return next(APIError.badRequest('Invalid Plan duration'));
        // }
        // if(details.major){
        //   if(!CONSTANTS.COURSE_TYPE.includes(details.major.toLowerCase())) return next(APIError.badRequest('Invalid Plan major')) ;
        // }
        const updatedPlan = await updatePlan(planId, details)
        if (!updatedPlan) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
        if (updatedPlan.error) return next(APIError.badRequest(updatedPlan.error));
        logger.info("Plan updated successfully", { meta:META.PLAN_SERVICE})
        res.status(200).json({success: true, msg:"Course updated successfully"});
     
    } catch (error) {
        next(error)
    }
}
exports.removePlanCourse = async(req, res, next) => {
    try { 
      if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !== CONSTANTS.ACCOUNT_TYPE[6])  return next(APIError.unauthorized());
        const { planId, modules } = req.query
        const plan = await getPlanById(planId)
        if(!plan || plan.length === 0) return next(APIError.customError(`Plan with id ${planId} does not exist `))
        if(plan.error) return next(APIError.badRequest(plan.error)); 
      const findModule = plan.courses.filter(x => parseInt(x.module) === parseInt(modules));
      if(!findModule || findModule.length === 0) return next(APIError.customError(`Module does not exist `))
      const otherModules = plan.courses.filter(x => parseInt(x.module) !== parseInt(modules));
        const updatedPlan = await removeCourse(planId, otherModules)
        if (!updatedPlan) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
        if (updatedPlan.error) return next(APIError.badRequest(updatedPlan.error));
        logger.info("Course removed successfully", { meta:META.PLAN_SERVICE})
        res.status(200).json({success: true, msg:"Course module removed updated successfully"});
     
    } catch (error) {
        next(error)
    }
}
exports.addPlanCourse = async(req, res, next) => {
    try { 
      if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !== CONSTANTS.ACCOUNT_TYPE[6])  return next(APIError.unauthorized());
        const { planId } = req.query
        const {courses } = req.body
        const plan = await getPlanById(planId)
        if(!plan || plan.length === 0) return next(APIError.customError(`Plan with id ${planId} does not exist `))
        if(plan.error) return next(APIError.badRequest(plan.error));
      
        if(!courses || courses.length === 0) return next(APIError.customError(`Course ID is required`))
        
        const updatedPlan = await addCourse(planId, courses)
        if (!updatedPlan) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
        if (updatedPlan.error) return next(APIError.badRequest(updatedPlan.error));
        logger.info("Plan course added successfully", { meta:META.PLAN_SERVICE})
        res.status(200).json({success: true, msg:"Plan Course added successfully"});
     
    } catch (error) {
        next(error)
    }
}
exports.userPlans = async(req, res, next) => {
    try { 
      if(req.userType != CONSTANTS.ACCOUNT_TYPE[0]) return next(APIError.unauthorized());
        const plan = await getUserPlans(req.userId)
        if(!plan || plan.length === 0) return next(APIError.customError(`Course Not Found`));
        if(plan.error) return next(APIError.customError(plan.error,400));
        const courses = plan[0].planInfo[0];
        // build response data structure
        const data = plan.map((cur) => {
            return responsBuilder.buildUserPlan(cur.toObject())
        })
        const response = responsBuilder.commonReponse("Found", data, "plans");
        res.status(200).json(response);
    } catch (error) {
        return { error }
    }
}

exports.initiateTransaction = async (req, res, next) => {
    try{ 
        const { planId } = req.body;
        if (!planId) return next(APIError.badRequest("Plan ID is required"));
        const plan = await getPlanById(planId);
        if(!plan) return next(APIError.customError("Plan NOT found", 404));
        if(plan.error) return next(APIError.customError(plan.error, 400));
         // get user plan
        let userPlan = await getUserPlanById(planId,req.userId);
        if(userPlan)return next(APIError.badRequest("You already have this course"));
        if(userPlan?.error) return next(APIError.customError(userPlan.error, 400));
        // update if plan have expired;
        // if(userPlan){ 
        //   const date = new Date();
        //   const currentDate = (date.getMonth()+1)  + '/' + date.getDate() + '/' + date.getFullYear();
        //   const expiryDate = (userPlan.expiryDate.getMonth()) + '/' + userPlan.expiryDate.getDate() + '/' + userPlan.expiryDate.getFullYear();
        //     if( new Date(currentDate) <= new Date(expiryDate)) return next(APIError.badRequest("Selected Plan is currently active"));
        // }
        
        const https = require("https");
        const params = JSON.stringify({
          "email": req.email,
          "amount": plan.price*100,
          "callback_url": PAYSTACK_CALL_BACK_URL,
        }) 
        const reqpay = https.request(options, reqpay => {
          let data = ''
          reqpay.on('data', (chunk) => {
            data += chunk
          });
          reqpay.on('end', () => {
            // write info to database
            data = JSON.parse(data);
            const details ={
                reference:data.data.reference,
                plan:planId,
                account: req.userId,
            }
            generateTempRef(details).then((check) =>{
              logger.info("Payment authorized successfully", {meta:META.PAYSTACK_SERVICE});
              res.send(data);
            }).catch((err) =>{
              return res.status(400).json({error:"Authorization failed, try again"})
            })
          })
        }).on('error', error => {
          next(error);
        })
        
        reqpay.write(params)
        reqpay.end()
    }catch(error){
        next(error);
    }
}

exports.paymentCompleted = async (req, res, next) => {
    try{
      const crypto = require('crypto');
        
        //validate event
        const hash = crypto.createHmac('sha512', PAYSTACK_SECRETE_KEY).update(JSON.stringify(req.body)).digest('hex'); 
      // verify request origin
      if (hash == req.headers['x-paystack-signature']) {
        // Retrieve the request's body
        logger.info("Payment authorization confirmed", {meta:META.PAYSTACK_SERVICE});
        const event = req.body.data;
        // check response status
        if(event.status === "success"){
          // send info to database
          let temPlan = await getTempReference(event.reference);
          if(!temPlan || temPlan.error){
            APIError.customError("Temporal reference failed",400);
            logger.info("Temporal reference retrieval failed", {meta:META.PAYSTACK_PLAN_SERVICE});
          }else{
            logger.info("Temporal reference id retrieved", {meta:META.PAYSTACK_PLAN_SERVICE});
            let plan = await  getPlanById(temPlan.plan);
            if(!plan || plan.error) {
              logger.error("Paid plan update failed", {meta:META.PAYSTACK_PLAN_SERVICE});
            }
            // update user plan info
            // let expiryDate = new Date(); 
            // const left = plan.duration.slice(-3).toUpperCase(); 
            // const digit = plan.duration.slice(0, -(left.length));
            // switch(left){
            //   case "MTH":
            //     expiryDate = expiryDate.setMonth(expiryDate.getMonth()+ digit);
            //     break;
            //   case "WKS":
            //     expiryDate = expiryDate.setDate(expiryDate.getDate() + digit);
            //     break;
            // }
            const planInfo = { 
              account: temPlan.account,
              plan: plan._id,
              reference: temPlan.reference,  
            } 
            const info = {
              id:plan._id,
              major:plan.major,
              type:plan.type,
              price: plan.price,
              courses: plan.courses,
              description: plan.description,
              view: plan.view
            }
            planInfo.info = [info];
            const user = await getUserById(temPlan.account);
            const finalize = await createUserPlan(planInfo);
            if(!finalize){ 
              logger.info("Course  purchase failed: returned undefined", {meta:META.PAYSTACK_PLAN_SERVICE});
            }else if(finalize.error){
              APIError.customError(`${finalize.error}: Course purchase failed`,400);
            } else{
              logger.info("Course purchase completed successfully", {meta: META.PAYSTACK_SERVICE});
              // send email to customer 
              const emailer = await paymentSuccessMailHandler(event.customer.email,plan.type,user.name);
              if (emailer.error)
                  APIError.customError("Course payment success mail fail to send", 400)
                  else logger.info("Course payment success mail sent successfully", {meta:META.MAIL_SERVICE});
                }
          }
        }
      }else{
          logger.info("Payment hack detected", {meta:META.PAYSTACK_SERVICE});
        }
    }catch(error){
      next(error);
    }
  }

  exports.verifyTransaction = async (req, res, next) => {
    try{
       
      const {reference} = req.query;
      if(!reference) return next(APIError.badRequest("Transaction reference is required"));
      const transaction = await getUserPlanByReference(req.userId,reference);
      if(!transaction) {
       return res.status(200).json({status: false, msg:"Transaction in process, check again"});
      }else if(transaction.error) {
        return next(APIError.customError(transaction.error,400))
      }else res.status(200).json({status: true, msg: "Transaction completed successfully"});
    }catch(error){
      return next(error);
    }
  } 

exports.viewCourse = async (req, res, next) => {
  try{
    if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[0])?.error)  return next(APIError.unauthorized());
    const plan = await getUserPlans(req.userId)
    if(!plan || plan.length === 0) return next(APIError.customError(`Plan Not Found`));
    if(plan.error) return next(APIError.customError(plan.error,400));
    const currentModules = [];
    plan.forEach((cur) =>{
      cur.planInfo[0].courses.sort((a, b) => {
       return parseInt(a.module) - parseInt(b.module);
      });
      cur.planInfo[0].view.sort((a, b) => {
       return parseInt(a.module) - parseInt(b.module);
      });
    })
    const views = plan[0].planInfo[0].view[0];
    plan.forEach((cur) => {
      cur.planInfo[0].courses.forEach((el) => {
        if(views.viewed !== views.count && !views.completed) {
        return  currentModules.push(el);
        }else{
          currentModules.push(el);
        }
      });
      cur.courses = currentModules;
    })

    const response = responsBuilder.commonReponse("Found", currentModules, "course");
    res.status(200).json(response);
  }catch(error) {
    next(error);
  }
}