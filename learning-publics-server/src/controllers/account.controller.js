 
const { hashSync } = require('bcryptjs');
const {
  usernameExist,
  emailExist,
  createAdmin,
  getAllAccounts,
  userExistByIdAndEmail,
  passwordRecovery,
  getPasswordRecoveryInfor,
  removePasswordRecoveryInfor,
  updateUserPass,
  updateUserInfor,
  deleteUser,
  registerTemUser,
  updateUserTempToken,
  deleteTempUser,
  registerUser,
  getPlans,
  getAdminsByRole,
  getUserById,
  getAdmins,
} = require('../services');
const { ACTIONS, ERROR_FIELD, META } = require('../utils/actions');
const { APIError } = require('../utils/apiError');
const { isValidEmail, OPTDigitGen } = require('../utils/validation');
const buildResponse = require('../utils/responsBuilder');
const { checkUsername, checkEmail } = require('../services/account.services');
const { v4: uuidv4 } = require('uuid');
const {
  recoveryPasswordMailHandler,
  registrationMailHandler,
  registrationOTPMailHandler,
  invitationMailHandler,
} = require('../utils/mailer');
const logger = require('../logger');
const jwt = require('jsonwebtoken');
const config = require('../config/env');
const TemporalAccountModel = require('../models/temporal.model');
const InvitationModel = require('../models/invation.model');
const { CONSTANTS } = require('../config');
const { requiredAdminRole } = require('../middlewares/auth.middleware');
 
exports.registerUser = async (req, res, next) => {
  try {
    const { name, username, password, email } = req.body;
    if (!name) return next(APIError.badRequest('Name is required'));
    if (!username) return next(APIError.badRequest('Username is required'));
    if (!password) return next(APIError.badRequest('Password is required'));
    if (!email) return next(APIError.badRequest('Email is required'));
    if (password.length < 8) return next(APIError.badRequest("Password must be at least 8 characters"));
    //check if detail exist
    let exist = await usernameExist(username);
    if (exist) {
      if (!exist.otp) {
        return next(APIError.customError(`${username} is not available`, 400));
      }
    }
    if (!isValidEmail(email))
      return next(APIError.badRequest(ERROR_FIELD.INVALID_EMAIL));
    if (!exist) exist = await emailExist(email);
    if (exist) {
      if (!exist.otp) {
        return next(APIError.customError(`${email} already exist`, 400));
      }
      
    }
    const details = { username, email, name };
    const hashedPassword = hashSync(password, 12);
    details.password = hashedPassword;
    if (req.body.phone) details.phone = req.body.phone;
    details.type = CONSTANTS.ACCOUNT_TYPE[0];
    let register = exist;
    if (!exist) {
      register = await registerTemUser(details);
      if (register.error)
        return next(APIError.customError(register.error, 400));
      if (!register) return next(APIError.customError());
      logger.info('Temporal account created successfully', {
        meta: META.ACCOUNT_SERVICE ,
      });
    }

    const payload = { id: register._id, type: register.type, email };
    const refreshToken = jwt.sign(payload, config.TOKEN_SECRETE, {
      expiresIn: '7m',
    });
    const data = buildResponse.buildUser(register.toObject());
    data.token = refreshToken;
    const otp = OPTDigitGen();
    const expiryMin = 7;
    data.expiryMin = expiryMin;
    const info = { refreshToken, otp, id: register._id };
    const updateToken = await updateUserTempToken(info);
    if (updateToken.error)
      return next(APIError.customError(updateToken.error, 400));
    if (!updateToken) return next(APIError.customError());
    logger.info('Temporal account updated successfully', {
      meta: META.ACCOUNT_SERVICE,
    });
    const result = await registrationOTPMailHandler(email, otp, `'${expiryMin} minutes'`);
    if (result.error) {
      return next(APIError.customError(ERROR_FIELD.FAILED_OTP));
    }
    logger.info('OTP sent successfully', { meta: META.ACCOUNT_SERVICE });
    res.clearCookie('jwt', {
      httpOnly: false,
      secure: true,
      sameSite: 'none',
    });
    res.cookie('jwt', refreshToken, {
      httpOnly: false,
      secure: true,
      sameSite: 'none',
      // maxAge:7*60*60*1000
    });
    data.refreshToken = [];
    if (register.register) {
      data.otp=null;
      return res
        .status(200)
        .json({ success: false, msg: 'Incomplete registration found', data });
    }
    const response = buildResponse.commonReponse(
      'Proceed to next stage',
      data,
      'tempAccount'
    );
    res.status(201).json(response);
  } catch (error) {
    next(error);
  }
};
exports.createAccount = async (req, res, next) => {
  try {
    let token = req.cookie?.jwt;
    if (!token) token = req.headers?.authorization?.split(' ')[1];
    if (!token) token = req.headers?.cookie?.split('=')[1];
    if (!token)
      return next(APIError.customError(ERROR_FIELD.TOKEN_NOT_FOUND, 403));
    let otp = req.body.otp.toString();
    if (!otp) return next(APIError.badRequest('OTP is required'));
    const verify = jwt.verify(token, config.TOKEN_SECRETE);
    let payload = { id: verify.id, type: verify.type };
    const exist = await TemporalAccountModel.findOne({
      refreshToken: token,
    }).exec();
    if (!exist) return next(APIError.customError(ERROR_FIELD.INVALID_OTP, 403));
    if (exist.otp !== otp)
      return next(APIError.customError(ERROR_FIELD.INVALID_OTP, 403));
    const data = buildResponse.buildTemporalUser(exist.toObject());
    data.status = CONSTANTS.ACCOUNT_STATUS[1];
    // const plans = await getPlans();
    // if (!plans) return next(APIError.customError("Student registration is pending at the moment",404))
    // if(plans.error) return next(APIError.customError(plans.error, 400))
    // let defaultPlan = plans.filter((x) => x.type.toLowerCase() === CONSTANTS.PLAN_TYPE[0].toLowerCase()); 
    // if(!defaultPlan || defaultPlan.length === 0) return next(APIError.customError("Default Plan is unavailable", 404));
    // defaultPlan = defaultPlan[0];
    // const ref = new Date();
    // const reference = hashSync(ref.toString(),10);
    // data.plan ={
    //   plan:defaultPlan._id,reference,
    // }
    // expiryDate = new Date(); 
    // expiryDate.setMonth(expiryDate.getMonth() + 1);
    // data.plan.expiryDate = expiryDate
    data.otp=null;
    const account = await registerUser(data);
    if (!account)
      return next(APIError.customError(ERROR_FIELD.REG_FAILED, 400));
    if (account.error) return next(APIError.customError(account.error));
    logger.info('Registration completed successfully', {
      meta: META.ACCOUNT_SERVICE,
    });
    await deleteTempUser(payload.id);
    logger.info('Temporal account deleted successfully', {
      meta: META.ACCOUNT_SERVICE,
    });
    //send registration TO MAIL
    const result = await registrationMailHandler(
      account.email,
      account.username
    );
    if (result.error) {
      return next(APIError.customError(ERROR_FIELD.REG_MAIL, 400));
    }
    logger.info('Registration mail sent successfully', {
      meta: META.MAIL_SERVICE,
    });
    res.clearCookie('jwt');
    res
      .status(201)
      .json({ success: true, msg: 'Registration Completed Successfully' });
  } catch (error) {
    if (error.message === ERROR_FIELD.JWT_EXPIRED) {
      logger.info('Expired token detected', { meta: META.ACCOUNT_SERVICE, });
      next(APIError.customError(ERROR_FIELD.EXPIRED_TOKEN, 403));
    }
    next(error);
  }
};

exports.temporalUserOpt = async (req, res, next) => {
  try {
    let refreshToken = req.cookie?.jwt;
    if (!refreshToken) refreshToken = req.headers?.authorization?.split(' ')[1];
    if (!refreshToken) refreshToken = req.headers?.cookie?.split('=')[1];
    if (!refreshToken)
      return next(APIError.customError(ERROR_FIELD.TOKEN_NOT_FOUND, 403));
    const email = req.body.email;
    if (!isValidEmail(email))
      return next(APIError.badRequest(ERROR_FIELD.INVALID_EMAIL));
    const exist = await TemporalAccountModel.findOne({ refreshToken });
    if (!exist) {
      jwt.verify(refreshToken, config.TOKEN_SECRETE, async (err, decode) => {
        if (err) {
          const payload = jwt.decode(refreshToken, config.TOKEN_SECRETE);
          const failedUser = await TemporalAccountModel.findOne({
           _id: payload?.id
          }); 
          failedUser.refreshToken = [];
          failedUser.save();
          logger.info('OTP reuse detected', { meta: META.MAIL_SERVICE, });
          return next(APIError.customError(ERROR_FIELD.INVALID_TOKEN, 403));
        }
        logger.info('OTP reuse detected', { meta: META.MAIL_SERVICE,});
        return next(APIError.customError(ERROR_FIELD.TOKEN_NOT_FOUND, 403));
      });
      return next(APIError.customError(ERROR_FIELD.INVALID_TOKEN, 403));
    } else {
      //if tooken is invalid send return to registration page
      if (exist) {
        jwt.verify(refreshToken, config.TOKEN_SECRETE, (err, _decoded) => {
          if (err) {
            if (err.name !== 'TokenExpiredError') {
              exist.refreshToken = [];
              exist.save();
              logger.info('Hacked token detected', { meta: META.ACCOUNT_SERVICE, });
              return next(APIError.customError("OTP Expired", 403));
            }
          }
        });
        if (exist.email !== email) {
          logger.info('Registration email mismatch detected', {
            meta: META.MAIL_SERVICE,
          });
          return next(APIError.customError("Email mismatch", 403));
        }
      }
      const payload = { id: exist._id, type: exist.type };
      const OTPToken = jwt.sign(payload, config.TOKEN_SECRETE, {
        expiresIn: '7m',
      });
      const otp = OPTDigitGen();
      exist.refreshToken = [OTPToken];
      exist.otp = otp;
      exist.save();
      const expiryMin=7;
      //send OTP TO MAIL
      const result = await registrationOTPMailHandler(
        exist.email,
        otp,
        `${expiryMin} minutes`
      );
      if (result.error) {
        return next(APIError.customError(ERROR_FIELD.FAILED_OTP));
      }
      logger.info('OTP sent successfully', { meta: META.MAIL_SERVICE, });
      res.cookie('jwt', OTPToken, {
        httpOnly: false,
        secure: true,
        sameSite: 'none',
        maxAge: 7 * 60 * 60 * 1000,
      });
      res
        .status(200)
        .json({ success: true, msg: 'OTP Sent Successfully', token: OTPToken ,expiryMin});
    }
  } catch (error) {
    next(error);
  }
};
exports.verifyAdminRegistration = async (req, res, next) => {
  try {
    const { id } = req.body;
    if (!id) return next(APIError.customError(ERROR_FIELD.INVALID_LINK, 400));
    //verify id
    const foundInvite = await InvitationModel.findOne({ token: id }).exec();
    if (!foundInvite || !foundInvite.admin) {
      logger.info('Invalid Invite detected', { meta: 'Invitation-service' });
      return next(APIError.customError(ERROR_FIELD.INVALID_INVITE, 404));
    }
    //invite last for 7days
    const currentDate = new Date();
    const validDate = foundInvite.createdAt.getDate() - currentDate.getDate();
    if (validDate > 7) {
      foundInvite.token = '';
      foundInvite.save();
      logger.info('Expired Invite detected', { meta: 'Invitation-service' });
      return next(APIError.customError(ERROR_FIELD.INVITE_EXPIRED, 400));
    }
    logger.info('Invitation validated successfully', {
      metat: 'Invitation-service',
    });
    res.status(200).json({ success: true, msg: 'Invitation is authenticated' });
  } catch (error) {
    next(error);
  }
};
exports.sendInvitation = async (req, res, next) => {
  try {
   if( requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error)return next(APIError.unauthorized())
    const {email, role} = req.body;
    if (!email) return next(APIError.badRequest('Invitee email is required'));
    if (!role) return next(APIError.badRequest('Invitee role is required'));
    if (!CONSTANTS.ACCOUNT_TYPE.includes(role.toLowerCase())) return next(APIError.badRequest('Invitee role is invalid'));
    if (!isValidEmail(email))
      return next(APIError.badRequest('Email is invalid'));
    //you can't send invite to already existing account.
    const existingUsering = await checkEmail(email);
    if (existingUsering) {
      logger.info('Invite to existing user detecting', {
        meta: 'invitation-service',
      });
      return next(APIError.customError("Can't invite an existing user", 400));
    }
    const uniqueString = uuidv4();
    await InvitationModel.create({
      admin: req.userId,
      token: uniqueString,
      email,
      role,
    });
    //send invite mail
    logger.info('Invitation created successfully', {
      meta: 'invitation-service',
    });
    const result = await invitationMailHandler(email, uniqueString, role, '7 days');
    if (result.error)
      return next(APIError.customError('Recovery mail failed to send', 400));
    logger.info('Invitation mail sent successfully', {
      meta: 'invitation-service',
    });
    res.status(200).json({ sucess: true, msg: 'Invitation sent successfully' });
  } catch (error) {
    next(error);
  }
};
exports.registerAdmin = async (req, res, next) => {
  try {
    const { name, username, password, email } = req.body;
    const { id } = req.query;
    if (!name) return next(APIError.badRequest('Name is required'));
    if (!username) return next(APIError.badRequest('Username is required'));
    if (!password) return next(APIError.badRequest('Password is required'));
    if (!email) return next(APIError.badRequest('Email is required'));
    if (!id) return next(APIError.badRequest('ID is required'));
    //check if detail exist
    let exist = await usernameExist(username);
    if (exist)
      return next(APIError.customError(`${username} is not available`, 400));
    if (!isValidEmail(email))
      return next(APIError.badRequest('Email is not valid'));
    exist = await emailExist(email);
    if (exist) return next(APIError.customError(`${email} already exist`, 400));
    const invite = await InvitationModel.findOne({ token: id }).exec();
    if (!invite)
      return next(
        APIError.customError('Invite ID is required, click on link again', 400)
      );
    const currentDate = new Date();
    const validDate = currentDate.getDate() - invite.createdAt.getDate();
    if (validDate > 7) {
      invite.token = '';
      invite.save();
      logger.info('Expired Invite detected', { meta: 'Invitation-service' });
      return next(APIError.customError(ERROR_FIELD.INVITE_EXPIRED, 400));
    }
    if (email !== invite.email) {
      logger.info('Admin registration mismatch detected', {
        meta: META.ACCOUNT_SERVICE,
      });
      return next(APIError.badRequest('Email mismatch'));
    }
    const details = { username, email, name };
    const hashedPassword = hashSync(password, 12);
    details.password = hashedPassword;
    if (req.body.phone) details.phone = req.body.phone;
    details.type = invite.role;
    details.status = CONSTANTS.ACCOUNT_STATUS[0];
    const register = await createAdmin(details);
    if (register.error) return next(APIError.customError(register.error, 400));
    logger.info('Admin registration successful', { meta: META.ACCOUNT_SERVICE });
    invite.token = '';
    invite.save();
    logger.info('Invite id invalided', { meta: META.ACCOUNT_SERVICE });
    const data = buildResponse.buildUser(register.toObject());
    const result = await registrationMailHandler(email, username);
    if (result.error) {
      await deleteUser(email);
      return next(APIError.customError(ERROR_FIELD.REG_FAILED));
    }
    const response = buildResponse.commonReponse(
      'Registration successful',
      data,
      'account'
    );
    res.status(201).json(response);
  } catch (error) {
    next(error);
  }
};
exports.getAccounts = async (req, res, next) => {
  try {
    if (!req.userId) return next(APIError.unauthenticated());
    const accounts = await getAllAccounts();
    if (accounts.length === 0)
      return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    logger.info('Account Found', { meta: META.ACCOUNT_SERVICE });
    const data = [];
    accounts.map((cur) => {
      if (cur._id.toString() !== req.userId)
        data.push(buildResponse.buildUser(cur.toObject()));
    });
    const response = buildResponse.commonReponse('Found', data, 'account');
    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};
exports.getAdminAccounts = async (req, res, next) => {
  try {
    if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error) return next(APIError.unauthorized());
    const accounts = await getAllAccounts();
    if (accounts.length === 0)
      return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    const data = []; 
    accounts.map((cur) => { 
      if (cur._id.toString() !== req.userId && cur.type !== CONSTANTS.ACCOUNT_TYPE[0])
        data.push(buildResponse.buildUser(cur.toObject()));
    });
    if (data.length === 0)
      return next(APIError.customError("No other admin found", 404));
    const response = buildResponse.commonReponse("Found", data, "account");
    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};
exports.forgotPassword = async (req, res, next) => {
  try {
    if (!req.query.username && !req.query.email)
      next(APIError.badRequest('Username or email is required'));
    let details;
    const data = {};
    for (const key in req.query) {
      data[key] = req.query[key];
    }
    if (data.email) {
      if (!isValidEmail(data.email))
        return next(APIError.customError(ERROR_FIELD.INVALID_EMAIL, 400));
    }
    if (data.username) {
      const useExist = await checkUsername(data.username);
      if (!useExist)
        return next(APIError.customError(ERROR_FIELD.ACCOUNT_NOT_FOUND, 404));
      if (useExist.error) return next(APIError.customError(useExist.error));
      logger.info('Forgot Password', { meta: 'account-service' });
      details = buildResponse.buildUser(useExist.toObject());
      details.userId = useExist._id;
      return res
        .status(200)
        .json({ success: true, msg: 'Found', user: details });
    } else if (data.email) {
      const useExist = await checkEmail(data.email);
      if (!useExist)
        return next(APIError.customError(ERROR_FIELD.ACCOUNT_NOT_FOUND, 404));
      if (useExist.error) return next(APIError.customError(useExist.error));
      logger.info('Forgot Password', { meta: 'account-service' });
      details = buildResponse.buildUser(useExist.toObject());
      details.userId = useExist._id;
      return res
        .status(200)
        .json({ success: true, msg: 'found', user: details });
    }
  } catch (error) {
    next(error);
  }
};
exports.sendRecoverMail = async (req, res, next) => {
  try {
    const { userId, email } = req.query;
    if (!userId) return next(APIError.badRequest('userid is required', 404));
    if (!email) return next(APIError.badRequest('Email is required', 404));
    const userExist = await userExistByIdAndEmail(userId, email);
    if (!userExist)
      return next(APIError.customError(ERROR_FIELD.ACCOUNT_NOT_FOUND, 404));
    if (userExist.error) return next(APIError.customError(userExist.error));
    const uniqueString = uuidv4() + userExist.id;
    const expiryTime = new Date();
    expiryTime.setTime(expiryTime.getTime() + 1800000);
    const infor = {
      user: userExist._id,
      uniqueString,
      expiryTime,
    };
    const saveLink = await passwordRecovery(infor);
    if (!saveLink)
      return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (saveLink.error) return next(APIError.customError(saveLink.error, 400));
    logger.info('Recovery Mail', { meta: 'account-service' });
    const result = await recoveryPasswordMailHandler(
      email,
      '30 minutes',
      uniqueString
    );
    if (result.error)
      return next(APIError.customError('Recovery mail failed to send', 400));
    logger.info('Recovery Mail', { meta: 'email-service' });
    res.status(200).json({
      ...result,
      id: uniqueString,
      msg: 'Recovery mail sent successfully',
    });
  } catch (error) {
    next(error);
  }
};

exports.verifyPasswordReset = async (req, res, next) => {
  try {
    const { id } = req.query;
    if (!id) return next(APIError.customError(ERROR_FIELD.INVALID_LINK, 400));
    const check = await getPasswordRecoveryInfor(id);
    if (!check)
      return next(APIError.customError(ERROR_FIELD.INVALID_LINK, 404));
    if (check.error) return next(APIError.customError(check.error, 400));
    //verify link
    const currentTime = new Date();
    if (currentTime > check.expiryTime) {
      await removePasswordRecoveryInfor(id);
      return next(APIError.customError('Link expired', 400));
    }
    logger.info('Verify password reset', { meta: 'account-service' });
    res
      .status(200)
      .json({ success: true, id: check.uniqueString, msg: 'Link is valid' });
  } catch (error) {
    return next(error);
  }
};
exports.resetPassword = async (req, res, next) => {
  try {
    const { newPassword, id } = req.body;
    if (!id) return next(APIError.badRequest('Recovery link id is required'));
    if (!newPassword) return next(APIError.badRequest('Provide new password'));
    const hashedPass = hashSync(newPassword, 12);

    const reset = await updateUserPass(id, hashedPass);
    if (!reset) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (reset.error) return next(APIError.customError(reset.error, 400));
    logger.info('Reset password successful', { meta: 'account-service' });
    res.clearCookie('jwt');
    res.status(200).json({ success: true, msg: 'Password reset successful' });
  } catch (error) {
    next(error);
  }
};
exports.updateUser = async (req, res, next) => {
  try {
    if (!req.userId) return next(APIError.unauthenticated());
    const details = {};
    for (const key in req.body) {
      details[key] = req.body[key];
    }
    if (details.length === 0) return next(APIError.badRequest('No data sent'));
    const update = await updateUserInfor(req.userId, details);
    if (!update) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (update.error) return next(APIError.customError(update.error, 400));
    const data = buildResponse.buildUser(update.toObject());
    const response = buildResponse.commonReponse(
      'Update successfully',
      data,
      'account'
    );
    logger.info(response.msg, { meta: META.ACCOUNT_SERVICE});
    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};

exports.accountByRole = async (req,res,next) =>{
  try{
    if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error) return next(APIError.unauthorized());
    const {role} = req.query;
    if(!role) return next(APIError.badRequest("Role is required"));
    const admins = await getAdminsByRole(role.toLowerCase());
    if(!admins) return  next(APIError.badRequest(ERROR_FIELD.NOT_FOUND));
    if(admins.error) return  next(APIError.badRequest(admins.error));
    const data = []; 
    admins.map((cur) => { 
        data.push(buildResponse.buildUserRole(cur.toObject()));
    });
    const response = buildResponse.commonReponse("Found", data, "account");
    res.status(200).json(response);
  }catch(error){
     next(error)
  }
}

exports.info = async (req, res, next) => {
  try {
  
const exist = await getUserById(req.userId)
    const data = buildResponse.buildUser(exist.toObject());
    logger.info("User info retrieved successfully", { meta: META.ACCOUNT_SERVICE });
    const response = buildResponse.commonReponse(
      "found",
      data,
      "user", 
    );

    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};
exports.editorAccount = async (req, res, next) =>{
  try{
       
    const admins = await getAdmins();
    if(!admins) return  next(APIError.badRequest(ERROR_FIELD.NOT_FOUND));
    if(admins.error) return  next(APIError.badRequest(admins.error));
    const data = []; 
    if(admins.length > 0){
      admins.map((cur) => {
        if(cur.type.toLowerCase() === CONSTANTS.ACCOUNT_TYPE[3])
        data.push(buildResponse.buildUserRole(cur.toObject()));
    });
  }
    const response = buildResponse.commonReponse("Found", data, "editors");
    res.status(200).json(response);
  }catch(error){
     next(error)
  }
}