import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import { 
  HomeIcon, 
  DocumentTextIcon, 
  MagnifyingGlassIcon, 
  BookOpenIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeIconSolid,
  DocumentTextIcon as DocumentTextIconSolid,
  MagnifyingGlassIcon as MagnifyingGlassIconSolid,
  BookOpenIcon as BookOpenIconSolid,
  PlusIcon as PlusIconSolid
} from '@heroicons/react/24/solid';
import { 
  HOME_ROUTE, 
  ARTICLES_BASE_ROUTE, 
  JOURNALS_BASE_ROUTE, 
  SUBMIT_ROUTE 
} from 'routes';
import { slideInFromBottom, buttonVariants } from 'design-system/animations';

/**
 * Modern Mobile Bottom Navigation
 * Provides easy access to main sections on mobile devices
 */

const MobileBottomNav = () => {
  const location = useLocation();
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Auto-hide on scroll down, show on scroll up
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  const navItems = [
    {
      label: 'Home',
      path: HOME_ROUTE,
      icon: HomeIcon,
      iconSolid: HomeIconSolid,
      color: 'text-blue-900' // Learning Publics dark blue
    },
    {
      label: 'Articles',
      path: ARTICLES_BASE_ROUTE,
      icon: DocumentTextIcon,
      iconSolid: DocumentTextIconSolid,
      color: 'text-red-600' // Learning Publics red
    },
    {
      label: 'Search',
      path: HOME_ROUTE, // Navigate to home and focus search
      icon: MagnifyingGlassIcon,
      iconSolid: MagnifyingGlassIconSolid,
      color: 'text-blue-900', // Learning Publics dark blue
      isSearch: true
    },
    {
      label: 'Journals',
      path: JOURNALS_BASE_ROUTE,
      icon: BookOpenIcon,
      iconSolid: BookOpenIconSolid,
      color: 'text-red-600' // Learning Publics red
    },
    {
      label: 'Submit',
      path: SUBMIT_ROUTE,
      icon: PlusIcon,
      iconSolid: PlusIconSolid,
      color: 'text-blue-900', // Learning Publics dark blue
      isAction: true
    }
  ];

  const isActive = (path) => {
    if (path === HOME_ROUTE) {
      return location.pathname === HOME_ROUTE;
    }
    return location.pathname.startsWith(path);
  };

  const handleSearchClick = () => {
    // Focus on search input in hero section
    const searchInput = document.querySelector('[data-search-input]');
    if (searchInput) {
      searchInput.focus();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.nav
          initial="hidden"
          animate="visible"
          exit="hidden"
          variants={slideInFromBottom}
          className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg md:hidden"
          style={{
            paddingBottom: 'env(safe-area-inset-bottom)',
          }}
        >
          <div className="flex items-center justify-around px-2 py-2">
            {navItems.map((item) => {
              const active = isActive(item.path);
              const IconComponent = active ? item.iconSolid : item.icon;
              
              if (item.isSearch) {
                return (
                  <motion.button
                    key={item.label}
                    onClick={handleSearchClick}
                    variants={buttonVariants}
                    initial="rest"
                    whileHover="hover"
                    whileTap="tap"
                    className="flex flex-col items-center justify-center p-2 min-w-0 flex-1"
                  >
                    <div className={`p-2 rounded-full transition-colors duration-200 ${
                      active 
                        ? `${item.color} bg-current bg-opacity-10` 
                        : 'text-gray-500 hover:text-gray-700'
                    }`}>
                      <IconComponent className="w-5 h-5" />
                    </div>
                    <span className={`text-xs font-medium mt-1 transition-colors duration-200 ${
                      active ? item.color : 'text-gray-500'
                    }`}>
                      {item.label}
                    </span>
                  </motion.button>
                );
              }

              return (
                <motion.div
                  key={item.label}
                  variants={buttonVariants}
                  initial="rest"
                  whileHover="hover"
                  whileTap="tap"
                  className="flex-1"
                >
                  <Link
                    to={item.path}
                    className="flex flex-col items-center justify-center p-2 min-w-0"
                  >
                    <div className={`p-2 rounded-full transition-colors duration-200 ${
                      active
                        ? `${item.color} bg-current bg-opacity-10`
                        : 'text-gray-500 hover:text-gray-700'
                    } ${item.isAction ? 'bg-red-600 text-white hover:bg-red-700' : ''}`}>
                      <IconComponent className="w-5 h-5" />
                    </div>
                    <span className={`text-xs font-medium mt-1 transition-colors duration-200 ${
                      active ? item.color : 'text-gray-500'
                    } ${item.isAction ? 'text-red-600' : ''}`}>
                      {item.label}
                    </span>
                  </Link>
                </motion.div>
              );
            })}
          </div>
        </motion.nav>
      )}
    </AnimatePresence>
  );
};

export default MobileBottomNav;
