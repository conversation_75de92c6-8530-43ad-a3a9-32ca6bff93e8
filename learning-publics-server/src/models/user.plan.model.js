const { Schema, model } = require("mongoose");

const UserPlanSchema = new Schema({
  plan:{
    type: Schema.Types.ObjectId,
    ref:"plan",
    required: true,
    indexed: true,
  },
  account: {
    type: Schema.Types.ObjectId,
    ref: "account",
    required: true,
    indexed: true,
  },
  // expiryDate: {
  //   type: Date,
  //   required: true,
  // },
  reference: {
    type: String,
    required: true,
    indexed: true,
  },
  planInfo:{ 
    type: [],
  }
}, {timestamps: true}
);
const UserPlanModel = model("Userplan", UserPlanSchema);
module.exports = UserPlanModel;