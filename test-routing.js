#!/usr/bin/env node

/**
 * Simple test script to verify the new routing endpoints
 * Run with: node test-routing.js
 */

const http = require('http');

const BASE_URL = 'http://localhost:4001';

// Test endpoints
const endpoints = [
  {
    name: 'SEO Articles List',
    path: '/api/v1/author/articles/seo?page=1&limit=3',
    expected: 'articles'
  },
  {
    name: 'Article by Slug',
    path: '/api/v1/author/articles/slug/knowledge-attitude-and-practices-of-students-of-university-of-the-gambia-towards-female-genital-mutilation',
    expected: 'article'
  },
  {
    name: 'Search Articles',
    path: '/api/v1/author/articles/search?q=gender&page=1&limit=2',
    expected: 'search'
  },
  {
    name: 'Related Articles',
    path: '/api/v1/author/articles/related?category=6865efcf9de1663b1b28eb9f&exclude=6866c805fe3d7bc7a4cb6310&limit=3',
    expected: 'related'
  }
];

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function runTests() {
  console.log('🚀 Testing JMS Routing Modernization Endpoints\n');
  console.log('=' .repeat(60));
  
  let passed = 0;
  let failed = 0;
  
  for (const endpoint of endpoints) {
    try {
      console.log(`\n📋 Testing: ${endpoint.name}`);
      console.log(`🔗 URL: ${BASE_URL}${endpoint.path}`);
      
      const result = await makeRequest(`${BASE_URL}${endpoint.path}`);
      
      if (result.status === 200 && result.data.success) {
        if (result.data[endpoint.expected]) {
          console.log(`✅ PASS - Status: ${result.status}, Data: Found ${endpoint.expected}`);
          
          // Show sample data
          if (endpoint.expected === 'articles' && result.data.articles.length > 0) {
            console.log(`   📄 Sample: "${result.data.articles[0].title.substring(0, 50)}..."`);
          } else if (endpoint.expected === 'article' && result.data.article) {
            console.log(`   📄 Article: "${result.data.article.article.title.substring(0, 50)}..."`);
            console.log(`   🔗 Slug: ${result.data.article.article.slug}`);
          } else if (endpoint.expected === 'search' && result.data.search) {
            console.log(`   🔍 Found: ${result.data.search.pagination.total} articles`);
          } else if (endpoint.expected === 'related') {
            console.log(`   🔗 Related: ${result.data.related.length} articles`);
          }
          
          passed++;
        } else {
          console.log(`❌ FAIL - Missing expected data field: ${endpoint.expected}`);
          failed++;
        }
      } else {
        console.log(`❌ FAIL - Status: ${result.status}, Success: ${result.data.success}`);
        if (result.data.error) {
          console.log(`   Error: ${result.data.error}`);
        }
        failed++;
      }
    } catch (error) {
      console.log(`❌ FAIL - ${error.message}`);
      failed++;
    }
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! The routing modernization is working correctly.');
    console.log('\n📋 Next Steps:');
    console.log('   1. Test frontend routing in browser');
    console.log('   2. Verify SEO meta tags are generated');
    console.log('   3. Test error boundaries and 404 pages');
    console.log('   4. Run performance tests');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the server and endpoints.');
    process.exit(1);
  }
}

// Check if server is running first
async function checkServer() {
  try {
    await makeRequest(`${BASE_URL}/api/v1/author/articles/seo?page=1&limit=1`);
    return true;
  } catch (error) {
    console.log('❌ Server not running or not accessible at http://localhost:4001');
    console.log('   Please start the server with: npm run dev');
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await runTests();
  }
}

main().catch(console.error);
