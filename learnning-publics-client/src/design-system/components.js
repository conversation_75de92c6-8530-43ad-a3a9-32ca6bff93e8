/**
 * Component Variants System
 * Reusable component patterns and variants for consistent UI
 */

import { colors, typography, spacing, borderRadius, shadows } from './tokens';

// Button Variants
export const buttonVariants = {
  // Base button styles
  base: {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: borderRadius.md,
    fontWeight: typography.fontWeight.medium,
    fontSize: typography.fontSize.sm,
    lineHeight: typography.lineHeight.none,
    transition: 'all 150ms ease-in-out',
    cursor: 'pointer',
    border: 'none',
    outline: 'none',
    textDecoration: 'none',
    userSelect: 'none',
    '&:focus': {
      outline: '2px solid transparent',
      outlineOffset: '2px',
      boxShadow: `0 0 0 2px ${colors.primary[500]}40`
    },
    '&:disabled': {
      opacity: '0.5',
      cursor: 'not-allowed'
    }
  },

  // Size variants
  sizes: {
    xs: {
      height: '1.5rem',
      paddingLeft: spacing[2],
      paddingRight: spacing[2],
      fontSize: typography.fontSize.xs
    },
    sm: {
      height: '2rem',
      paddingLeft: spacing[3],
      paddingRight: spacing[3],
      fontSize: typography.fontSize.sm
    },
    md: {
      height: '2.5rem',
      paddingLeft: spacing[4],
      paddingRight: spacing[4],
      fontSize: typography.fontSize.sm
    },
    lg: {
      height: '3rem',
      paddingLeft: spacing[6],
      paddingRight: spacing[6],
      fontSize: typography.fontSize.base
    },
    xl: {
      height: '3.5rem',
      paddingLeft: spacing[8],
      paddingRight: spacing[8],
      fontSize: typography.fontSize.lg
    }
  },

  // Color variants
  variants: {
    primary: {
      backgroundColor: colors.primary[500],
      color: colors.neutral[50],
      '&:hover': {
        backgroundColor: colors.primary[600]
      },
      '&:active': {
        backgroundColor: colors.primary[700]
      }
    },
    secondary: {
      backgroundColor: colors.secondary[500],
      color: colors.neutral[50],
      '&:hover': {
        backgroundColor: colors.secondary[600]
      },
      '&:active': {
        backgroundColor: colors.secondary[700]
      }
    },
    outline: {
      backgroundColor: 'transparent',
      color: colors.primary[600],
      border: `1px solid ${colors.primary[300]}`,
      '&:hover': {
        backgroundColor: colors.primary[50],
        borderColor: colors.primary[400]
      }
    },
    ghost: {
      backgroundColor: 'transparent',
      color: colors.primary[600],
      '&:hover': {
        backgroundColor: colors.primary[50]
      }
    },
    destructive: {
      backgroundColor: colors.error[500],
      color: colors.neutral[50],
      '&:hover': {
        backgroundColor: colors.error[600]
      }
    }
  }
};

// Card Variants
export const cardVariants = {
  base: {
    backgroundColor: colors.neutral[50],
    borderRadius: borderRadius.lg,
    border: `1px solid ${colors.neutral[200]}`,
    overflow: 'hidden'
  },
  
  variants: {
    default: {
      boxShadow: shadows.sm
    },
    elevated: {
      boxShadow: shadows.md
    },
    interactive: {
      cursor: 'pointer',
      transition: 'all 150ms ease-in-out',
      '&:hover': {
        boxShadow: shadows.lg,
        transform: 'translateY(-2px)'
      }
    },
    outlined: {
      border: `2px solid ${colors.neutral[200]}`,
      boxShadow: 'none'
    }
  }
};

// Input Variants
export const inputVariants = {
  base: {
    width: '100%',
    borderRadius: borderRadius.md,
    border: `1px solid ${colors.neutral[300]}`,
    backgroundColor: colors.neutral[50],
    fontSize: typography.fontSize.sm,
    lineHeight: typography.lineHeight.normal,
    transition: 'all 150ms ease-in-out',
    '&:focus': {
      outline: 'none',
      borderColor: colors.primary[500],
      boxShadow: `0 0 0 3px ${colors.primary[500]}20`
    },
    '&:disabled': {
      backgroundColor: colors.neutral[100],
      color: colors.neutral[500],
      cursor: 'not-allowed'
    },
    '&::placeholder': {
      color: colors.neutral[400]
    }
  },

  sizes: {
    sm: {
      height: '2rem',
      paddingLeft: spacing[3],
      paddingRight: spacing[3],
      fontSize: typography.fontSize.sm
    },
    md: {
      height: '2.5rem',
      paddingLeft: spacing[4],
      paddingRight: spacing[4],
      fontSize: typography.fontSize.sm
    },
    lg: {
      height: '3rem',
      paddingLeft: spacing[4],
      paddingRight: spacing[4],
      fontSize: typography.fontSize.base
    }
  },

  variants: {
    default: {},
    error: {
      borderColor: colors.error[500],
      '&:focus': {
        borderColor: colors.error[500],
        boxShadow: `0 0 0 3px ${colors.error[500]}20`
      }
    },
    success: {
      borderColor: colors.success[500],
      '&:focus': {
        borderColor: colors.success[500],
        boxShadow: `0 0 0 3px ${colors.success[500]}20`
      }
    }
  }
};

// Badge Variants
export const badgeVariants = {
  base: {
    display: 'inline-flex',
    alignItems: 'center',
    borderRadius: borderRadius.full,
    fontSize: typography.fontSize.xs,
    fontWeight: typography.fontWeight.medium,
    paddingLeft: spacing[2],
    paddingRight: spacing[2],
    paddingTop: spacing[1],
    paddingBottom: spacing[1]
  },

  variants: {
    default: {
      backgroundColor: colors.neutral[100],
      color: colors.neutral[800]
    },
    primary: {
      backgroundColor: colors.primary[100],
      color: colors.primary[800]
    },
    secondary: {
      backgroundColor: colors.secondary[100],
      color: colors.secondary[800]
    },
    success: {
      backgroundColor: colors.success[100],
      color: colors.success[800]
    },
    warning: {
      backgroundColor: colors.warning[100],
      color: colors.warning[800]
    },
    error: {
      backgroundColor: colors.error[100],
      color: colors.error[800]
    },
    outline: {
      backgroundColor: 'transparent',
      color: colors.neutral[700],
      border: `1px solid ${colors.neutral[300]}`
    }
  }
};

// Loading Skeleton Variants (Learning Publics Brand Colors)
export const skeletonVariants = {
  base: {
    backgroundColor: '#f1f5f9', // Light gray-blue for Learning Publics theme
    borderRadius: borderRadius.md,
    animation: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite'
  },

  shapes: {
    text: {
      height: '1rem',
      width: '100%'
    },
    title: {
      height: '1.5rem',
      width: '60%'
    },
    avatar: {
      height: '2.5rem',
      width: '2.5rem',
      borderRadius: borderRadius.full
    },
    button: {
      height: '2.5rem',
      width: '6rem'
    },
    card: {
      height: '12rem',
      width: '100%'
    }
  }
};

// Animation Presets
export const animations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.2 }
  },
  
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3 }
  },
  
  slideDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 },
    transition: { duration: 0.3 }
  },
  
  scaleIn: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 },
    transition: { duration: 0.2 }
  },
  
  stagger: {
    container: {
      animate: {
        transition: {
          staggerChildren: 0.1
        }
      }
    },
    item: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 }
    }
  }
};

export default {
  buttonVariants,
  cardVariants,
  inputVariants,
  badgeVariants,
  skeletonVariants,
  animations
};
