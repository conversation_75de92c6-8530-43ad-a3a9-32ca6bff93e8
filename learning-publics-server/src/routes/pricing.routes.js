const express = require('express');
const router = express.Router();
const pricingController = require('../controllers/pricing.controller');
const { adminRequired, notELearningRole } = require('../middlewares/auth.middleware');

// Public routes - Get current pricing
router.get('/service/:serviceType', pricingController.getServicePricing);

// Admin routes - Require authentication and admin role
// Get all pricing configurations
router.get('/admin/configs', adminRequired, notELearningRole, pricingController.getAllPricingConfigs);

// Create new pricing configuration
router.post('/admin/configs', adminRequired, notELearningRole, pricingController.createPricingConfig);

// Update pricing configuration
router.put('/admin/configs/:id', adminRequired, notELearningRole, pricingController.updatePricingConfig);

// Schedule price change
router.post('/admin/configs/:id/schedule', adminRequired, notELearningRole, pricingController.schedulePriceChange);

// Set promotional pricing
router.post('/admin/configs/:id/promotion', adminRequired, notELearningRole, pricingController.setPromotionalPricing);

// Toggle service pricing (free/paid)
router.patch('/admin/service/:serviceType/toggle', adminRequired, notELearningRole, pricingController.toggleServicePricing);

// Execute scheduled pricing changes (can be called manually or by cron)
router.post('/admin/execute-scheduled', adminRequired, notELearningRole, pricingController.executeScheduledPricing);

// Get pricing analytics
router.get('/admin/analytics', adminRequired, notELearningRole, pricingController.getPricingAnalytics);

// Delete pricing configuration (Super Admin only - for now using same middleware)
router.delete('/admin/configs/:id', adminRequired, notELearningRole, pricingController.deletePricingConfig);

module.exports = router;
