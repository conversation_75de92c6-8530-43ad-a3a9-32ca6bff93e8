import { MdArrowDropUp, MdOutlineArrowDropDown } from "react-icons/md";
import { useEffect, useRef, useState } from "react";

import { FaHome } from "react-icons/fa";

function CustomDropdown({
  options,
  defaultValue,
  onSelect,
  extendedClassNames,
  title = "Select Address"
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(defaultValue);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleOutsideClick = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("click", handleOutsideClick);

    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleOptionClick = (option) => {
    setSelectedOption(option);
    onSelect(option);
    setIsOpen(false);
  };

  return (
    <div
      className={extendedClassNames}
      ref={dropdownRef}
      onClick={toggleDropdown}
    >
      <div className=" cursor-pointer  p-4 border border-blue-400 transition-colors duration-200 ease-in-out capitalize  rounded-lg flex items-center justify-center gap-4 hover:text-blue-400">
        <FaHome />

        <div className="w-3/5 whitespace-nowrap  overflow-hidden text-ellipsis">
          {selectedOption || defaultValue || title}
        </div>
        {!isOpen ? <MdOutlineArrowDropDown /> : <MdArrowDropUp />}
      </div>

      {isOpen && (
        <ul className=" min-w-full w-[max-content] flex items-stretch flex-col place-self-center space-y-3  p-4 border bg-slate-600  border-blue-400 capitalize  rounded-lg my-4 z-50 divide-y divide-blue-400 absolute">
          {options.map((option) => (
            <li
              key={option.key}
              onClick={() => handleOptionClick(option.key)}
              className=" cursor-pointer hover:text-blue-400 pt-3 first:pt-0 "
            >
              {option.value}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default CustomDropdown;
