import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { Cloudinary } from '@cloudinary/url-gen';
import axios from 'axios';

import ArticlesTable from '../Articles/articles-table';
import { TableErrorBoundary } from 'components/ErrorBoundary/SpecializedErrorBoundaries';
import { usePerformanceMonitor } from 'hooks/usePerformanceMonitor';
import { fadeIn } from 'design-system/animations';
import { DOWNLOAD_ENDPOINT } from 'api/ACTION';
import { LOCAL_STORAGE } from 'api/LOCALSTORAGE';
import { heading } from '../sharedComponents/articleData';

/**
 * Optimized Dashboard Articles Section Component
 * Handles article display, actions, and performance monitoring
 */
const DashboardArticlesSection = ({ 
  data, 
  userRole, 
  handleUpdateData, 
  isLoading = false 
}) => {
  const navigate = useNavigate();
  const [actionLoading, setActionLoading] = useState({});
  
  // Performance monitoring
  const { measureAsync, measureSync } = usePerformanceMonitor('DashboardArticlesSection', {
    trackRenders: true,
    trackMemory: true
  });

  // Optimized download handler with performance tracking
  const handleDownload = useCallback(async (article) => {
    const downloadOperation = async () => {
      setActionLoading(prev => ({ ...prev, [article.articleId]: 'downloading' }));
      
      try {
        const cld = new Cloudinary({
          cloud: {
            cloudName: process.env.REACT_APP_CLOUDINARY_CLOUD_NAME
          }
        });

        const response = await axios.get(
          DOWNLOAD_ENDPOINT.DOWNLOAD_ARTICLE(article.articleId),
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem(LOCAL_STORAGE.TOKEN)}`
            },
            responseType: 'blob'
          }
        );

        // Create download link
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${article.title || 'article'}.pdf`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);

        toast.success('Article downloaded successfully');
      } catch (error) {
        console.error('Download error:', error);
        toast.error(error.response?.data?.error || 'Download failed');
      } finally {
        setActionLoading(prev => ({ ...prev, [article.articleId]: null }));
      }
    };

    await measureAsync('Article Download', downloadOperation);
  }, [measureAsync]);

  // Optimized view handler
  const handleView = useCallback((article) => {
    measureSync('Navigate to Article', () => {
      navigate(`/admin/articles/${article.articleId}`);
    });
  }, [navigate, measureSync]);

  // Optimized edit handler
  const handleEdit = useCallback((article) => {
    measureSync('Navigate to Edit', () => {
      navigate(`/admin/articles/edit/${article.articleId}`);
    });
  }, [navigate, measureSync]);

  // Memoized table data
  const tableData = React.useMemo(() => {
    return data?.slice(0, 6) || [];
  }, [data]);

  // Memoized columns configuration
  const columns = React.useMemo(() => {
    return [
      {
        key: 'title',
        label: 'Title',
        sortable: true,
        render: (value, row) => (
          <div className="max-w-xs">
            <div className="font-medium text-gray-900 truncate">{value}</div>
            <div className="text-sm text-gray-500 truncate">{row.author}</div>
          </div>
        )
      },
      {
        key: 'category',
        label: 'Category',
        sortable: true,
        render: (value) => (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {value}
          </span>
        )
      },
      {
        key: 'status',
        label: 'Status',
        sortable: true,
        render: (value) => {
          const statusColors = {
            published: 'bg-green-100 text-green-800',
            review: 'bg-yellow-100 text-yellow-800',
            draft: 'bg-gray-100 text-gray-800',
            rejected: 'bg-red-100 text-red-800'
          };
          
          return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[value] || statusColors.draft}`}>
              {value?.charAt(0).toUpperCase() + value?.slice(1) || 'Draft'}
            </span>
          );
        }
      },
      {
        key: 'createdAt',
        label: 'Created',
        sortable: true,
        render: (value) => (
          <div className="text-sm text-gray-900">
            {new Date(value).toLocaleDateString()}
          </div>
        )
      }
    ];
  }, []);

  if (isLoading) {
    return (
      <motion.div 
        className="bg-white rounded-lg shadow-md p-6"
        {...fadeIn}
      >
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </motion.div>
    );
  }

  if (!tableData.length) {
    return (
      <motion.div 
        className="bg-white rounded-lg shadow-md p-6 text-center"
        {...fadeIn}
      >
        <div className="text-gray-500 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Articles Found</h3>
        <p className="text-gray-500 mb-4">
          There are no articles to display at the moment.
        </p>
        <button
          onClick={() => navigate('/admin/articles/upload')}
          className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Upload New Article
        </button>
      </motion.div>
    );
  }

  return (
    <motion.div 
      className="bg-white rounded-lg shadow-md overflow-hidden"
      {...fadeIn}
    >
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Recent Articles
          </h3>
          <button
            onClick={() => navigate('/admin/articles')}
            className="text-sm text-red-600 hover:text-red-700 font-medium"
          >
            View All →
          </button>
        </div>
      </div>

      <TableErrorBoundary tableName="Dashboard Articles">
        <div className="overflow-hidden">
          <ArticlesTable
            data={tableData}
            columns={columns}
            hasStatus={true}
            heading={heading}
            value=""
            handleUpdateData={handleUpdateData}
            user={userRole}
            actions={[
              { 
                name: "download", 
                action: handleDownload,
                loading: (article) => actionLoading[article.articleId] === 'downloading'
              },
              { 
                name: "view", 
                action: handleView 
              },
              { 
                name: "edit", 
                action: handleEdit 
              }
            ]}
            compact={true}
            showPagination={false}
          />
        </div>
      </TableErrorBoundary>

      {/* Quick Actions Footer */}
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">
            Showing {Math.min(6, tableData.length)} of {data?.length || 0} articles
          </span>
          <div className="flex space-x-2">
            <button
              onClick={() => navigate('/admin/articles/upload')}
              className="inline-flex items-center px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors"
            >
              Upload Article
            </button>
            <button
              onClick={() => navigate('/admin/articles')}
              className="inline-flex items-center px-3 py-1 bg-blue-900 text-white text-xs rounded hover:bg-blue-800 transition-colors"
            >
              Manage All
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default DashboardArticlesSection;
