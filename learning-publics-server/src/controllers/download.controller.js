const { default: mongoose } = require("mongoose");
const { CONSTANTS } = require("../config");
const logger = require("../logger");
const { getDownloadByArticleId, createDownload, getDownloads, deleteDownload, getArticlesById } = require("../services");
const { downloads } = require("../services/download.service");
const { ERROR_FIELD, META } = require("../utils/actions");
const { APIError } = require("../utils/apiError")
const responsBuilder = require("../utils/responsBuilder");

exports.downloads = async (req, res, next) => {
  try {
    const {articleId} = req.body;
    if(!articleId) return next(APIError.badRequest("Article ID is required"));
     // check if article has be accepted
     const articleStatus = await getArticlesById(articleId, req.userId, req.userType);
     if(!articleStatus) return next(APIError.badRequest("Article does not exist"));
     if(articleStatus.error) return next(APIError.badRequest(articleStatus.error));
     if(req.userType !== CONSTANTS.ACCOUNT_TYPE[2]){
       const assigned = articleStatus.editors.find(x =>x.id.toString() === req.userId);
       if(!assigned) return next(APIError.badRequest("This article as not assigned to you"));
     }
      const details = {admin:req.userId, article:articleId};
      const download = await createDownload(details);
      if(!download) return next(APIError.customError(ERROR_FIELD.NOT_FOUND,404));
      if (download.error) return next(APIError.customError(download.error, 400));
      logger.info("Article downloaded successfully", {meta: META.DOWNLOAD_SERVICE});
      res.status(201).json({success: true, msg: "Article downloaded successfully"});
  } catch (error) {
    next(error)
  }
}
exports.getUserDownloads = async (req, res, next) => {
  try{
    const downloads = await getDownloads(req.userId, req.userType);
    if(!downloads || downloads.length === 0) return next(APIError.notFound(ERROR_FIELD.NOT_FOUND));
    if(downloads.error) return next(APIError.badRequest(downloads.error));
    logger.info("Download retrieved successfully", {meta: META.DOWNLOAD_SERVICE})
    const data = downloads.map((cur) => {
      return responsBuilder.buildDownload(cur.toObject());
    }) 
    const response = responsBuilder.commonReponse("Found", data, "download");
    res.status(200).json(response);
  }catch(error){
    next(error);
  }
}
exports.getUserDownloadsById = async (req, res, next) => {
  try{
    const {articleId} = req.query;
    if(!articleId) return next(APIError.badRequest("Article ID is required"))
    const download = await getDownloadByArticleId(req.userId,articleId.trim());
    if(!download) return next(APIError.customError(ERROR_FIELD.NOT_FOUND,404));
    if(download.error) return next(APIError.customError(download.error, 400));
    logger.info("Downloaded retrieved by ID successfully", {meta: META.DOWNLOAD_SERVICE})
    const data =   responsBuilder.buildDownload(download.toObject());
    const response = responsBuilder.commonReponse("Found", data, "download");
    res.status(200).json(response);
  }catch(error){
    next(error);
  }
}
exports.deleteUserDownloads = async (req, res, next) => {
  try{
    const { id }= req.query;
    if (!id) return next(APIError.badRequest("Download ID is required"));
    if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2]?.error)) return next(APIError.unauthorized());
    const download = await deleteDownload(req.userId, id);
    if(!download) return next(APIError.customError(ERROR_FIELD.NOT_FOUND,404));
    if(download.error) return next(APIError.customError(download.error, 400));
    logger.info("Download deleted successfully", {meta: META.DOWNLOAD_SERVICE})
    res.status(200).json({success: true, msg: "Download deleted successfully"});
  }catch(error){
    next(error);
  }
}
