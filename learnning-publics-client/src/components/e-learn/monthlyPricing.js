import "./Elearn.css";

import { Heading, TextBody } from "./style";
import React, { useEffect, useState } from "react";

import Mainlayout from "layout/MainLayout";
import PaymentCard from "components/payment/PaymentCard";
import { PaymentCont } from "components/payment/payment.style";
import { USER_PLAN } from "api/ACTION";
import UserLoginComp from "components/Home/Admin Login/userLogin";
import jmsApp from "api/jms";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";

// import PaymentCard from 'components/payment/PaymentCard'

const MonthlyPricing = () => {
  const [plan, setPlan] = useState([]);
  const navigate = useNavigate();

  const handleData = async () => {
    try {
      const { data } = await jmsApp.get(USER_PLAN.GET_PLAN());
      if (data.success) {
        setPlan(data.plans);
      }
    } catch (error) {
      // toast.error("error");
    }
  };

  const handler = async (e) => {
    navigate("/create-account");
  };

  useEffect(() => {
    handleData();
  }, []);
  return (
    <div>
      <section>
        <PaymentCont>
          <div className="sub" style={{ width: "100%" }}>
            <h4>
              We create a monthly pricing package for all standard students
            </h4>
            <p style={{ width: "60%" }}>
              Basically we create this package for those who are really
              interested and get benifited from our courses or books. We want to
              make a low cost package for them. So that they can purchase any
              courses with the package they buy from us. Also will get free
              books from every packages.
            </p>
          </div>
          <div className="paymentOffers">
            {plan?.map((option, i) => (
              <div key={option.planId}>
                <PaymentCard
                  id={option.planId}
                  key={option.planId}
                  planId={option.planId}
                  price={option.price}
                  handler={handler}
                  type={option.type}
                />
              </div>
            ))}
          </div>
        </PaymentCont>
      </section>

      {/* <UserLoginComp /> */}
    </div>
  );
};

export default MonthlyPricing;
