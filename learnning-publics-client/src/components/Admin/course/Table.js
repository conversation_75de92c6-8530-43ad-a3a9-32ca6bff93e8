import {
  Article,
  Move,
  TableData
} from "components/Admin/sharedComponents/style";
import React, { useEffect, useState } from "react";
import { usePagination, useTable } from "react-table";

import { AiFillDelete } from "react-icons/ai";
import { BiEdit } from "react-icons/bi";
import { IconContext } from "react-icons/lib";
import { Link } from "react-router-dom";

const CourseTable = ({ data, columns, color, handleDelete, handleEdit }) => {
  const [tableInfo, setTableInfo] = useState(data);

  useEffect(() => {
    setTableInfo(data);
  }, [data]);

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    page,
    nextPage,
    canNextPage,
    canPreviousPage,
    previousPage,
    pageOptions,
    state,
    prepareRow
  } = useTable({ columns, data: tableInfo }, usePagination);

  const { pageIndex } = state;

  return (
    <div>
      <Article color={color} {...getTableProps()}>
        <thead className="table">
          {headerGroups?.map((headerGroup, idx) => (
            <tr
              key={idx}
              style={{ background: `${color === "grey" ? "" : "white"}` }}
              {...headerGroup.getHeaderGroupProps()}
            >
              {headerGroup.headers?.map((head, headID) => (
                <th
                  {...head.getHeaderProps()}
                  key={headID}
                  style={{ color: `${color === "grey" ? "" : "black"}` }}
                >
                  {head.render("Header")}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody {...getTableBodyProps()}>
          {page?.map((item, id) => {
            prepareRow(item);
            return (
              <TableData {...item.getRowProps()} id={id} key={id}>
                {item.cells?.map((cell, i) => {
                  return (
                    <td
                      key={`&&${i}`}
                      {...cell.getCellProps()}
                      className={`${
                        cell.column.Header === "Type" ? "title" : ""
                      }`}
                    >
                      {cell.column.Header === "Links" ? (
                        <Link
                          to={cell.row.original.resourceUrl}
                          target="_blank"
                        >
                          Resource Link
                        </Link>
                      ) : cell.column.Header === "Actions" ? (
                        <div className="actions">
                          <IconContext.Provider
                            value={{ color: "green", size: "20px" }}
                          >
                            <div
                              id={id}
                              onClick={(e) => handleEdit(cell.row.original)}
                            >
                              <BiEdit />
                            </div>
                          </IconContext.Provider>
                          <IconContext.Provider
                            value={{ color: "red", size: "20px" }}
                          >
                            <div
                              id={id}
                              onClick={() =>
                                handleDelete(cell.row.original.courseId)
                              }
                            >
                              <AiFillDelete />
                            </div>
                          </IconContext.Provider>
                        </div>
                      ) : (
                        cell.render("Cell")
                      )}
                    </td>
                  );
                })}
              </TableData>
            );
          })}
        </tbody>
      </Article>
      {data.length > 10 && (
        <Move>
          <span>
            Page{" "}
            <strong>
              {pageIndex + 1} of {pageOptions.length}
            </strong>{" "}
          </span>
          <button onClick={() => previousPage()} disabled={!canPreviousPage}>
            Prev
          </button>
          <button onClick={() => nextPage()} disabled={!canNextPage}>
            Next
          </button>
        </Move>
      )}
    </div>
  );
};

export default CourseTable;
