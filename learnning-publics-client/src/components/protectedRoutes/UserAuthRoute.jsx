import { Navigate, useNavigate } from "react-router-dom";
import { LOCAL_STORAGE } from "api/LOCALSTORAGE";

export function AuthRoutes({ children }) {
  const user = LOCAL_STORAGE.user();
  if (user) {
    // Redirect them to the /login page, but save the current location they were
    // trying to go to when they were redirected. This allows us to send them
    // along to that page after they login, which is a nicer user experience
    // than dropping them off on the home page.
    if (user.type === "user") {
      return <Navigate to='/dashboard' replace />;
    }
    return <Navigate to='/admin/dashboard' replace />;
  }

  return children;
}
