// import { USER_PLAN } from "api/ACTION";
// import jmsApp from "api/jms";

import { AiFillDelete, AiOutlineEyeInvisible } from "react-icons/ai";
import {
  Article,
  Move,
  TableData
} from "components/Admin/sharedComponents/style";
import React, { useEffect, useState } from "react";
import { usePagination, useTable } from "react-table";

import { BiEdit } from "react-icons/bi";
import { FiEye } from "react-icons/fi";
import { IconContext } from "react-icons/lib";

const PlanTable = ({
  data,
  columns,
  color,
  handleDelete,
  handleEdit,
  viewCourses
}) => {
  const [tableInfo, setTableInfo] = useState(data);

  useEffect(() => {
    setTableInfo(data);
  }, []);

  useEffect(() => {
    setTableInfo(data);
  }, [data]);

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    page,
    nextPage,
    canNextPage,
    canPreviousPage,
    previousPage,
    pageOptions,
    state,
    prepareRow
  } = useTable({ columns, data: tableInfo }, usePagination);

  const { pageIndex } = state;
  return (
    <>
      <Article color={color} {...getTableProps()}>
        <thead className="table">
          {headerGroups?.map((headerGroup, idx) => (
            <tr
              key={idx}
              style={{ background: `${color === "grey" ? "" : "white"}` }}
              {...headerGroup.getHeaderGroupProps()}
            >
              {headerGroup.headers?.map((head, headID) => (
                <th
                  {...head.getHeaderProps()}
                  key={headID}
                  style={{ color: `${color === "grey" ? "" : "black"}` }}
                >
                  {head.render("Header")}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody {...getTableBodyProps()}>
          {page?.map((item, id) => {
            prepareRow(item);
            return (
              <TableData {...item.getRowProps()} id={id} key={id}>
                {item.cells?.map((cell, i) => {
                  return (
                    <td
                      key={i}
                      {...cell.getCellProps()}
                      className={`${
                        cell.column.Header === "Type" ? "title" : ""
                      }`}
                    >
                      {cell.column.Header === "Courses(No)" ? (
                        <>{cell.value.length}</>
                      ) : cell.column.Header === "Actions" ? (
                        <div className="actions">
                          {cell.row.original.count > 0 ? (
                            <IconContext.Provider
                              value={{ color: "blue", size: "20px" }}
                            >
                              <div
                                id={id}
                                onClick={() =>
                                  viewCourses(cell.row.original.courses)
                                }
                              >
                                <FiEye />
                              </div>
                            </IconContext.Provider>
                          ) : (
                            <IconContext.Provider
                              value={{ color: "blue", size: "20px" }}
                            >
                              <div id={id}>
                                <AiOutlineEyeInvisible />
                              </div>
                            </IconContext.Provider>
                          )}
                          <IconContext.Provider
                            value={{ color: "green", size: "20px" }}
                          >
                            <div
                              id={id}
                              onClick={(e) => handleEdit(cell.row.original)}
                            >
                              <BiEdit />
                            </div>
                          </IconContext.Provider>
                          <IconContext.Provider
                            value={{ color: "red", size: "20px" }}
                          >
                            <div
                              id={id}
                              onClick={() =>
                                handleDelete(cell.row.original.planId)
                              }
                            >
                              <AiFillDelete />
                            </div>
                          </IconContext.Provider>
                        </div>
                      ) : (
                        cell.render("Cell")
                      )}
                    </td>
                  );
                })}
              </TableData>
            );
          })}
        </tbody>
      </Article>
      {data.length > 10 && (
        <Move>
          <span>
            Page{" "}
            <strong>
              {pageIndex + 1} of {pageOptions.length}
            </strong>{" "}
          </span>
          <button onClick={() => previousPage()} disabled={!canPreviousPage}>
            Prev
          </button>
          <button onClick={() => nextPage()} disabled={!canNextPage}>
            Next
          </button>
        </Move>
      )}
    </>
  );
};

export default PlanTable;
