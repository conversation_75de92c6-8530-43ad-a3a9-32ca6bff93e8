import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

import { ADMIN_ENDPOINTS } from "api/ACTION";
import { ADMIN_SIGNUP_ROUTE } from "routes";
import { AuthButton } from "components/Auth";
import { FormSkeleton } from "components/Common/Skeleton";
import { VerifyCont } from "./style";
import jmsApp from "api/jms";
import { toast } from "react-hot-toast";



function VerifyInvite() {
  let { id } = useParams();
  const navigate = useNavigate();

  id = id.split("=")[1];
  const verifyInvite = async () => {
    localStorage.setItem("verifyId", JSON.stringify(id));
    try {
      const { data } = await jmsApp.post(ADMIN_ENDPOINTS.VERIFY_INVITE(), {
        id: id
      });

      if (data.success) {
        navigate(`${ADMIN_SIGNUP_ROUTE}`);
      }
    } catch (err) {
      if (err.response) {
        toast.error(err.response.data.error);
      }
    }
  };

  useEffect(() => {
    verifyInvite();
  }, []);

  return (
    <VerifyCont>
      <h1>Please verify invite sent to your mail to continue as an Admin</h1>
      <FormSkeleton fields={1} className="max-w-md mx-auto" />
      <h1>If no action after few seconds click on the button below.</h1>
      <form>
        <AuthButton title="Verify Invite" />
      </form>
    </VerifyCont>
  );
}

export default VerifyInvite;
