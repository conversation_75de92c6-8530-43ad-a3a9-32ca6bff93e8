import React from 'react';
import { Helmet } from 'react-helmet-async';
import { generateCanonicalUrl } from 'utils/seo';
import JournalsComp from 'components/Journals/JournalsComp';

const JournalsListPage = () => {
  const canonicalUrl = generateCanonicalUrl('/journals');

  return (
    <>
      <Helmet>
        <title>Academic Journals | Learning Publics</title>
        <meta name="description" content="Browse our collection of academic journals across various disciplines." />
        <link rel="canonical" href={canonicalUrl} />
      </Helmet>

      <JournalsComp />
    </>
  );
};

export default JournalsListPage;
