import { css } from "styled-components"

// type RuleOrQueryType = CSSObject | TemplateStringsArray;

const mediaQuery = query => rules =>
	css`
		@media screen and (${css(query)}) {
			${css(rules)}
		}
	`

const media = {
	// Updated to align with design system breakpoints (mobile-first)
	smallMobile: mediaQuery`max-width: 475px`, // xs breakpoint
	mobile: mediaQuery`max-width: 640px`, // sm breakpoint
	tablet: mediaQuery`max-width: 768px`, // md breakpoint
	smallDesktop: mediaQuery`min-width: 1024px`, // lg breakpoint
	smallDesktopMinimum: mediaQuery`max-width: 1024px`, // lg breakpoint max
	desktop: mediaQuery`min-width: 1280px`, // xl breakpoint
	largeDesktop: mediaQuery`min-width: 1536px`, // 2xl breakpoint
	print: mediaQuery`print`,

	// Touch-friendly media queries
	hover: mediaQuery`(hover: hover)`, // Only apply hover on devices that support it
	touch: mediaQuery`(pointer: coarse)`, // Touch devices
	fine: mediaQuery`(pointer: fine)` // Mouse/trackpad devices
}

export default media
