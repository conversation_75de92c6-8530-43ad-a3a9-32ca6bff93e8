import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { toast } from 'react-hot-toast';
import Colors from 'utils/colors';
import jmsApp from 'api/jms';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';

const AnalyticsContainer = styled.div`
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
`;

const Header = styled.div`
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: ${Colors.primary};
  margin-bottom: 0.5rem;
`;

const Subtitle = styled.p`
  color: #64748b;
  font-size: 1.1rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
`;

const StatIcon = styled.div`
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  background: ${props => props.color || Colors.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  
  svg {
    color: white;
    font-size: 1.5rem;
  }
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
`;

const StatChange = styled.div`
  color: ${props => props.positive ? '#16a34a' : '#dc2626'};
  font-size: 0.875rem;
  font-weight: 600;
  margin-top: 0.5rem;
`;

const ChartsGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const ChartCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
`;

const ChartTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 1.5rem;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid ${Colors.primary};
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const AdminAnalytics = () => {
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState({
    overview: {
      totalRevenue: 0,
      totalTransactions: 0,
      averageTransactionValue: 0,
      activeServices: 0
    },
    revenueData: [],
    serviceBreakdown: [],
    recentTransactions: []
  });

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      
      // Fetch pricing analytics
      const pricingResponse = await jmsApp.get('/pricing/admin/analytics');
      
      if (pricingResponse.data.success) {
        const data = pricingResponse.data.data;
        
        // Mock data for demonstration (replace with real data)
        setAnalytics({
          overview: {
            totalRevenue: data.totalRevenue || 12450,
            totalTransactions: data.totalTransactions || 156,
            averageTransactionValue: data.averageTransactionValue || 79.81,
            activeServices: data.activeServices || 4
          },
          revenueData: data.revenueData || [
            { month: 'Jan', revenue: 2400, transactions: 24 },
            { month: 'Feb', revenue: 1398, transactions: 18 },
            { month: 'Mar', revenue: 9800, transactions: 98 },
            { month: 'Apr', revenue: 3908, transactions: 39 },
            { month: 'May', revenue: 4800, transactions: 48 },
            { month: 'Jun', revenue: 3800, transactions: 38 }
          ],
          serviceBreakdown: data.serviceBreakdown || [
            { name: 'Expedited Review', value: 45, revenue: 5625 },
            { name: 'Open Access', value: 30, revenue: 750 },
            { name: 'Reprints', value: 15, revenue: 225 },
            { name: 'Additional Review', value: 10, revenue: 300 }
          ],
          recentTransactions: data.recentTransactions || []
        });
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('Failed to load analytics data');
      
      // Set mock data on error
      setAnalytics({
        overview: {
          totalRevenue: 12450,
          totalTransactions: 156,
          averageTransactionValue: 79.81,
          activeServices: 4
        },
        revenueData: [
          { month: 'Jan', revenue: 2400, transactions: 24 },
          { month: 'Feb', revenue: 1398, transactions: 18 },
          { month: 'Mar', revenue: 9800, transactions: 98 },
          { month: 'Apr', revenue: 3908, transactions: 39 },
          { month: 'May', revenue: 4800, transactions: 48 },
          { month: 'Jun', revenue: 3800, transactions: 38 }
        ],
        serviceBreakdown: [
          { name: 'Expedited Review', value: 45, revenue: 5625 },
          { name: 'Open Access', value: 30, revenue: 750 },
          { name: 'Reprints', value: 15, revenue: 225 },
          { name: 'Additional Review', value: 10, revenue: 300 }
        ],
        recentTransactions: []
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <AnalyticsContainer>
        <LoadingSpinner>
          <div className="spinner"></div>
        </LoadingSpinner>
      </AnalyticsContainer>
    );
  }

  return (
    <AnalyticsContainer>
      <Header>
        <Title>Analytics Dashboard</Title>
        <Subtitle>Comprehensive insights into your journal's financial performance</Subtitle>
      </Header>

      <StatsGrid>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <StatCard>
            <StatIcon color="#16a34a">
              💰
            </StatIcon>
            <StatValue>{formatCurrency(analytics.overview.totalRevenue)}</StatValue>
            <StatLabel>Total Revenue</StatLabel>
            <StatChange positive>+12.5% from last month</StatChange>
          </StatCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <StatCard>
            <StatIcon color="#3b82f6">
              📊
            </StatIcon>
            <StatValue>{analytics.overview.totalTransactions}</StatValue>
            <StatLabel>Total Transactions</StatLabel>
            <StatChange positive>+8.2% from last month</StatChange>
          </StatCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <StatCard>
            <StatIcon color="#f59e0b">
              💳
            </StatIcon>
            <StatValue>{formatCurrency(analytics.overview.averageTransactionValue)}</StatValue>
            <StatLabel>Average Transaction</StatLabel>
            <StatChange positive>+3.1% from last month</StatChange>
          </StatCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <StatCard>
            <StatIcon color="#8b5cf6">
              🎯
            </StatIcon>
            <StatValue>{analytics.overview.activeServices}</StatValue>
            <StatLabel>Active Services</StatLabel>
            <StatChange>No change</StatChange>
          </StatCard>
        </motion.div>
      </StatsGrid>

      <ChartsGrid>
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <ChartCard>
            <ChartTitle>Revenue Trends</ChartTitle>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analytics.revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [formatCurrency(value), 'Revenue']} />
                <Line 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke={Colors.primary} 
                  strokeWidth={3}
                  dot={{ fill: Colors.primary, strokeWidth: 2, r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </ChartCard>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <ChartCard>
            <ChartTitle>Service Revenue Distribution</ChartTitle>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analytics.serviceBreakdown}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {analytics.serviceBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value} transactions`, 'Count']} />
              </PieChart>
            </ResponsiveContainer>
          </ChartCard>
        </motion.div>
      </ChartsGrid>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <ChartCard>
          <ChartTitle>Monthly Transaction Volume</ChartTitle>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analytics.revenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="transactions" fill={Colors.secondary} />
            </BarChart>
          </ResponsiveContainer>
        </ChartCard>
      </motion.div>
    </AnalyticsContainer>
  );
};

export default AdminAnalytics;
