{"name": "jms-frontend", "version": "0.1.0", "private": true, "dependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@cloudinary/react": "^1.11.2", "@cloudinary/url-gen": "^1.10.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.2.0", "@preline/tooltip": "^2.0.2", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "autoprefixer": "^10.4.16", "axios": "^1.3.3", "cloudinary": "^1.36.2", "dotenv": "^16.0.3", "file-saver": "^2.0.5", "flutterwave-react-v3": "^1.3.0", "formik": "^2.2.9", "framer-motion": "^12.23.0", "jwt-decode": "^3.1.2", "mobx": "^6.8.0", "mobx-react-lite": "^3.4.0", "postcss": "^8.4.32", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.4.0", "react-icons": "^4.7.1", "react-paystack": "^6.0.0", "react-pdf": "^6.2.2", "react-player": "^2.12.0", "react-query": "^3.39.3", "react-router-dom": "^6.9.0", "react-scripts": "5.0.1", "react-spinners": "^0.13.8", "react-table": "^7.8.0", "recharts": "^3.1.0", "slugify": "^1.6.6", "styled-components": "^5.3.11", "styled-jsx": "^5.1.2", "styled-jsx-plugin-postcss": "^4.0.1", "swr": "^2.0.3", "twin.macro": "^3.4.0", "use-resize-observer": "^9.1.0", "web-vitals": "^2.1.0", "yarn": "^1.22.19", "yup": "^1.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"babel-plugin-macros": "^3.1.0", "babel-plugin-styled-components": "^2.1.4", "babel-plugin-tailwind-components": "^0.5.10", "tailwind.macro": "^0.5.10", "tailwindcss": "^3.3.7"}, "babelMacros": {"twin": {"preset": "styled-components"}}}