const express = require("express");
const categoryRoute = express.Router();
const Controller = require("../controllers");
const { adminRequired, notELearningRole } = require("../middlewares/auth.middleware");

categoryRoute.post("/", adminRequired,notELearningRole, Controller.CatController.createCategory);
categoryRoute.get("/", Controller.CatController.articleCategory);
categoryRoute.get("/all", adminRequired,notELearningRole, Controller.CatController.category);
categoryRoute.patch("/", adminRequired,notELearningRole, Controller.CatController.updateCategory);
categoryRoute.delete("/", adminRequired,notELearningRole, Controller.CatController.deleteCategory);
module.exports = {
  categoryRoute,
}