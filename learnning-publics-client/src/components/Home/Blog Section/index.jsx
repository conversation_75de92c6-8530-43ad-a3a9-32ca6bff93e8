// import { Section, Container } from 'globalStyles';

import { ArtHead } from "../Article Section/style";
import { Link } from "react-router-dom";
import React from "react";
import { Section } from "globalStyles";
import styled from "styled-components";
import tw from "twin.macro";
import { waec } from "utils/assets";

// import ArticleCards from '../Article Section/ArticleCards';

const BlogHeadCon = styled.div`
  /* // padding: 25px 0 40px 8.5%; */
  ${tw``}
`;

const StyledSection = styled(Section)`
  ${tw`px-6 sm:px-12 py-4 my-4`}
`;
const BlogSecCon = styled.div`
  display: flex;
  align-items: center;

  flex-wrap: wrap;
  /* // padding:0 8.5% 0 8.5%; */

  ${tw`sm:flex-nowrap flex-wrap sm:items-start sm:justify-between`}
`;

const BlogImage = styled.img`
  max-width: 100%;
  ${tw`h-full w-full object-cover aspect-video`}
`;

const BlogContent = styled.div`
  /* // flex: 1; */

  p {
    font-size: 18px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 20px;
  }

  ${tw`sm:px-4 flex flex-col items-start justify-between`}
`;

const BlogLink = styled(Link)`
  display: inline-block;
  /* // padding: 10px 20px; */
  background-color: #3498db;
  /* // color: #fff; */
  text-decoration: none;
  border-radius: 5px;
  /* // font-weight: bold; */
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #297fb8;
  }

  ${tw`px-4 py-2 rounded-md text-white font-bold text-xs sm:text-sm`}
`;

function BlogSection() {
  return (
    <StyledSection>
      <BlogHeadCon>
        <ArtHead>Other Programs</ArtHead>
      </BlogHeadCon>

      <BlogSecCon>
        <div className="flex items-center justify-center w-full h-40 sm:w-[55vw] sm:h-[400px] rounded-lg overflow-hidden">
          <BlogImage src={waec} alt="WAEC preparation" />
        </div>

        <BlogContent>
          <p className=" max-w-lg  text-left text-sm sm:text-base ">
            Prepare for success in WAEC and JAMB with our tailored courses.
            Covering a range of subjects, our comprehensive platform offers
            interactive lessons, practice tests, and personalized feedback to
            build confidence and competence for both exams.
          </p>

          <p>Prepare for WAEC and JAMB by utilizing our courses</p>
          <BlogLink to="/elearn" className="w-fit whitespace-nowrap">
            Visit&nbsp;our&nbsp;e-learning&nbsp;portal
          </BlogLink>
        </BlogContent>
      </BlogSecCon>
    </StyledSection>
  );
}

export default BlogSection;
