import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import LoadingSpinner from 'components/Common/LoadingSpinner';

const LegacyArticleRedirect = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    const redirectToSlugUrl = async () => {
      try {
        // Fetch article by ID to get the slug
        const response = await fetch(`/api/articles/${id}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.article && data.article.slug) {
            // Redirect to the new slug-based URL
            navigate(`/articles/${data.article.slug}`, { replace: true });
            return;
          }
        }
        
        // If article not found or no slug, redirect to articles list
        navigate('/articles', { replace: true });
        
      } catch (error) {
        console.error('Error redirecting legacy article URL:', error);
        navigate('/articles', { replace: true });
      }
    };

    if (id) {
      redirectToSlugUrl();
    } else {
      navigate('/articles', { replace: true });
    }
  }, [id, navigate]);

  return (
    <div className="flex justify-center items-center min-h-64">
      <div className="text-center">
        <LoadingSpinner />
        <p className="mt-4 text-gray-600">Redirecting to updated URL...</p>
      </div>
    </div>
  );
};

export default LegacyArticleRedirect;
