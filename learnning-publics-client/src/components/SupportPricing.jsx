import React from "react";
import styled from "styled-components";
// import { ArtHead, ArtHeadCon } from "./Home/Article Section/style";

// Styled components
const SupportSection = styled.section`
  padding: 24px 8.3%;
  border-top: 1px solid rgb(204, 204, 204);
  // border: 2px solid red;
  @media (max-width: 768px) {
    padding: 24px 5%;
  }
`;

const DonationContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 40px;
`;

const DonationOption = styled.div`
  text-align: center;
  padding: 20px;
  background-color: #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  margin: 10px;
  border-radius: 20px;
  // width: calc(33.333% - 20px);

  @media (max-width: 768px) {
    width: calc(100% - 20px);
  }

  &:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  }
`;

const Amount = styled.h3`
  font-size: 24px;
  color: #ffffff;
`;

const Description = styled.p`
  font-size: 18px;
  line-height: 1.5;
  color: #ffffff;
`;

const DonateButton = styled.button`
  background-color: #dc143c;
  color: #ffffff;
  font-size: 16px;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 20px;

  // &:hover {
  //   background-color: transparent;
  //   border: 2px solid #dc143c;
  //   color: #dc143c;
  //   font-weight: bold;
  //   font-size: 18px;
  // }
`;

const SupportPricing = () => {
  return (
    <SupportSection>
      <div style={{ textAlign: "center" }}>
        <h1
          style={{
            fontFamily: "Montserrat",
            fontStyle: "normal",
            fontWeight: "500",
            fontSize: "36px",
            // lineHeight: "50px",
            color: "#46555c",
          }}
        >
          Support Us
        </h1>
      </div>
      <DonationContainer>
        <DonationOption style={{ backgroundColor: "#4682B4" }}>
          <Amount>$10</Amount>
          <Description>Lorem ipsum dolor sit amet</Description>
          <DonateButton>Donate</DonateButton>
        </DonationOption>
        <DonationOption style={{ backgroundColor: "#87CEEB  " }}>
          <Amount>$25</Amount>
          <Description>Lorem ipsum dolor sit amet</Description>
          <DonateButton>Donate</DonateButton>
        </DonationOption>
        <DonationOption style={{ backgroundColor: "#1E90FF" }}>
          <Amount>$50</Amount>
          <Description>Lorem ipsum dolor sit amet</Description>
          <DonateButton>Donate</DonateButton>
        </DonationOption>
        <DonationOption style={{ backgroundColor: "#4169E1" }}>
          <Amount>$100</Amount>
          <Description>Lorem ipsum dolor sit amet</Description>
          <DonateButton>Donate</DonateButton>
        </DonationOption>
      </DonationContainer>
    </SupportSection>
  );
};

export default SupportPricing;
