const BookmarkModel = require('../models/bookmark.model');
const ArticleModel = require('../models/article.model');

exports.addBookmark = async (userId, userEmail, articleSlug) => {
  try {
    // First, find the article by slug
    const article = await ArticleModel.findBySlug(articleSlug);
    if (!article) {
      return { error: 'Article not found' };
    }

    // Check if bookmark already exists
    const existingBookmark = await BookmarkModel.findOne({ userId, articleSlug });
    if (existingBookmark) {
      return { error: 'Article already bookmarked' };
    }

    // Create new bookmark
    const bookmark = await BookmarkModel.create({
      userId,
      userEmail,
      articleSlug,
      articleId: article._id
    });

    return bookmark;
  } catch (error) {
    if (error.code === 11000) {
      return { error: 'Article already bookmarked' };
    }
    return { error: error.message };
  }
};

exports.removeBookmark = async (userId, articleSlug) => {
  try {
    const result = await BookmarkModel.findOneAndDelete({ userId, articleSlug });
    if (!result) {
      return { error: 'Bookmark not found' };
    }
    return { success: true };
  } catch (error) {
    return { error: error.message };
  }
};

exports.getUserBookmarks = async (userId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;
    
    const bookmarks = await BookmarkModel.find({ userId })
      .populate({
        path: 'articleId',
        model: 'Article',
        select: 'title description author slug createdAt category',
        populate: {
          path: 'category',
          model: 'Categorie',
          select: 'name slug'
        }
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await BookmarkModel.countDocuments({ userId });

    return {
      bookmarks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    return { error: error.message };
  }
};

exports.getBookmarkStatus = async (userId, articleSlug) => {
  try {
    const bookmark = await BookmarkModel.findOne({ userId, articleSlug });
    return { isBookmarked: !!bookmark };
  } catch (error) {
    return { error: error.message };
  }
};

exports.getBookmarkCount = async (articleSlug) => {
  try {
    const count = await BookmarkModel.countDocuments({ articleSlug });
    return { count };
  } catch (error) {
    return { error: error.message };
  }
};
