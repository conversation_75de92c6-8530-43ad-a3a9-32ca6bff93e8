.Example input,
.Example button {
  font: inherit;
}

.Example header {
  background-color: #323639;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
  padding: 20px;
  color: white;
}

.Example header h1 {
  font-size: inherit;
  margin: 0;
}

.overlay {
  background: white;
  z-index: 400;
  width: 100vw;
  height: 100vh;
  position: fixed;

  left: 0;
  right: 0;
  top: 0;
  /* background: rgba(49,49,49,0.8); */
}

.close-screen {
  width: 70px;
  border-radius: 3px;
  position: absolute;
  top: 10px;
  right: 20px;
  padding: 5px 7px;
  cursor: pointer;
  background: red;
  border: none;
  color: white;
}
.page-btn {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 45px;
  right: 15px;
  padding: 5px 7px;
}
.page-toggleBtn {
  width: 100px;
  height: 25px;
  cursor: pointer;
}
.page-prevBtn {
    width: 150px;
    height: 25px;
    cursor: pointer;
}

.btn-screen {
  background: white;
  outline: none;
  border: none;
  padding: 5px 7px;
  cursor: pointer;
  color: rgb(88, 147, 241);
  font-weight: 700;
}

.Example__container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 10px 0;
  padding: 10px;
}

.Example__container__load {
  margin-top: 1em;
  color: white;
}

.Example__container__document {
  /* width: 100vw; */
  /* background: white; */
  /* margin: 1em 0; */
  /* position: fixed; */
  /* transform: translate(0%, 0%); */
  height: 100vh;
  overflow-y: scroll;
}

.Example__container__document .react-pdf__Document {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.Example__container__document .react-pdf__Page {
  max-width: calc(100% - 2em);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
  margin: 1em;
}

.Example__container__document .react-pdf__Page canvas {
  max-width: 100%;
  height: auto !important;
}

.Example__container__document .react-pdf__message {
  padding: 20px;
  color: white;
}
