const characters = ["a","b", "c","d", "e", "f", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s","t","u", "v", "w", "x", "y","z"];
const isValidEmail = (email) => {
  let regex = new RegExp("[a-z0-9]+@[a-z]+.[a-z]{2,3}");
  return regex.test(email);
};
const OPTDigitGen = () =>{
  return Math.floor(Math.random() * 9000 + 1000);
};


const articleIdGenerator = (articleId) =>{
  if(!articleId) throw new Error("Invalid article ID data");
  const data =Array.from(JSON.stringify(articleId));
  data.pop();//remove trailing space
  data.shift();//remove trailing space
  let charCount = 0;
  let digitSum = 0;
  let digitCount =0;
  let char ="";
  // add all digits
  data.forEach(el =>{
    if(!isNaN(el)){
      digitSum += parseInt(el);
      digitCount++;
    }else{
      char += el;
      charCount++;
    }
  })
  // count the number of characters
  // divide the sum of digits by the number of characters and pick the whole number
  const firstInt =  Math.floor(digitSum/charCount);  
 let  secondInt = Math.floor( (charCount + digitCount) / firstInt);
 if(secondInt.toString().length <2) {
  if(charCount.toString().length >=3) charCount = charCount.toString().substring(0,2);
  secondInt = secondInt.toString().concat(charCount.toString());}
 let thirdInt = Math.floor((charCount + digitCount)/2);
  const firstChar = characters[pickCharacter(0, 25)];
  const secondChar = characters[pickCharacter(0, 25)];
  const ID = `lpj${firstInt}${firstChar}${secondInt}${secondChar}`;
  if(ID.toString().length <7) ID.toString().concat(thirdInt);
  return ID.toUpperCase();
}

const pickCharacter = (min, max) =>{
  const randomFraction = Math.random();
 const range = min + randomFraction * (max - min);
 return Math.floor(range);
}
module.exports = {
  isValidEmail,
  OPTDigitGen,
  articleIdGenerator,
};
