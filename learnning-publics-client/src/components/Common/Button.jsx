import React from 'react';
import { motion } from 'framer-motion';
import { buttonVariants } from 'design-system/components';

/**
 * Modern Button Component
 * Consistent, accessible button with multiple variants and animations
 */

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  leftIcon,
  rightIcon,
  className = '',
  animate = true,
  onClick,
  type = 'button',
  ...props
}) => {
  const baseStyles = buttonVariants.base;
  const sizeStyles = buttonVariants.sizes[size];
  const variantStyles = buttonVariants.variants[variant];

  const buttonClasses = `
    inline-flex items-center justify-center
    font-medium transition-all duration-150 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    ${variant === 'primary' ? 'bg-primary-500 hover:bg-primary-600 text-white' : ''}
    ${variant === 'secondary' ? 'bg-secondary-500 hover:bg-secondary-600 text-white' : ''}
    ${variant === 'outline' ? 'border border-primary-300 text-primary-600 hover:bg-primary-50' : ''}
    ${variant === 'ghost' ? 'text-primary-600 hover:bg-primary-50' : ''}
    ${variant === 'destructive' ? 'bg-error-500 hover:bg-error-600 text-white' : ''}
    ${size === 'xs' ? 'h-6 px-2 text-xs' : ''}
    ${size === 'sm' ? 'h-8 px-3 text-sm' : ''}
    ${size === 'md' ? 'h-10 px-4 text-sm' : ''}
    ${size === 'lg' ? 'h-12 px-6 text-base' : ''}
    ${size === 'xl' ? 'h-14 px-8 text-lg' : ''}
    rounded-md
    ${className}
  `.trim().replace(/\s+/g, ' ');

  const buttonContent = (
    <>
      {loading && (
        <motion.div
          className="mr-2"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        </motion.div>
      )}
      {leftIcon && !loading && <span className="mr-2">{leftIcon}</span>}
      <span>{children}</span>
      {rightIcon && <span className="ml-2">{rightIcon}</span>}
    </>
  );

  const handleClick = (e) => {
    if (disabled || loading) return;
    onClick?.(e);
  };

  if (animate) {
    return (
      <motion.button
        className={buttonClasses}
        disabled={disabled || loading}
        onClick={handleClick}
        type={type}
        whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
        whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
        transition={{ duration: 0.1 }}
        {...props}
      >
        {buttonContent}
      </motion.button>
    );
  }

  return (
    <button
      className={buttonClasses}
      disabled={disabled || loading}
      onClick={handleClick}
      type={type}
      {...props}
    >
      {buttonContent}
    </button>
  );
};

// Specialized button variants
export const PrimaryButton = (props) => <Button variant="primary" {...props} />;
export const SecondaryButton = (props) => <Button variant="secondary" {...props} />;
export const OutlineButton = (props) => <Button variant="outline" {...props} />;
export const GhostButton = (props) => <Button variant="ghost" {...props} />;
export const DestructiveButton = (props) => <Button variant="destructive" {...props} />;

// Icon button component
export const IconButton = ({
  icon,
  size = 'md',
  variant = 'ghost',
  className = '',
  ...props
}) => {
  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-14 h-14'
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={`${sizeClasses[size]} p-0 ${className}`}
      {...props}
    >
      {icon}
    </Button>
  );
};

// Floating Action Button
export const FloatingActionButton = ({
  icon,
  className = '',
  ...props
}) => (
  <motion.button
    className={`
      fixed bottom-6 right-6 w-14 h-14 
      bg-primary-500 hover:bg-primary-600 
      text-white rounded-full shadow-lg 
      flex items-center justify-center
      focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
      transition-colors duration-150
      ${className}
    `}
    whileHover={{ scale: 1.1 }}
    whileTap={{ scale: 0.9 }}
    initial={{ scale: 0 }}
    animate={{ scale: 1 }}
    transition={{ type: "spring", stiffness: 260, damping: 20 }}
    {...props}
  >
    {icon}
  </motion.button>
);

// Button group component
export const ButtonGroup = ({ 
  children, 
  className = '', 
  orientation = 'horizontal' 
}) => {
  const orientationClasses = orientation === 'horizontal' 
    ? 'flex-row' 
    : 'flex-col';

  return (
    <div className={`inline-flex ${orientationClasses} ${className}`}>
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return child;
        
        const isFirst = index === 0;
        const isLast = index === React.Children.count(children) - 1;
        
        let additionalClasses = '';
        if (orientation === 'horizontal') {
          additionalClasses = `
            ${!isFirst ? '-ml-px' : ''}
            ${!isFirst && !isLast ? 'rounded-none' : ''}
            ${isFirst && !isLast ? 'rounded-r-none' : ''}
            ${!isFirst && isLast ? 'rounded-l-none' : ''}
          `;
        } else {
          additionalClasses = `
            ${!isFirst ? '-mt-px' : ''}
            ${!isFirst && !isLast ? 'rounded-none' : ''}
            ${isFirst && !isLast ? 'rounded-b-none' : ''}
            ${!isFirst && isLast ? 'rounded-t-none' : ''}
          `;
        }

        return React.cloneElement(child, {
          className: `${child.props.className || ''} ${additionalClasses}`.trim()
        });
      })}
    </div>
  );
};

export default Button;
