import { Document, Page, pdfjs } from "react-pdf";
import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";

import { BiArrowBack } from "react-icons/bi";
import { IoReload } from "react-icons/io5";
import { useDebounce } from "hooks/use-debounce";
import useResizeObserver from "use-resize-observer";

// Import PDF viewer styles to fix text layer duplication
import "./pdf-viewer.css";

// Enhanced imports for modern UI
import { PDFErrorBoundary } from "components/ErrorBoundary/SpecializedErrorBoundaries";
import { usePerformanceMonitor } from "hooks/usePerformanceMonitor";
import { staggerContainer, staggerItem } from "design-system/animations";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import { BASE_URL } from "services";

// import { useScreen } from "hooks/use-window-size";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const ViewArticle = ({ data, close }) => {
  const { url, title, slug, updatedAt } = data;
  const { measureAsync, measureSync } = usePerformanceMonitor();

  const { ref, width = 1 } = useResizeObserver();
  const debouncedWidth = useDebounce(width, 300);

  // Enhanced state management
  const [loadError, setLoadError] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [numPages, setNumPages] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [scale, setScale] = useState(1);
  const [retryCount, setRetryCount] = useState(0);
  const [forceRefresh, setForceRefresh] = useState(0);
  const [windowSize, setWindowSize] = useState({ width: window.innerWidth, height: window.innerHeight });

  // Use PDF proxy endpoint if slug is available, otherwise fallback to direct URL
  // Add cache-busting parameter using updatedAt timestamp and force refresh to ensure latest version
  const pdfUrl = slug
    ? `${BASE_URL}/author/articles/pdf/${slug}?v=${updatedAt ? new Date(updatedAt).getTime() : Date.now()}&refresh=${forceRefresh}`
    : url;

  // Calculate optimal PDF width based on screen size and fullscreen mode
  const calculatePDFWidth = useCallback(() => {
    if (!debouncedWidth) return undefined;

    const screenWidth = windowSize.width;
    const containerPadding = isFullscreen ? (screenWidth >= 1024 ? 64 : screenWidth >= 768 ? 48 : 32) : 32;
    const availableWidth = debouncedWidth - containerPadding;

    if (isFullscreen) {
      // In fullscreen, use more of the available width on larger screens
      if (screenWidth >= 1440) return Math.min(availableWidth * 0.9, 1200);
      if (screenWidth >= 1024) return Math.min(availableWidth * 0.85, 1000);
      if (screenWidth >= 768) return Math.min(availableWidth * 0.8, 800);
      return availableWidth * 0.9;
    } else {
      // In modal mode, be more conservative
      return Math.min(availableWidth * 0.9, 800);
    }
  }, [debouncedWidth, isFullscreen, windowSize.width]);

  // Window resize listener for responsive PDF sizing
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  // Enhanced PDF event handlers with performance tracking
  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    measureSync('PDF Load Success', () => {
      setNumPages(numPages);
      setIsLoading(false);
      setLoadError("");
      setRetryCount(0);
    });
  }, [measureSync]);

  const onDocumentLoadFailed = useCallback((error) => {
    measureSync('PDF Load Failed', () => {
      setLoadError(error.message || 'Failed to load PDF');
      setNumPages(null);
      setIsLoading(false);
    });
  }, [measureSync]);

  // Enhanced navigation handlers
  const goToPreviousPage = useCallback(() => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  }, [currentPage]);

  const goToNextPage = useCallback(() => {
    if (currentPage < numPages) {
      setCurrentPage(prev => prev + 1);
    }
  }, [currentPage, numPages]);

  // Retry mechanism with cache busting
  const retryLoad = useCallback(async () => {
    const retryOperation = async () => {
      setRetryCount(prev => prev + 1);
      setForceRefresh(prev => prev + 1); // Force cache bust
      setLoadError("");
      setIsLoading(true);
      // Force re-render of Document component
      setCurrentPage(1);
    };

    await measureAsync('PDF Retry Load', retryOperation);
  }, [measureAsync]);

  // Fullscreen toggle
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
  }, []);

  // Zoom controls
  const zoomIn = useCallback(() => {
    setScale(prev => Math.min(prev + 0.25, 3));
  }, []);

  const zoomOut = useCallback(() => {
    setScale(prev => Math.max(prev - 0.25, 0.5));
  }, []);

  const resetZoom = useCallback(() => {
    setScale(1);
  }, []);

  // Enhanced error display with retry functionality
  if (loadError) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center space-y-6 w-full p-8 bg-white rounded-lg shadow-lg"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <DocumentIcon className="w-16 h-16 text-red-600 mx-auto mb-4" />
          <h3 className="font-bold text-xl text-red-600 mb-2">Error Loading PDF</h3>
          <p className="text-sm text-gray-600 max-w-md mb-4">
            {loadError}
          </p>
          {retryCount > 0 && (
            <p className="text-xs text-gray-500 mb-4">
              Retry attempt: {retryCount}
            </p>
          )}
        </motion.div>

        <div className="flex flex-col sm:flex-row gap-3">
          <motion.button
            className="flex items-center justify-center gap-2 px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 font-medium"
            onClick={retryLoad}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <IoReload className="w-4 h-4" />
            Retry Loading
          </motion.button>

          <motion.button
            className="flex items-center justify-center gap-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200 font-medium"
            onClick={close}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <XMarkIcon className="w-4 h-4" />
            Close Viewer
          </motion.button>
        </div>
      </motion.div>
    );
  }
  return (
    <PDFErrorBoundary>
      <motion.div
        className={`relative bg-white ${isFullscreen ? 'fixed inset-0 z-50 flex flex-col' : 'rounded-lg shadow-lg'}`}
        onContextMenu={(e) => e.preventDefault()} // Disable right-click context menu
        style={{ userSelect: 'none' }} // Disable text selection
        initial="hidden"
        animate="show"
        variants={staggerContainer}
      >
        {/* Enhanced Header with Controls */}
        <motion.div
          className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 lg:px-6 xl:px-8 py-3 flex items-center justify-between shadow-sm flex-shrink-0"
          variants={staggerItem}
        >
          <div className="flex items-center space-x-4">
            <motion.button
              onClick={close}
              className="flex items-center gap-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <BiArrowBack className="w-4 h-4" />
              <span className="hidden sm:inline">Back</span>
            </motion.button>

            <div className="hidden sm:block flex-1 min-w-0 mx-4">
              <h2 className="text-lg font-semibold text-blue-900 truncate max-w-xs lg:max-w-lg xl:max-w-2xl">
                {title}
              </h2>
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex items-center space-x-2">
            {/* Zoom Controls */}
            <div className="hidden md:flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
              <motion.button
                onClick={zoomOut}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                disabled={scale <= 0.5}
              >
                <span className="text-sm font-bold">-</span>
              </motion.button>

              <span className="px-2 text-sm font-medium text-gray-700 min-w-[3rem] text-center">
                {Math.round(scale * 100)}%
              </span>

              <motion.button
                onClick={zoomIn}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                disabled={scale >= 3}
              >
                <span className="text-sm font-bold">+</span>
              </motion.button>

              <motion.button
                onClick={resetZoom}
                className="px-2 py-1 text-xs text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Reset
              </motion.button>
            </div>

            {/* Fullscreen Toggle */}
            <motion.button
              onClick={toggleFullscreen}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {isFullscreen ? (
                <ArrowsPointingInIcon className="w-5 h-5" />
              ) : (
                <ArrowsPointingOutIcon className="w-5 h-5" />
              )}
            </motion.button>
          </div>
        </motion.div>

        {/* PDF Content Container - Enhanced with Floating Navigation */}
        <motion.div
          className={`relative flex-1 ${isFullscreen ? 'px-4 lg:px-6 xl:px-8 py-4' : 'p-4'}`}
          ref={ref}
          variants={staggerItem}
        >


          {/* Enhanced PDF Document with Scrollable Pages */}
          <motion.div
            className={`w-full mx-auto ${isFullscreen ? 'max-w-none' : 'max-w-4xl'} relative`}
            variants={staggerItem}
          >
            <Document
              file={pdfUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadFailed}
              loading={
                <motion.div
                  className="flex flex-col items-center justify-center py-16 bg-gray-50 rounded-lg"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-red-600 mb-4"></div>
                  <h3 className="text-lg font-semibold text-blue-900 mb-2">Loading PDF Document</h3>
                  <p className="text-gray-600 text-center max-w-md">
                    Please wait while we load your article. This may take a few moments.
                  </p>
                </motion.div>
              }
              error={
                <motion.div
                  className="flex flex-col items-center justify-center py-16 bg-red-50 rounded-lg"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <DocumentIcon className="w-16 h-16 text-red-600 mb-4" />
                  <h3 className="text-lg font-semibold text-red-600 mb-2">Failed to Load PDF</h3>
                  <p className="text-gray-600 text-center max-w-md mb-4">
                    There was an error loading the PDF document. Please try again.
                  </p>
                  <motion.button
                    onClick={retryLoad}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Retry
                  </motion.button>
                </motion.div>
              }
            >
              {/* Scrollable Container for Current Page */}
              <motion.div
                className={`pdf-scroll-container pdf-page-container bg-white shadow-lg rounded-lg overflow-auto ${
                  isFullscreen ? 'max-h-[calc(100vh-200px)]' : 'max-h-[80vh]'
                }`}
                style={{
                  transform: `scale(${scale})`,
                  transformOrigin: 'top center',
                  width: '100%'
                }}
                variants={staggerItem}
              >
                <div className="flex justify-center relative">
                  <div className="pdf-page-wrapper relative">
                    <Page
                      width={calculatePDFWidth()}
                      renderAnnotationLayer={true}
                      renderTextLayer={true}
                      pageNumber={currentPage}
                      error={
                        <motion.div
                          className="flex flex-col items-center justify-center py-12 bg-gray-50"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <DocumentIcon className="w-12 h-12 text-gray-400 mb-2" />
                          <p className="text-gray-600 font-medium">Page not found</p>
                        </motion.div>
                      }
                      loading={
                        <motion.div
                          className="flex flex-col items-center justify-center py-12 bg-gray-50"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <div className="animate-pulse bg-gray-300 rounded w-full h-96 mb-2"></div>
                          <p className="text-gray-600">Loading page...</p>
                        </motion.div>
                      }
                    />
                  </div>
                </div>
              </motion.div>

              {/* Subtle Scroll Hint */}
              <motion.div
                className="absolute top-4 right-4 z-10"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1, duration: 0.5 }}
              >
                <div className="bg-blue-50/90 backdrop-blur-sm text-blue-700 text-xs px-3 py-2 rounded-full border border-blue-200 shadow-sm">
                  <span className="scroll-hint mr-1">↕</span>
                  <span className="hidden sm:inline">Scroll to read full page</span>
                  <span className="sm:hidden">Scroll page</span>
                </div>
              </motion.div>
            </Document>
          </motion.div>

          {/* Navigation Controls - Fixed Bottom Center */}
          <motion.div
            className="fixed bottom-16 sm:bottom-8 left-0 right-0 z-50 flex justify-center pointer-events-none px-4"
            variants={staggerItem}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="pointer-events-auto bg-white/95 backdrop-blur-sm border border-gray-200 rounded-xl shadow-lg px-3 py-2 sm:px-4 sm:py-3 max-w-sm w-full sm:w-auto">
              <div className="flex items-center justify-center space-x-2 sm:space-x-4">
                {/* Previous Button */}
                <motion.button
                  onClick={goToPreviousPage}
                  disabled={currentPage <= 1}
                  className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200 font-medium text-xs sm:text-sm flex-shrink-0"
                  whileHover={{ scale: currentPage > 1 ? 1.05 : 1 }}
                  whileTap={{ scale: currentPage > 1 ? 0.95 : 1 }}
                >
                  <ChevronLeftIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">Previous</span>
                  <span className="sm:hidden text-xs">Prev</span>
                </motion.button>

                {/* Page Info */}
                <div className="text-center px-1 sm:px-3 flex-1 sm:flex-initial">
                  <p className="text-xs sm:text-sm font-medium text-gray-900">
                    {currentPage} / {numPages || '...'}
                  </p>
                  {numPages && (
                    <div className="w-16 sm:w-20 bg-gray-200 rounded-full h-1 mt-1 mx-auto">
                      <div
                        className="bg-red-600 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${(currentPage / numPages) * 100}%` }}
                      ></div>
                    </div>
                  )}
                </div>

                {/* Next Button */}
                <motion.button
                  onClick={goToNextPage}
                  disabled={currentPage >= numPages}
                  className="flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200 font-medium text-xs sm:text-sm flex-shrink-0"
                  whileHover={{ scale: currentPage < numPages ? 1.05 : 1 }}
                  whileTap={{ scale: currentPage < numPages ? 0.95 : 1 }}
                >
                  <span className="hidden sm:inline">Next</span>
                  <span className="sm:hidden text-xs">Next</span>
                  <ChevronRightIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                </motion.button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </motion.div>
    </PDFErrorBoundary>
  );
};

export default ViewArticle;
