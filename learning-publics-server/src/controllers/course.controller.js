const { default: mongoose } = require("mongoose");
const { CONSTANTS } = require("../config");
const logger = require("../logger");
const { requiredAdminRole } = require("../middlewares/auth.middleware");

const { 
  getCourse, 
  updateCourse, 
  getCourseById, 
  deleteCourseById,
  getCourseNotInPlan,
  addCourses,
  getUserPlanById,
  getUserPlans,
  updateUserPlanView} = require("../services");
const { META, ERROR_FIELD } = require("../utils/actions");
const { APIError } = require("../utils/apiError");
const { cloudinary, accessPath } = require("../utils/cloudinary");
const responsBuilder = require("../utils/responsBuilder");

exports.registerCourse = async (req, res, next) => {
  try { 
    if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !==  CONSTANTS.ACCOUNT_TYPE[6]) return next(APIError.unauthorized())
    const { type, title, category } = req.body;
    let resourceUrl;
    if (!type) return next(APIError.badRequest("Course type is required"));
    if (!title) return next(APIError.badRequest("Course title is required"));
    if (!category) return next(APIError.badRequest("Course category is required"));
    if (!CONSTANTS.COURSE_TYPE.includes(type.toLowerCase())) return next(APIError.badRequest("Invalid course type"));
    if (!CONSTANTS.COURSE_CATEGORY.includes(category.toLowerCase())) return next(APIError.badRequest("Invalid category type")); 
    if (category === CONSTANTS.COURSE_CATEGORY[0]) {
      if (!req.body.resourceUrl) return next(APIError.badRequest(`${category} link is required`));
      resourceUrl = req.body.resourceUrl;
    }
    const details = {admin:req.userId, type, title, category, resourceUrl };
    if (category === CONSTANTS.COURSE_CATEGORY[1]) {
      if (!req.body.fileData) return next(APIError.badRequest(`${category} file is required`));
      //verify file extention
      if(!CONSTANTS.ARTICLE_FORMAT.includes(req.body.format)) return next(APIError.badRequest("Invalid file format"));
      // upload file to cloudinary
      const upload = await cloudinary.uploader.upload(req.body.fileData, {
        upload_preset:accessPath.preset(),
        folder: accessPath.folder(),
      });
      logger.info("Uploaded course successfully", {meta: META.ARTICLE_SERVICE});
      details.resourceUrl = upload.secure_url;
      details.resourceID = upload.public_id;
    }
    const course = await addCourses(details);
    if (!course) return next(APIError.customError(ERROR_FIELD.NOT_FOUND,404));
    if (course.error) return next(APIError.customError(course.error,400));
    logger.info("Course created successfully",{meta:META.COURSE_SERVICE})
    res.status(201).json({success: true, msg:"Course created successfully"});
  } catch (error) {
    next(error);
  }
};

exports.updateCourse = async (req, res, next) => {
  try {
    if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !==  CONSTANTS.ACCOUNT_TYPE[6]) return next(APIError.unauthorized())
    const { id, type, title, category } = req.body;
    let resourceUrl;
    
    if (!id) return next(APIError.badRequest("Course ID is required"));
    if (!type) return next(APIError.badRequest("Course type is required"));
    if (!title) return next(APIError.badRequest("Course title is required"));
    if (!category) return next(APIError.badRequest("Course category is required")); 
    if (!CONSTANTS.COURSE_TYPE.includes(type.toLowerCase())) return next(APIError.badRequest("Invalid course type"));
    if (!CONSTANTS.COURSE_CATEGORY.includes(category.toLowerCase())) return next(APIError.badRequest("Invalid category type")); 
    if (category === CONSTANTS.COURSE_CATEGORY[0]) {
      if (!req.body.resourceUrl) return next(APIError.badRequest(`${category} link is required`));
      resourceUrl = req.body.resourceUrl;
    }
    const details = { admin:req.userId, type, title, category, resourceUrl};
    if (category === CONSTANTS.COURSE_CATEGORY[1]) {
      if (!req.body.fileData) return next(APIError.badRequest(`${category} file is required`));
      //verify file extension
      if(!CONSTANTS.ARTICLE_FORMAT.includes(req.body.fileData)) return next(APIError.badRequest("Invalid file format"));
      // upload file to cloudinary
      const upload = await cloudinary.uploader.upload(req.body.fileData, {
        upload_preset:accessPath.preset(),
        folder: accessPath.folder(),
});
      logger.info("Update course successfully", {meta: META.ARTICLE_SERVICE});
      details.resourceUrl = upload.secure_url;
      details.resourceID = upload.public_id;
    }
    const course = await updateCourse(id, details);
    logger.info("Course updated successfully", {meta:META.COURSE_SERVICE});

    if (!course) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (course.error) return next(APIError.badRequest(course.error));
    res.status(201).json({success: true, msg:"Course updated successfully"});
  } catch (error) {
    next(error);
  }
};

exports.course = async (req, res, next) => {
  try{ 
    if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !==  CONSTANTS.ACCOUNT_TYPE[6]) return next(APIError.unauthorized())
    const courses = await getCourse();
    if(!courses || courses.length === 0) return next(APIError.customError("No course found", 404));
    if (courses.error) return next(APIError.badRequest(courses.error));
    // build response data structure
    const data = courses.map((cur) =>{
      return responsBuilder.buildCourse(cur.toObject())
    })
    const response = responsBuilder.commonReponse("Found", data, "course");
    res.status(200).json(response);
  }catch(error){
    return next(error);
  }
}

exports.courseById = async (req, res, next) => {
  try{
    const { courseId } = req.query;
    if(!courseId) return next(APIError.badRequest('Course Id is required'));
    let courses;
    let data;
    if(req.userType === CONSTANTS.ACCOUNT_TYPE[2]){
      courses = await  getCourseById(courseId);
     if(!courses ) return next(APIError.customError("No course found", 404));
     if (courses.error) return next(APIError.badRequest(courses.error));
      data = responsBuilder.buildCourse(courses.toObject())
    }else if(req.userType ===  CONSTANTS.ACCOUNT_TYPE[0]){
      const {modules, planId} = req.query;
      if(!modules ) return next(APIError.badRequest("Course module is required"));
      if(!planId ) return next(APIError.badRequest("Course ID is required"));
      const plan = await getUserPlans(req.userId)
      if(!plan || plan.length === 0) return next(APIError.customError(`Course Not Found`));
      if(plan.error) return next(APIError.customError(plan.error,400));
      // build response data structure
      let findPlan = plan.find(x => x.plan._id.toString() === planId.toString());
      if(!findPlan) return next(APIError.badRequest("plan does not exit"));
      // findPlan.forEach((cur) =>{
        //  if(!findCourse){
        const   findCourse =  findPlan.planInfo[0].courses.find(x => x.id.toString() === courseId.toString() && parseInt(x.module) === parseInt(modules) && findPlan.plan.toString() === planId.toString());
        //  }
        //  if(findCourse) return;
      // })
      if(!findCourse ) return next(APIError.badRequest("Course does not exist"));
      if(parseInt(findCourse.module) > findPlan.planInfo[0].view[0].module &&  !findPlan.planInfo[0].view[0].completed ) return next(APIError.unauthorized("This module have not been activated"));
      data = findCourse;
    }
     
    const response = responsBuilder.commonReponse("Found", data, "course");
    res.status(200).json(response);
  }catch(error){
    return next(error);
  } 
}

exports.courseDelete = async (req, res, next) => {
  try {  
    if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !==  CONSTANTS.ACCOUNT_TYPE[6]) return next(APIError.unauthorized())
    const { courseId } = req.query;
    if(!courseId) return next(APIError.badRequest('Course Id is required'));
    const course = await deleteCourseById(courseId);
    if (!course) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (course.error) return next(APIError.customError(course.error, 400));
    logger.info("Course deleted successfully", {meta:META.COURSE_SERVICE});
    res
      .status(200)
      .json({success: true, msg: "Course deleted successfully"});

  } catch (error) {
    return next(error);
  }
};
exports.courseNotInPlan = async (req, res, next) => {
  try {  
    if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error && req.userType !==  CONSTANTS.ACCOUNT_TYPE[6]) return next(APIError.unauthorized())
    const course = await getCourseNotInPlan();
    if (!course) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
    if (course.error) return next(APIError.customError(course.error, 400));
    logger.info("Course not in plan retrieved", {meta:META.COURSE_SERVICE});
    const data = course.map((cur) =>{
      return responsBuilder.buildCourse(cur.toObject())
    })
    const response = responsBuilder.commonReponse("Found", data, "course");
    res.status(200).json(response);

  } catch (error) {
    return next(error);
  }
};

exports.completeCourse = async (req, res, next) => {
  try{
    const { courseId, planId, modules } = req.body;
    if(!courseId) return next(APIError.badRequest('Course Id is required'));
    if(!planId) return next(APIError.badRequest('Plan Id is required'));
    if(!modules) return next(APIError.badRequest('Course Module is required'));
    let plan = await  getUserPlanById(planId,req.userId);
    const userplanId = plan._id;
    // const plan = await  getPlanById(userPlan.plan);
    if(!plan || plan.length == 0) return next(APIError.customError("No course found", 404));
    if (plan.error) return next(APIError.badRequest(plan.error));
    plan = plan.planInfo[0];
    const cmodule =  plan.view[0];
    const oldModule = parseInt(cmodule.module);
   
    if(parseInt(modules) >  parseInt(cmodule.module)) return next(APIError.badRequest("Module have not been activated"))
    // build response data structure
    const course = plan.courses.find(el => el.id.toString() === courseId.toString() && parseInt(el.module) === parseInt(modules));
    const otherCourse = plan.courses.filter(el => el.id.toString() !== courseId.toString() || parseInt(el.module) !== parseInt(modules));
    if(!course) return next(APIError.customError("Course was not found", 404));
    if(!course.completed || course.completed === false){
      course.completed = true,
      cmodule.viewed = cmodule.viewed + 1;
    }
    otherCourse.push(course);
    if(cmodule.count === cmodule.viewed && cmodule.viewed !==0) {
      cmodule.module = parseInt(cmodule.module) +1;
      cmodule.completed = true
    } 
    if(cmodule.completed === true){
      const nextModule = plan.courses.filter(el => el.id !== courseId && parseInt(el.module) === parseInt(cmodule.module));
      cmodule.count = nextModule?.length ? nextModule?.length : 1; 
      cmodule.viewed = 0;
      cmodule.completed = false;
    }
    // update the course view aspect of the course
    if(parseInt(modules) === parseInt(oldModule)){ 
      const updateView = await updateUserPlanView(userplanId,cmodule, otherCourse)
      if(!updateView) return next(APIError.customError("Module not found", 404));
      if (updateView.error) return next(APIError.badRequest(updateView.error));
    }
    res.status(200).json({success: true, msg: "Course module completed"});
  }catch(error){
    return next(error); 
  } 
}