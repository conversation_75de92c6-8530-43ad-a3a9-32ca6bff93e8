import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  DocumentTextIcon,
  TagIcon
} from '@heroicons/react/24/outline';

// Simple button variants for animations
const buttonVariants = {
  rest: { scale: 1 },
  hover: { scale: 1.02 },
  tap: { scale: 0.98 }
};

const ArticleCard = ({ article, index = 0 }) => {
  if (!article) return null;

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };



  // Get category color
  const getCategoryColor = (categoryName) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-purple-100 text-purple-800',
      'bg-orange-100 text-orange-800',
      'bg-pink-100 text-pink-800',
      'bg-indigo-100 text-indigo-800'
    ];
    const hash = categoryName?.split('').reduce((a, b) => a + b.charCodeAt(0), 0) || 0;
    return colors[hash % colors.length];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      whileHover={{ y: -2, transition: { duration: 0.2 } }}
      className="bg-white rounded-lg shadow-md hover:shadow-lg overflow-hidden transition-all duration-300 h-full flex flex-col border border-gray-200"
    >
      {/* Compact Header */}
      <div className="bg-gradient-to-r from-red-600 to-red-700 p-4 text-white">
        <div className="flex items-center justify-between">
          {/* Category Badge */}
          {article.category && (
            <div className="inline-flex items-center px-2 py-1 bg-white/20 rounded text-xs font-medium">
              <TagIcon className="w-3 h-3 mr-1" />
              {article.category.name.replace('learning publics journal of ', '').toUpperCase()}
            </div>
          )}

          {/* Document Icon */}
          <DocumentTextIcon className="w-5 h-5 text-white/80" />
        </div>
      </div>

      <div className="p-4 sm:p-6 flex flex-col h-full">
        {/* Category with mobile-optimized styling */}
        {article.category && (
          <div className="mb-3">
            <Link
              to={`/articles/category/${article.category.slug}`}
              className="inline-block bg-blue-100 text-blue-800 text-xs sm:text-sm font-medium px-2 py-1 rounded-full hover:bg-blue-200 transition-colors touch-manipulation"
            >
              {article.category.name}
            </Link>
          </div>
        )}

        {/* Title - No line clamp to show full title */}
        <motion.h3
          className="text-lg sm:text-xl font-bold text-blue-900 mb-3 leading-tight"
          variants={buttonVariants}
          initial="rest"
          whileHover="hover"
        >
          <Link
            to={`/articles/${article.slug}`}
            className="hover:text-red-600 transition-colors duration-200"
          >
            {article.title}
          </Link>
        </motion.h3>

        {/* Description - Show full text without clamp */}
        {(article.metaDescription || article.description) && (
          <p className="text-gray-600 text-sm sm:text-base mb-4 leading-relaxed flex-grow">
            {article.metaDescription || article.description}
          </p>
        )}

        {/* Meta Info - Better spacing and full author display */}
        <div className="space-y-2 mb-4 pt-3 border-t border-gray-100">
          {/* Author - Full display without truncation */}
          {article.author && (
            <div className="text-sm text-gray-700">
              <span className="font-medium">By:</span> {article.author}
            </div>
          )}

          {/* Date and Views */}
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>{formatDate(article.createdAt)}</span>
            {article.viewCount !== undefined && (
              <span>{article.viewCount} views</span>
            )}
          </div>
        </div>

        {/* Keywords with mobile-friendly wrapping */}
        {article.keywords && article.keywords.length > 0 && (
          <div className="mt-auto pt-3 flex flex-wrap gap-1">
            {article.keywords.slice(0, 3).map((keyword, index) => (
              <span
                key={index}
                className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded touch-manipulation"
              >
                {keyword}
              </span>
            ))}
            {article.keywords.length > 3 && (
              <span className="text-xs text-gray-500">
                +{article.keywords.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Mobile-friendly read more button */}
        <div className="mt-4 pt-3 border-t border-gray-100">
          <Link
            to={`/articles/${article.slug}`}
            className="inline-flex items-center justify-center w-full py-2 px-4 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 active:bg-red-800 transition-colors duration-200 touch-manipulation min-h-[44px]"
          >
            Read Full Article
          </Link>
        </div>
      </div>
    </motion.div>
  );
};

export default ArticleCard;
