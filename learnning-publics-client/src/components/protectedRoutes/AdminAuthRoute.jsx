import { LOCAL_STORAGE } from "api/LOCALSTORAGE";
import { Navigate, useLocation } from "react-router-dom";

export function AdminProtectedRoute({ children }) {
  let location = useLocation();
  const user = LOCAL_STORAGE.user();
  // const user = JSON.parse(localStorage.getItem("isUserDetails"));
  if (user) {
    if (user.type === "user") {
      return <Navigate to="/unauthorized" state={{ from: location }} replace />;
    }

    return children;
  }
  return <Navigate to="/login" state={{ from: location }} replace />;
}
