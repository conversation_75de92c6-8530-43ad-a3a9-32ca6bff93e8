const { APIError } = require("../utils/apiError");
const jwt = require("jsonwebtoken");
const { getUserPlan, getUserById } = require("../services");
const { ERROR_FIELD } = require("../utils/actions");
const config = require("../config/env"); 
const { CONSTANTS } = require("../config");

const adminRequired = async (req, res, next) => {
  try {
    let token = req.cookies?.jwt;
    if (!token) token = req.headers?.authorization?.split(" ")[1];
    if (!token) token = req.headers?.cookie?.split("=")[1];
    if (!token) token = req.body.token;
    if (!token) return next(APIError.unauthenticated());
    const payload = jwt.verify(token, config.TOKEN_SECRETE);
    if (payload.type.toLowerCase() === CONSTANTS.ACCOUNT_TYPE[0])
      return next(APIError.unauthorized());
    req.userId = payload.id;
    req.userType = payload.type; 
    next();
  } catch (error) {
    if (error.message === ERROR_FIELD.JWT_EXPIRED) next(APIError.unauthenticated());
    next(error);
  }
};
const userRequired = async (req, res, next) => {
  try {
    let token = req.cookies?.jwt;
    if (!token) token = req.headers?.authorization?.split(" ")[1];
    if (!token) token = req.headers?.cookie?.split("=")[1];
    if (!token) token = req.body.refreshToken;
    if (!token) return next(APIError.unauthenticated());
    const payload = jwt.verify(token, config.TOKEN_SECRETE);
    const isUser = await getUserById(payload.id);
    if (!isUser) return next(APIError.customError(`user does not exist`, 404));
    if (isUser.error) return next(APIError.customError(isUser.error), 400);
    req.userId = payload.id;
    req.userType = payload.type;
    req.username = isUser.username;
    req.email = isUser.email;
    next();
  } catch (error) {
    if (error.message === ERROR_FIELD.JWT_EXPIRED) 
      next(APIError.unauthenticated());
    else next(error);
  }
};
const notELearningRole  = async (req, res, next) => {
  try{
  if(req.userType.toLowerCase() == CONSTANTS.ACCOUNT_TYPE[6]) return next(APIError.unauthorized());
  next();
  }catch(error){
    next(error)
  }
} 
const requiredAdminRole = (role, requiredRole) => {
  try{
  if(role.toLowerCase() !== requiredRole.toLowerCase()) throw new Error("You don't have required permission", 403);
  }catch(error){
    return {error};
  }
} 
module.exports = {
  adminRequired,
  userRequired, 
  requiredAdminRole,
  notELearningRole,
};
