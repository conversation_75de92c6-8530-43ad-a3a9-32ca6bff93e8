import { ADMIN_ROLES } from "utils/admin-roles";
import { ARTICLE_ENDPOINT, DOWNLOAD_ENDPOINT } from "api/ACTION";
import {
  AllArticles,
  ArticleCont
} from "components/Admin/Articles/Articles.style";
import ArticleAssign, { handleAssign } from "../Dashboard/article-asign";
import { useEffect, useMemo, useState, useCallback, Suspense } from "react";

import ArticlesTable from "./articles-table";

import { Cloudinary } from "@cloudinary/url-gen";
import { AdminDashboardSkeleton } from "components/Common/Skeleton";
import { FcDeleteDatabase } from "react-icons/fc";
import { LOCAL_STORAGE } from "api/LOCALSTORAGE";
import Search from "../sharedComponents/Search";
import UploadFileComp from "./UploadFileComp";
import authStore from "mobx/AuthStore";
import axios from "axios";
import { heading } from "../sharedComponents/articleData";
import jmsApp from "api/jms";
import { observer } from "mobx-react-lite";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";

// Enhanced imports for optimization
import { AdminErrorBoundary, TableErrorBoundary } from "components/ErrorBoundary/SpecializedErrorBoundaries";
import { useOptimizedFetch } from "hooks/useOptimizedFetch";
import { usePerformanceMonitor } from "hooks/usePerformanceMonitor";
import { motion } from "framer-motion";
import { fadeIn, staggerContainer } from "design-system/animations";

// import Table from "../sharedComponents/Table-v2";

// Error boundary wrapper for Router context issues
const ArticlesCompWrapper = () => {
  try {
    return <ArticlesComp />;
  } catch (error) {
    console.error('ArticlesComp error:', error);
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <h2 className="text-xl font-bold text-red-600 mb-4">Error Loading Articles</h2>
        <p className="text-gray-600 mb-4">There was an error loading the articles page.</p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Reload Page
        </button>
      </div>
    );
  }
};

const ArticlesComp = observer(() => {
  const activeStatus = [
    { status: "all", access: ["super", "editor", "formatter", "sub"] },
    { status: "approved", access: ["super"] },
    { status: "review", access: ["super"] },
    { status: "published", access: ["super"] },
    { status: "rejected", access: ["super"] },
    { status: "pending", access: ["super"] },
    { status: "downloaded", access: ["super", "editor", "formatter", "sub "] },
    { status: "formatted", access: ["super", "editor", "formatter", "sub "] },
    { status: "reviewed", access: ["super", "editor", "formatter", "sub"] },
    { status: "assigned", access: ["super", "editor", "formatter", "sub"] }
  ];
  const isAssignModalOpen = authStore.getOpenModalState();

  const currentUser = LOCAL_STORAGE.user();
  const userRole = currentUser?.type ?? "";
  const columns = useMemo(() => heading, []);
  const [active, setActive] = useState("all");
  const [value, setValue] = useState("");
  const [isDownloaded, setIsDownloaded] = useState(null);
  const [downloadList, setDownloadList] = useState();
  const [isDeletingArticle, setIsDeletingArticle] = useState(null);
  const [actionLoading, setActionLoading] = useState({});

  // Performance monitoring
  const { measureAsync, measureSync } = usePerformanceMonitor('ArticlesComp', {
    trackRenders: true,
    trackMemory: true,
    onPerformanceData: (data) => {
      if (data.type === 'async_operation' && data.duration > 1000) {
        console.warn(`Slow operation detected: ${data.operationName} took ${data.duration}ms`);
      }
    }
  });

  // Optimized data fetching with caching and error handling
  const {
    data: articles = [],
    isLoading: isFetchingArticles,
    error: fetchError,
    retry: retryFetch,
    refresh: refreshArticles
  } = useOptimizedFetch(ARTICLE_ENDPOINT.GET_ARTICLE(), {
    transform: (data) => data.article || [],
    enableCache: true,
    cacheTime: 2 * 60 * 1000, // 2 minutes cache for admin data
    onError: (error) => {
      console.error('Articles fetch error:', error);
      if (error?.response?.status === 401) {
        try {
          navigate("/login");
        } catch (navError) {
          console.error('Navigation error:', navError);
          window.location.href = "/login";
        }
      }
    }
  });

  // Ensure articles is always an array to prevent null/undefined errors
  const safeArticles = Array.isArray(articles) ? articles : [];
  // useEffect(() => {
  //   const { data } = jmsApp.get(DOWNLOAD_ENDPOINT.DOWNLOADED_ARTICLE());
  //   console.log(data);
  //   setIsDownloaded();
  // }, []);

  // Safe navigation with error handling
  const navigate = useNavigate();

  // Safe navigation function to prevent Router context errors
  const safeNavigate = useCallback((path) => {
    try {
      navigate(path);
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to window.location if navigate fails
      window.location.href = path;
    }
  }, [navigate]);

  // Optimized filtering with performance tracking
  const filterArticles = useCallback((status) => {
    return measureSync('Filter Articles', () => {
      if (status === "all") {
        return safeArticles;
      }
      if (status === "downloaded") {
        // Handle downloaded articles logic
        return safeArticles.filter(article => downloadList?.includes(article.articleId));
      }
      return safeArticles.filter((article) => article.status === status);
    });
  }, [safeArticles, downloadList, measureSync]);

  // Memoized filtered articles
  const filteredArticles = useMemo(() => {
    try {
      const result = filterArticles(active);
      return Array.isArray(result) ? result : [];
    } catch (error) {
      console.error('Error filtering articles:', error);
      return [];
    }
  }, [filterArticles, active]);

  // Optimized search functionality
  const searchFilteredArticles = useMemo(() => {
    try {
      if (!value.trim()) return filteredArticles;

      return measureSync('Search Articles', () => {
        const searchTerm = value.toLowerCase();
        return filteredArticles.filter(article =>
          article?.title?.toLowerCase().includes(searchTerm) ||
          article?.author?.toLowerCase().includes(searchTerm) ||
          article?.category?.toLowerCase().includes(searchTerm) ||
          article?.description?.toLowerCase().includes(searchTerm)
        );
      });
    } catch (error) {
      console.error('Error searching articles:', error);
      return [];
    }
  }, [filteredArticles, value, measureSync]);

  // Fetch download list separately
  useEffect(() => {
    const fetchDownloadList = async () => {
      try {
        const { data: downloads } = await jmsApp.get(
          DOWNLOAD_ENDPOINT.DOWNLOADED_ARTICLE()
        );
        setDownloadList(downloads?.downloadedArticles || []);
      } catch (error) {
        console.error('Failed to fetch download list:', error);
      }
    };

    // Use safeArticles to prevent null/undefined errors
    if (safeArticles && safeArticles.length > 0) {
      fetchDownloadList();
    }
  }, [safeArticles.length]);

  // Safe Cloudinary configuration with error handling
  const cld = useMemo(() => {
    try {
      return new Cloudinary({
        cloud: {
          cloudName: process.env.REACT_APP_CLOUDINARY_NAME,
          apiKey: process.env.REACT_APP_CLOUDINARY_API_KEY,
          apiSecret: process.env.REACT_APP_CLOUDINARY_API_SECRET
        }
      });
    } catch (error) {
      console.error('Cloudinary configuration error:', error);
      return null;
    }
  }, []);
  // Optimized download function with performance tracking and loading states
  const downloadFile = useCallback(async (articleURL, title, articleId) => {
    const downloadOperation = async () => {
      setActionLoading(prev => ({ ...prev, [articleId]: 'downloading' }));

      try {
        // Track download in backend
        await jmsApp.post(DOWNLOAD_ENDPOINT.DOWNLOADED_ARTICLE(), {
          articleId: articleId
        });

        // Handle Cloudinary image download with safety checks
        if (!cld) {
          throw new Error('Cloudinary not configured properly');
        }

        const myImage = cld.image(articleURL);
        const image = await axios({
          url: myImage.publicID,
          method: "GET",
          responseType: "blob"
        });

        // Create and trigger download
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(image.data);
        link.setAttribute("download", `${title || 'article'}.pdf`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(link.href);

        toast.success("Article downloaded successfully");

        // Update download list
        setDownloadList(prev => [...(prev || []), articleId]);

      } catch (err) {
        console.error("Download error:", err);
        if (err.response?.status === 401) {
          navigate("/login");
        } else {
          toast.error(err.response?.data?.error || "Download failed");
        }
      } finally {
        setActionLoading(prev => ({ ...prev, [articleId]: null }));
      }
    };

    await measureAsync('Article Download', downloadOperation);
  }, [measureAsync, cld, navigate]);
  // Optimized data update handler
  const handleUpdateData = useCallback((updatedData) => {
    measureSync('Update Articles Data', () => {
      // Trigger a refresh of the optimized fetch
      refreshArticles();
    });
  }, [refreshArticles, measureSync]);

  async function handleDownload(article) {
    const { articleUrl, title, articleId } = article;
    if (LOCAL_STORAGE.articleId() !== articleId) {
      toast.error("Select the article to be downloaded first");
      return;
    }
    try {
      await toast.promise(downloadFile(articleUrl, title, articleId), {
        loading: "Downloading...",
        success: "Download successful successful",
        error: "Download failed"
      });
      setIsDownloaded(articleId);
    } catch (err) {
      setIsDownloaded(null);
    }
  }
  function handleView(article) {
    navigate(`/admin/articles/${article.articleId}`);
  }
  async function handleDelete(article) {
    try {
      await toast.promise(
        jmsApp.delete(ARTICLE_ENDPOINT.DELETE_ARTICLE(), {
          params: {
            articleId: article.articleId
          }
        }),
        {
          loading: `Deleting ${article.title}...`,
          error: `${article.title} deletion failed.`,
          success: `${article.title} successfully deleted.`
        }
      );
      setIsDeletingArticle(null);
      await refreshArticles();
    } catch (err) {}
    // const {}
  }
  const actions =
    userRole === ADMIN_ROLES.SUPER_ADMIN
      ? [
          { name: "download", action: handleDownload },
          { name: "view", action: handleView },

          {
            name: "delete",
            action: (article) => setIsDeletingArticle(article)
          },
          { name: "assign", action: handleAssign }
        ]
      : [
          { name: "download", action: handleDownload },
          { name: "view", action: handleView },

          { name: "assign", action: handleAssign }
        ];
  // console.log(filterArticles(active));
  return (
    <AdminErrorBoundary section="Articles Management">
      <ArticleCont>
        <div className="heading">
          <div className=" w-full flex flex-col items-start xl:flex-row gap-2 xl:items-center justify-between py-4 px-6 ">
            <h1 className=" text-lg  font-bold uppercase">All Articles</h1>
            {currentUser.name && (
              <div className=" flex text-sm lg:text-lg  font-bold uppercase space-x-4 items-center justify-end ">
                <span>Welcome</span>
                <span className=" ">{currentUser?.name}</span>
              </div>
            )}
          </div>
          <div className="search">
            <Search
              value={value}
              setValue={setValue}
              placeholder="Search Articles..."
            />
          </div>
        </div>
        <div className="active-status overflow-x-scroll  space-x-4 py-8">
          <>
            {activeStatus?.map((status, id) => {
              if (!status.access.includes(userRole)) return null;
              return (
                <div
                  key={id}
                  className={`${
                    active === status.status ? "active" : ""
                  } w-fit whitespace-nowrap h-fit px-2.5 py-1.5 rounded-lg border border-[#808080] border-solid cursor-pointer hover:bg-[#46555c] hover:text-white transition-colors duration-100 ease-out capitalize`}
                  onClick={() => setActive(status.status)}
                >
                  {status.status}
                </div>
              );
            })}
          </>
        </div>

        {/* Optimized rendering with error boundaries and performance tracking */}
        {safeArticles.length === 0 ? (
          !isFetchingArticles ? (
            <motion.div
              className="bg-transparent capitalize font-bold text-2xl font-primary text-center w-full py-10 text-black/50 flex flex-col space-y-4 items-center justify-center"
              {...fadeIn}
            >
              <span>
                <FcDeleteDatabase size={40} />
              </span>
              <p>No Articles found</p>
              {fetchError && (
                <button
                  onClick={retryFetch}
                  className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                >
                  Retry Loading
                </button>
              )}
            </motion.div>
          ) : (
            <AdminDashboardSkeleton />
          )
        ) : searchFilteredArticles.length > 0 ? (
          <motion.div variants={staggerContainer} initial="initial" animate="animate">
            <TableErrorBoundary tableName="Articles">
              <AllArticles>
                <ArticlesTable
                  user={userRole}
                  data={searchFilteredArticles}
                  columns={columns}
                  hasStatus={true}
                  value={value}
                  handleUpdateData={handleUpdateData}
                  actions={actions}
                  isPagination={true}
                  actionLoading={actionLoading}
                />
              </AllArticles>
            </TableErrorBoundary>

            <AdminErrorBoundary section="Upload">
              <div className="">
                <UploadFileComp
                  isDownloaded={isDownloaded}
                  refreshData={refreshArticles}
                />
              </div>
            </AdminErrorBoundary>
          </motion.div>
        ) : (
          <motion.div
            className="bg-transparent capitalize font-bold text-xl font-primary text-center w-full text-black/50 py-10 flex flex-col space-y-4 items-center justify-center"
            {...fadeIn}
          >
            <span>
              <FcDeleteDatabase size={40} />
            </span>
            <p>No articles found for "{value}" in {active} status</p>
            <button
              onClick={() => setValue("")}
              className="mt-2 px-3 py-1 bg-blue-900 text-white text-sm rounded hover:bg-blue-800 transition-colors"
            >
              Clear Search
            </button>
          </motion.div>
        )}

        {/* {data.length !== 0 &&
          (filterArticles(active).length > 0 ? (
            <AllArticles>
              <ArticlesTable
                user={userRole}
                data={filterArticles(active)}
                columns={columns}
                hasStatus={true}
                value={value}
                handleUpdateData={handleUpdateData}
                actions={actions}
                // actions={[
                //   { name: "download", action: handleDownload },
                //   { name: "view", action: handleView },
                //   { name: "delete", action: handleDelete },
                //   { name: "assign", action: handleAssign }
                // ]}
              />
            </AllArticles>
          ) : (
            <div className="bg-transparent capitalize font-bold text-xl font-primary text-center w-full  text-black/50 py-10 flex flex-col space-y-4 items-center  justify-center">
              <span>
                <FcDeleteDatabase size={40} />
              </span>
              <p>No articles found</p>
            </div>
          ))} */}
        {/* <div className="">
         
          <UploadFileComp isDownloaded={isDownloaded} />
        </div> */}
        {isAssignModalOpen && <ArticleAssign refreshData={refreshArticles} />}
        {isDeletingArticle && (
          <div className="min-h-screen  bg-black/70 bg-opacity-50 backdrop-blur-sm fixed w-full h-full top-0 left-0 flex items-center justify-center">
            <div
              className=" min-w-[250px] max-w-[400px] h-[250px] flex flex-col bg-white rounded-lg items-center justify-center gap-6
           p-6"
            >
              <p className=" font-medium text-center text-xl ">
                Do you want to remove{" "}
                <strong className="text-red-600 italic underline block">
                  {isDeletingArticle.title}
                </strong>{" "}
                <span className=" block">from the shelf ?</span>
              </p>
              <div className=" w-full items-center justify-between flex gap-6">
                <button
                  onClick={() => setIsDeletingArticle(null)}
                  className=" w-full p-2 rounded-md border border-red-500 text-red-500 transition-all duration-300 ease-in-out hover:bg-red-500 hover:text-white capitalize font-bold text-lg "
                >
                  no
                </button>
                <button
                  onClick={() => handleDelete(isDeletingArticle)}
                  className=" w-full p-2 rounded-md border border-blue-500 text-blue-500 transition-all duration-300 ease-in-out hover:bg-blue-500 hover:text-white capitalize font-bold text-lg "
                >
                  yes
                </button>
              </div>
            </div>
          </div>
        )}
        {/* <div className="col-start-1 text-end">
          <div className="hs-tooltip inline-block [--placement:left]">
            <button
              type="button"
              className="hs-tooltip-toggle w-10 h-10 inline-flex justify-center items-center gap-2 rounded-full bg-gray-50 border border-gray-200 text-gray-600 hover:bg-blue-50 hover:border-blue-200 hover:text-blue-600 "
            >

              <span
                className="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 transition-opacity inline-block absolute invisible z-10 py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded shadow-sm "
                role="tooltip"
              >
                Assign
              </span>
            </button>
          </div> 
        </div>*/}
      </ArticleCont>
    </AdminErrorBoundary>
  );
});

export default ArticlesCompWrapper;
