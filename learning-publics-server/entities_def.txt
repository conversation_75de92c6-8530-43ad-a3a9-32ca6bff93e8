Account
    -_id
    -name
    -username
    -password
    -email
    -status ["verified", "unverified"]
    -refreshToken []
    -phone
    -type ["user", "admin", "super"]

TemporalAccount
    -_id
    -name
    -username
    -password
    -email
    -status ["verified", "unverified"]
    -refreshToken []
    -otp
    -phone
    -type ["user", "admin", "super"]

Invitation
    -_id
    -token
    -email 
    -admin ->Account(_id)

RecoveryLink 
    -_id
    -user ->Account(_id)
    -expiryTime
    -uniqueString

Article
    -_id
    -author
    -email
    -phone
    -country
    -title
    -description
    -status ["pending", "review", "rejected", "approved", "published"],
    -category,
    -admin ->Account(_id)
    -articleId
    -articleUrl
    -format
    -coverPageUrl
    -coverPageId

category
    -_id
    -name
    -admin ->Account(_id)

Course 
    -_id
    -admin ->Account(_id)
    -type ["video", "book"],
    -title
    -category ["art", "science"]
    -resourceUrl
    -resourceId

Plan
    -_id
    -major [art, science],
    -type [free, waec, jamb, dual]
    -price 0.00
    -courses [course(_id)]
    -duration: 1mth (1 month)

UserPlan
    -_id
    -plan ->Plan(_id)
     -account -<Account(_id)
    -expiryDate Date

Download
    -_id
    -admin ->Account(_id)
    -article ->Article(_id);

TemporalReference
    -_id
    -plan ->Plan(_id)
    -account ->Account(_id)
    -reference 