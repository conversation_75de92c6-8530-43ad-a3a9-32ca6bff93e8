import { <PERSON><PERSON><PERSON><PERSON>, ArtPen } from "utils/assets";
import {
  BigMenuDiv,
  SideDesktoImageDiv,
  SideMenuStatic,
  UserDpName
} from "./style";

import BigMenu from "../Menu/BigMenu";
import { DASHBOARD_LOGOUT_ROUTE } from "routes";
import { Img } from "globalStyles";
import { LogoutDiv } from "pages/Dashboard/Logout/style";
import MenuDas from "../Menu";

const AuthSideNavDesk = ({ isOpen, onClick }) => {
  return (
    <SideMenuStatic>
      <BigMenuDiv>
        <SideDesktoImageDiv>
          <Img src={AppLogo} alt="byshelb user" />
        </SideDesktoImageDiv>
        <UserDpName><PERSON></UserDpName>
        <MenuDas />
        <LogoutDiv>
          <BigMenu link={DASHBOARD_LOGOUT_ROUTE} text="Logout" icon={ArtPen} />
        </LogoutDiv>
      </BigMenuDiv>
    </SideMenuStatic>
  );
};

export default AuthSideNavDesk;
