const CategoryModel = require("../models/category.model");

exports.create = async (details) => {
  try{
    const existing = await CategoryModel.findOne({name:{$regex: new RegExp(details.name, 'i')}}).exec();
    if (existing) return {error: `${details.name} already exists`};
    return await CategoryModel.create({...details, $push:{image:details.image}});
  }catch(error){
    return {error};
  }
};
exports.update = async (details) => {
  try{
    return await CategoryModel.findOneAndUpdate({_id:details._id,},{name:details.name, $set:{images: details.image}},{returnOriginal:false}).exec();
  }catch(error){
    return {error};
  }
};
exports.delete = async (id) => {
  try{
    return await CategoryModel.findOneAndDelete({_id:id}).exec();
  }catch(error){
    return {error};
  }
};
exports.findById = async (id) => {
  try{
    return await CategoryModel.findOne({_id:id}).exec();
  }catch(error){
    return {error};
  }
};
exports.exist = async (category) => {
  try{
    return await CategoryModel.findOne({name:{$regex: new RegExp(category, 'i')}}).exec();
  }catch(error){
    return {error};
  }
};
exports.all = async () => {
  try{
    return await CategoryModel.find({}).populate("admin");
  }catch(error){
    return {error};
  }
};