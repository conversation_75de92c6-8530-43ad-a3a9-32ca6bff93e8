const PricingConfig = require('../models/pricingConfig.model');
const { APIError } = require('../utils/apiError');
const { CONSTANTS } = require('../config');

// Get current pricing for a service
exports.getServicePricing = async (req, res, next) => {
  try {
    const { serviceType } = req.params;
    const { region, quantity = 1, promoCode } = req.query;

    const pricingConfig = await PricingConfig.findOne({ 
      serviceType, 
      isActive: true 
    });

    if (!pricingConfig) {
      return next(APIError.notFound('Pricing configuration not found for this service'));
    }

    // Execute any pending scheduled pricing changes
    await pricingConfig.executeScheduledPricing();

    const currentPrice = pricingConfig.getCurrentPrice(region, parseInt(quantity), promoCode);

    res.status(200).json({
      success: true,
      data: {
        serviceType: pricingConfig.serviceType,
        serviceName: pricingConfig.serviceName,
        serviceDescription: pricingConfig.serviceDescription,
        pricing: currentPrice,
        isActive: pricingConfig.isActive,
        paymentMethods: pricingConfig.paymentMethods,
        refundPolicy: pricingConfig.refundPolicy
      }
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};

// Get all pricing configurations (Admin only)
exports.getAllPricingConfigs = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, serviceType, isActive } = req.query;
    
    const filter = {};
    if (serviceType) filter.serviceType = serviceType;
    if (isActive !== undefined) filter.isActive = isActive === 'true';

    const pricingConfigs = await PricingConfig.find(filter)
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await PricingConfig.countDocuments(filter);

    res.status(200).json({
      success: true,
      data: {
        pricingConfigs,
        pagination: {
          current: page,
          pages: Math.ceil(total / limit),
          total
        }
      }
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};

// Create new pricing configuration (Admin only)
exports.createPricingConfig = async (req, res, next) => {
  try {
    const {
      serviceType,
      serviceName,
      serviceDescription,
      currentPrice,
      isActive = true,
      isFree = true,
      paymentMethods = ['paystack'],
      refundPolicy,
      regionalPricing = [],
      bulkPricing = []
    } = req.body;

    // Check if pricing config already exists for this service
    const existingConfig = await PricingConfig.findOne({ serviceType });
    if (existingConfig) {
      return next(APIError.badRequest('Pricing configuration already exists for this service'));
    }

    const pricingConfig = new PricingConfig({
      serviceType,
      serviceName,
      serviceDescription,
      currentPrice: currentPrice || { amount: 0, currency: 'USD' },
      isActive,
      isFree,
      paymentMethods,
      refundPolicy,
      regionalPricing,
      bulkPricing,
      createdBy: req.userId
    });

    await pricingConfig.save();

    res.status(201).json({
      success: true,
      message: 'Pricing configuration created successfully',
      data: pricingConfig
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};

// Update pricing configuration (Admin only)
exports.updatePricingConfig = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const pricingConfig = await PricingConfig.findById(id);
    if (!pricingConfig) {
      return next(APIError.notFound('Pricing configuration not found'));
    }

    // If price is being changed, add to price history
    if (updateData.currentPrice && 
        (updateData.currentPrice.amount !== pricingConfig.currentPrice.amount ||
         updateData.currentPrice.currency !== pricingConfig.currentPrice.currency)) {
      
      pricingConfig.priceHistory.push({
        previousPrice: pricingConfig.currentPrice,
        newPrice: updateData.currentPrice,
        changedBy: req.userId,
        changeReason: updateData.changeReason || 'Manual price update',
        effectiveDate: new Date()
      });
    }

    // Update fields
    Object.keys(updateData).forEach(key => {
      if (key !== 'changeReason') {
        pricingConfig[key] = updateData[key];
      }
    });

    pricingConfig.lastModifiedBy = req.userId;
    await pricingConfig.save();

    res.status(200).json({
      success: true,
      message: 'Pricing configuration updated successfully',
      data: pricingConfig
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};

// Schedule price change (Admin only)
exports.schedulePriceChange = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { newPrice, effectiveDate, reason } = req.body;

    if (!newPrice || !effectiveDate) {
      return next(APIError.badRequest('New price and effective date are required'));
    }

    const pricingConfig = await PricingConfig.findById(id);
    if (!pricingConfig) {
      return next(APIError.notFound('Pricing configuration not found'));
    }

    await pricingConfig.scheduleePriceChange(
      newPrice, 
      new Date(effectiveDate), 
      reason, 
      req.userId
    );

    res.status(200).json({
      success: true,
      message: 'Price change scheduled successfully',
      data: {
        scheduledChange: pricingConfig.scheduledPricing[pricingConfig.scheduledPricing.length - 1]
      }
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};

// Set promotional pricing (Admin only)
exports.setPromotionalPricing = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      discountType,
      discountValue,
      startDate,
      endDate,
      promoCode,
      usageLimit,
      description
    } = req.body;

    const pricingConfig = await PricingConfig.findById(id);
    if (!pricingConfig) {
      return next(APIError.notFound('Pricing configuration not found'));
    }

    pricingConfig.promotionalPricing = {
      isActive: true,
      discountType,
      discountValue,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      promoCode: promoCode?.toUpperCase(),
      usageLimit: usageLimit || 0,
      currentUsage: 0,
      description
    };

    pricingConfig.lastModifiedBy = req.userId;
    await pricingConfig.save();

    res.status(200).json({
      success: true,
      message: 'Promotional pricing set successfully',
      data: pricingConfig.promotionalPricing
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};

// Toggle service pricing (free/paid) (Admin only)
exports.toggleServicePricing = async (req, res, next) => {
  try {
    const { serviceType } = req.params;
    const { isFree, reason } = req.body;

    const pricingConfig = await PricingConfig.findOne({ serviceType });
    if (!pricingConfig) {
      return next(APIError.notFound('Pricing configuration not found'));
    }

    // Add to price history
    pricingConfig.priceHistory.push({
      previousPrice: pricingConfig.currentPrice,
      newPrice: { 
        amount: isFree ? 0 : pricingConfig.currentPrice.amount, 
        currency: pricingConfig.currentPrice.currency 
      },
      changedBy: req.userId,
      changeReason: reason || `Service set to ${isFree ? 'free' : 'paid'}`,
      effectiveDate: new Date()
    });

    pricingConfig.isFree = isFree;
    if (isFree) {
      pricingConfig.currentPrice.amount = 0;
    }
    pricingConfig.lastModifiedBy = req.userId;
    
    await pricingConfig.save();

    res.status(200).json({
      success: true,
      message: `Service pricing ${isFree ? 'disabled' : 'enabled'} successfully`,
      data: {
        serviceType: pricingConfig.serviceType,
        isFree: pricingConfig.isFree,
        currentPrice: pricingConfig.currentPrice
      }
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};

// Execute scheduled pricing changes (Cron job endpoint)
exports.executeScheduledPricing = async (req, res, next) => {
  try {
    await PricingConfig.executeAllScheduledPricing();

    res.status(200).json({
      success: true,
      message: 'Scheduled pricing changes executed successfully'
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};

// Get pricing analytics (Admin only)
exports.getPricingAnalytics = async (req, res, next) => {
  try {
    const { serviceType, startDate, endDate } = req.query;

    const filter = {};
    if (serviceType) filter.serviceType = serviceType;

    const pricingConfigs = await PricingConfig.find(filter);

    const analytics = pricingConfigs.map(config => ({
      serviceType: config.serviceType,
      serviceName: config.serviceName,
      currentPrice: config.currentPrice,
      isFree: config.isFree,
      analytics: config.analytics,
      totalScheduledChanges: config.scheduledPricing.length,
      pendingChanges: config.scheduledPricing.filter(sp => !sp.isExecuted).length,
      hasActivePromotion: config.promotionalPricing.isActive
    }));

    res.status(200).json({
      success: true,
      data: {
        analytics,
        summary: {
          totalServices: pricingConfigs.length,
          freeServices: pricingConfigs.filter(c => c.isFree).length,
          paidServices: pricingConfigs.filter(c => !c.isFree).length,
          totalRevenue: pricingConfigs.reduce((sum, c) => sum + (c.analytics.totalRevenue || 0), 0)
        }
      }
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};

// Delete pricing configuration (Super Admin only)
exports.deletePricingConfig = async (req, res, next) => {
  try {
    const { id } = req.params;

    const pricingConfig = await PricingConfig.findById(id);
    if (!pricingConfig) {
      return next(APIError.notFound('Pricing configuration not found'));
    }

    await PricingConfig.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: 'Pricing configuration deleted successfully'
    });
  } catch (error) {
    return next(APIError.internal(error.message));
  }
};
