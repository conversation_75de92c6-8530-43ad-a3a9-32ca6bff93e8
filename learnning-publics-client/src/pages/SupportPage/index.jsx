import React, { useState } from 'react';
import { motion } from 'framer-motion';
import styled from 'styled-components';
import { MainContainer } from 'globalStyles';
import SEOHead from 'components/SEO/SEOHead';
import { toast } from 'react-hot-toast';
import Colors from 'utils/colors';
import { useNavigate } from 'react-router-dom';

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem 0;
`;

const ContentWrapper = styled(MainContainer)`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 1rem;
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
`;

const HeroSection = styled.div`
  background: linear-gradient(135deg, ${Colors.primary} 0%, #5a6a72 100%);
  color: white;
  padding: 4rem 2rem;
  border-radius: 1rem;
  margin-bottom: 3rem;
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
  }
`;

const HeroContent = styled.div`
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
`;

const HeroTitle = styled.h1`
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
`;

const HeroSubtitle = styled.p`
  font-size: 1.3rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  line-height: 1.6;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`;

const StatItem = styled.div`
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: ${Colors.secondary};
`;

const StatLabel = styled.div`
  font-size: 0.9rem;
  opacity: 0.8;
`;

const SupportGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
`;

const SupportCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: ${Colors.secondary};
  }
`;

const CardIcon = styled.div`
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: white;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, ${Colors.secondary}, #f4d03f);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  ${SupportCard}:hover &::after {
    opacity: 1;
  }
`;

const CardTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 1rem;
`;

const CardContent = styled.div`
  color: #374151;
  line-height: 1.6;
  
  ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
  }
  
  li {
    margin-bottom: 0.5rem;
  }
`;

const ContactForm = styled.form`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  min-height: 120px;
  resize: vertical;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  background: white;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: #3b82f6;
  }
`;

const SubmitButton = styled.button`
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  color: white;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

// Pricing Tier Styles
const PricingSection = styled.section`
  background: white;
  border-radius: 1rem;
  padding: 3rem 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const PricingTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: 700;
  color: ${Colors.primary};
  text-align: center;
  margin-bottom: 1rem;
`;

const PricingSubtitle = styled.p`
  font-size: 1.2rem;
  color: #64748b;
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const PricingGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const PricingCard = styled.div`
  background: ${props => props.featured ? `linear-gradient(135deg, ${Colors.primary}, #5a6a72)` : 'white'};
  color: ${props => props.featured ? 'white' : '#374151'};
  border: ${props => props.featured ? 'none' : `2px solid ${Colors.secondary}`};
  border-radius: 1rem;
  padding: 2rem;
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
`;

const PopularBadge = styled.div`
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: ${Colors.secondary};
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 600;
`;

const TierName = styled.h3`
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-align: center;
`;

const TierPrice = styled.div`
  text-align: center;
  margin-bottom: 2rem;
`;

const Price = styled.span`
  font-size: 3rem;
  font-weight: 700;
  color: ${props => props.featured ? 'white' : Colors.secondary};
`;

const PriceUnit = styled.span`
  font-size: 1rem;
  opacity: 0.8;
  margin-left: 0.5rem;
`;

const FeatureList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 2rem 0;
`;

const Feature = styled.li`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.95rem;

  &::before {
    content: '✓';
    color: ${props => props.featured ? Colors.secondary : Colors.primary};
    font-weight: bold;
    margin-right: 0.75rem;
    font-size: 1.2rem;
  }
`;

const SelectButton = styled.button`
  width: 100%;
  background: ${props => props.featured ? 'white' : `linear-gradient(135deg, ${Colors.primary}, #5a6a72)`};
  color: ${props => props.featured ? Colors.primary : 'white'};
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
`;

const ImpactSection = styled.section`
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  color: white;
  border-radius: 1rem;
  padding: 3rem 2rem;
  margin-bottom: 3rem;
  text-align: center;
`;

const ImpactGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
`;

const ImpactCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 1rem;
  backdrop-filter: blur(10px);
`;

const ImpactNumber = styled.div`
  font-size: 2.5rem;
  font-weight: 700;
  color: ${Colors.secondary};
  margin-bottom: 0.5rem;
`;

const ImpactLabel = styled.div`
  font-size: 1rem;
  opacity: 0.9;
`;

const SupportPage = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    category: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const pricingTiers = [
    {
      name: "Community Supporter",
      price: 25,
      period: "month",
      description: "Support our mission to democratize academic publishing",
      features: [
        "Early access to new articles",
        "Monthly research newsletter",
        "Community forum access",
        "Digital supporter badge",
        "Tax-deductible receipt"
      ],
      impact: "Supports 1 researcher from developing countries",
      buttonText: "Support Community"
    },
    {
      name: "Research Champion",
      price: 100,
      period: "month",
      description: "Accelerate breakthrough research in agriculture & environment",
      features: [
        "All Community benefits",
        "Quarterly virtual events with editors",
        "Priority manuscript review feedback",
        "Research collaboration opportunities",
        "Annual impact report",
        "Recognition in journal acknowledgments"
      ],
      impact: "Funds peer review for 5 articles monthly",
      buttonText: "Champion Research",
      featured: true
    },
    {
      name: "Academic Patron",
      price: 500,
      period: "month",
      description: "Transform global agricultural research landscape",
      features: [
        "All Research Champion benefits",
        "Direct access to editorial board",
        "Sponsored research opportunities",
        "Custom research briefings",
        "VIP conference invitations",
        "Legacy naming opportunities",
        "Strategic advisory participation"
      ],
      impact: "Sponsors full publication of 2 research papers",
      buttonText: "Become a Patron"
    }
  ];

  const impactMetrics = [
    { number: "2,500+", label: "Researchers Supported" },
    { number: "50+", label: "Countries Reached" },
    { number: "95%", label: "Open Access Rate" },
    { number: "$2M+", label: "Research Funding Facilitated" }
  ];

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleTierSelection = (tier) => {
    // Store selected tier in localStorage for payment processing
    localStorage.setItem('selectedSupportTier', JSON.stringify({
      name: tier.name,
      price: tier.price,
      period: tier.period,
      description: tier.description,
      impact: tier.impact
    }));

    // Navigate to payment page
    navigate('/payment', {
      state: {
        type: 'support',
        tier: tier,
        amount: tier.price,
        description: `${tier.name} - Monthly Support`
      }
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      toast.success('Support request submitted successfully! We\'ll get back to you within 24 hours.');
      setFormData({
        name: '',
        email: '',
        category: '',
        subject: '',
        message: ''
      });
      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <>
      <SEOHead
        title="Support Center - Get Help & Contact Us"
        description="Get comprehensive support for Learning Publics Journal. Contact our team for technical help, submission assistance, and general inquiries."
        keywords="support, help center, contact, technical support, submission help, customer service"
        canonicalUrl="/support"
      />
      
      <PageContainer>
        <ContentWrapper>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <HeroSection>
              <HeroContent>
                <HeroTitle>Support Academic Excellence</HeroTitle>
                <HeroSubtitle>
                  Join our mission to democratize academic publishing and accelerate breakthrough research
                  in agriculture and environmental studies. Your support directly impacts researchers worldwide.
                </HeroSubtitle>
                <StatsGrid>
                  <StatItem>
                    <StatNumber>2,500+</StatNumber>
                    <StatLabel>Researchers Supported</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatNumber>50+</StatNumber>
                    <StatLabel>Countries Reached</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatNumber>95%</StatNumber>
                    <StatLabel>Open Access Rate</StatLabel>
                  </StatItem>
                  <StatItem>
                    <StatNumber>$2M+</StatNumber>
                    <StatLabel>Research Funding</StatLabel>
                  </StatItem>
                </StatsGrid>
              </HeroContent>
            </HeroSection>

            <PricingSection>
              <PricingTitle>Choose Your Impact Level</PricingTitle>
              <PricingSubtitle>
                Every contribution directly supports researchers, accelerates discoveries, and advances
                sustainable solutions for global challenges.
              </PricingSubtitle>

              <PricingGrid>
                {pricingTiers.map((tier, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                  >
                    <PricingCard featured={tier.featured}>
                      {tier.featured && <PopularBadge>Most Popular</PopularBadge>}

                      <TierName>{tier.name}</TierName>

                      <TierPrice>
                        <Price featured={tier.featured}>${tier.price}</Price>
                        <PriceUnit>/{tier.period}</PriceUnit>
                      </TierPrice>

                      <p style={{
                        textAlign: 'center',
                        marginBottom: '2rem',
                        opacity: 0.9,
                        lineHeight: 1.5
                      }}>
                        {tier.description}
                      </p>

                      <FeatureList>
                        {tier.features.map((feature, featureIndex) => (
                          <Feature key={featureIndex} featured={tier.featured}>
                            {feature}
                          </Feature>
                        ))}
                      </FeatureList>

                      <div style={{
                        background: tier.featured ? 'rgba(255,255,255,0.1)' : '#f8fafc',
                        padding: '1rem',
                        borderRadius: '0.5rem',
                        marginBottom: '2rem',
                        textAlign: 'center'
                      }}>
                        <strong>Impact: </strong>{tier.impact}
                      </div>

                      <SelectButton
                        featured={tier.featured}
                        onClick={() => handleTierSelection(tier)}
                      >
                        {tier.buttonText}
                      </SelectButton>
                    </PricingCard>
                  </motion.div>
                ))}
              </PricingGrid>
            </PricingSection>

            <ImpactSection>
              <h2 style={{ fontSize: '2.5rem', fontWeight: '700', marginBottom: '1rem' }}>
                Your Support Creates Real Impact
              </h2>
              <p style={{ fontSize: '1.2rem', opacity: '0.9', marginBottom: '2rem', maxWidth: '600px', margin: '0 auto 2rem' }}>
                See how your contribution directly advances agricultural research and environmental sustainability worldwide.
              </p>

              <ImpactGrid>
                {impactMetrics.map((metric, index) => (
                  <ImpactCard key={index}>
                    <ImpactNumber>{metric.number}</ImpactNumber>
                    <ImpactLabel>{metric.label}</ImpactLabel>
                  </ImpactCard>
                ))}
              </ImpactGrid>
            </ImpactSection>

            <SupportGrid>
              <SupportCard>
                <CardIcon>🎓</CardIcon>
                <CardTitle>Author Excellence Program</CardTitle>
                <CardContent>
                  <p>Comprehensive support for academic authors at every stage:</p>
                  <ul>
                    <li>Manuscript preparation workshops</li>
                    <li>Academic writing mentorship</li>
                    <li>Research methodology guidance</li>
                    <li>Publication strategy consulting</li>
                    <li>Grant writing assistance</li>
                  </ul>
                  <p><strong>Impact:</strong> 95% of supported authors achieve publication</p>
                </CardContent>
              </SupportCard>

              <SupportCard>
                <CardIcon>🔬</CardIcon>
                <CardTitle>Research Integrity Services</CardTitle>
                <CardContent>
                  <p>Ensuring the highest standards of academic integrity:</p>
                  <ul>
                    <li>Plagiarism detection and prevention</li>
                    <li>Data verification and validation</li>
                    <li>Ethical review consultation</li>
                    <li>Reproducibility assessments</li>
                    <li>Open science compliance</li>
                  </ul>
                  <p><strong>Certification:</strong> ISO 27001 compliant processes</p>
                </CardContent>
              </SupportCard>

              <SupportCard>
                <CardIcon>🌍</CardIcon>
                <CardTitle>Global Outreach Initiative</CardTitle>
                <CardContent>
                  <p>Expanding access to quality research worldwide:</p>
                  <ul>
                    <li>Developing country author support</li>
                    <li>Translation and language services</li>
                    <li>Open access advocacy</li>
                    <li>International collaboration facilitation</li>
                    <li>Digital literacy programs</li>
                  </ul>
                  <p><strong>Reach:</strong> Supporting researchers in 50+ countries</p>
                </CardContent>
              </SupportCard>

              <SupportCard>
                <CardIcon>💡</CardIcon>
                <CardTitle>Innovation & Technology</CardTitle>
                <CardContent>
                  <p>Leveraging cutting-edge technology for research advancement:</p>
                  <ul>
                    <li>AI-powered research discovery</li>
                    <li>Blockchain-based peer review</li>
                    <li>Advanced analytics and metrics</li>
                    <li>Virtual collaboration platforms</li>
                    <li>Next-generation publishing tools</li>
                  </ul>
                  <p><strong>Investment:</strong> $2M+ in R&D annually</p>
                </CardContent>
              </SupportCard>

              <SupportCard>
                <CardIcon>🤝</CardIcon>
                <CardTitle>Partnership Development</CardTitle>
                <CardContent>
                  <p>Building strategic alliances for academic excellence:</p>
                  <ul>
                    <li>University partnerships</li>
                    <li>Research institution collaborations</li>
                    <li>Industry-academia bridges</li>
                    <li>International society affiliations</li>
                    <li>Funding agency relationships</li>
                  </ul>
                  <p><strong>Network:</strong> 200+ institutional partners globally</p>
                </CardContent>
              </SupportCard>

              <SupportCard>
                <CardIcon>📊</CardIcon>
                <CardTitle>Impact & Analytics</CardTitle>
                <CardContent>
                  <p>Measuring and maximizing research impact:</p>
                  <ul>
                    <li>Citation tracking and analysis</li>
                    <li>Altmetrics and social impact</li>
                    <li>Research visibility optimization</li>
                    <li>Performance benchmarking</li>
                    <li>Impact reporting and insights</li>
                  </ul>
                  <p><strong>Metrics:</strong> Tracking 10M+ research interactions</p>
                </CardContent>
              </SupportCard>
            </SupportGrid>

            <ContactForm onSubmit={handleSubmit}>
              <h2 style={{ color: '#1e40af', marginBottom: '2rem', fontSize: '1.8rem' }}>Contact Support</h2>
              
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <FormGroup>
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </FormGroup>
              </div>

              <FormGroup>
                <Label htmlFor="category">Support Category *</Label>
                <Select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select a category</option>
                  <option value="submission">Submission Support</option>
                  <option value="technical">Technical Support</option>
                  <option value="editorial">Editorial Inquiries</option>
                  <option value="general">General Support</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <Label htmlFor="subject">Subject *</Label>
                <Input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <Label htmlFor="message">Message *</Label>
                <TextArea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Please describe your issue or question in detail..."
                  required
                />
              </FormGroup>

              <SubmitButton type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : 'Submit Support Request'}
              </SubmitButton>
            </ContactForm>
          </motion.div>
        </ContentWrapper>
      </PageContainer>
    </>
  );
};

export default SupportPage;
