// import { Img } from 'globalStyles'

import {
  ArtCardRight,
  ArtHead,
  ArtHeadCon,
  ArticleCardContainer
} from "./style";
import React from "react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";

import ArticleCards from "./ArticleCards";
import { books } from "utils/assets";

// Import animations from design system
import { fadeIn, slideUp, staggerContainer, staggerItem } from "design-system/animations";

// import { LOCAL_STORAGE } from 'api/LOCALSTORAGE'

// import { MainBtn } from '../Hero Section/style'
// import { Link } from 'react-router-dom'

function ArticleSection({ isFetchingArticles, articles = [], getAllArticles }) {
  // Use articles from props instead of fetching separately
  // const [article, setArticle] = useState([]);

  // const handleData = async () => {
  //   try {
  //     const { data } = await jmsApp.get(ARTICLE_ENDPOINT.ARTICLE_PUBLISHED());
  //     if (data?.success) {
  //       setArticle(data.article);
  //     }
  //   } catch (error) {
  //     if (error?.response?.status === "401") {
  //       // navigate('/login')
  //     } else {
  //       // return new Error(error);
  //     }
  //   }
  // };

  // useEffect(() => {
  //   handleData();
  // }, []);

  // eslint-disable-next-line no-unused-vars
  const lineStyle = {
    content: "",
    position: "absolute",
    bottom: 0,
    left: 0,
    width: "100%",
    height: "2px", // Thickness of the red line
    backgroundColor: "#ff0000" // Red color
  };

  // Handle loading state with modern skeleton
  if (isFetchingArticles) {
    return (
      <motion.div
        className="w-full"
        initial="hidden"
        animate="visible"
        variants={staggerContainer}
      >
        <ArtHeadCon>
          <div className="flex items-center justify-between w-full">
            <ArtHead>Latest Articles</ArtHead>
            <Link
              to="/articles"
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 border border-red-600 hover:border-red-700 rounded-lg transition-colors duration-200"
            >
              View All Articles
              <motion.span
                className="ml-2"
                whileHover={{ x: 4 }}
                transition={{ duration: 0.2 }}
              >
                →
              </motion.span>
            </Link>
          </div>
        </ArtHeadCon>
        <ArticleCardContainer>
          <div className="w-full px-4 sm:w-11/12 lg:w-full mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 place-items-center">
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={i}
                className="w-full max-w-sm mx-auto rounded-lg overflow-hidden flex flex-col bg-white shadow-lg"
                variants={slideUp}
              >
                {/* Skeleton Image */}
                <div className="relative aspect-[4/3] overflow-hidden bg-gray-200 animate-pulse">
                  <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-[shimmer_1.5s_infinite]"></div>
                  <div className="absolute top-2 right-2 bg-gray-300 text-transparent text-xs px-2 py-1 rounded-full animate-pulse">
                    Article
                  </div>
                </div>

                {/* Skeleton Content */}
                <div className="flex flex-col flex-grow p-4 sm:p-5 space-y-3">
                  {/* Skeleton Title */}
                  <div className="space-y-2">
                    <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-6 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                  </div>

                  {/* Skeleton Author */}
                  <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>

                  {/* Skeleton Description */}
                  <div className="space-y-2 flex-grow">
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                  </div>

                  {/* Skeleton Button */}
                  <div className="h-11 bg-gray-200 rounded-lg animate-pulse mt-auto"></div>
                </div>
              </motion.div>
            ))}
          </div>
        </ArticleCardContainer>
      </motion.div>
    );
  }

  // Handle empty state
  if (articles.length === 0) {
    return (
      <div className="w-full h-full flex items-center justify-center flex-col gap-6">
        <p className="text-lg font-bold text-blue-800 uppercase">
          No articles found
        </p>
        <button
          onClick={() => getAllArticles()}
          className="p-2 capitalize text-xs font-bold border-blue-600 border border-solid rounded-lg text-blue-600 hover:text-white hover:bg-blue-600 transition-all duration-300 ease-in-out"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <motion.div
      className="w-full"
      initial="hidden"
      animate="visible"
      variants={fadeIn}
    >
      {/* Publishing Guide Link - Subtle and non-intrusive */}
      <motion.div
        className="w-full flex justify-end mb-4 px-4 sm:px-12"
        variants={fadeIn}
      >
        <Link
          to="/publishing-guide"
          className="text-sm text-red-600 hover:text-red-800 underline transition-colors duration-200 flex items-center gap-1 font-medium"
        >
          <span>📝</span>
          <span>New to publishing?</span>
        </Link>
      </motion.div>

      <motion.div variants={slideUp}>
        <ArtHeadCon>
          <ArtHead>Latest Articles</ArtHead>
        </ArtHeadCon>
      </motion.div>

      <ArticleCardContainer>
        {/* Mobile-first responsive grid with improved touch targets and spacing */}
        <motion.div
          className="w-full px-4 sm:w-11/12 lg:w-full mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 place-items-center"
          initial="hidden"
          animate="show"
          variants={staggerContainer}
        >

          {articles.length > 0 ? (
            articles
              .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
              .reverse()
              .slice(0, 4)
              .map((e, i) => {
                return (
                  <motion.div
                    key={e.articleId}
                    className="w-full max-w-sm mx-auto rounded-lg overflow-hidden flex flex-col bg-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group hover:scale-[1.02] active:scale-[0.98]"
                    variants={staggerItem}
                    whileHover={{ y: -4 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {/* Image container with aspect ratio and enhanced mobile optimization */}
                    <div className="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
                      <motion.img
                        src={books}
                        alt="journal icon"
                        className="w-full h-full object-cover transition-transform duration-500"
                        whileHover={{ scale: 1.1 }}
                        loading="lazy"
                      />
                      {/* Learning Publics brand overlay with animation */}
                      <motion.div
                        className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.2 }}
                      >
                        Article
                      </motion.div>
                      {/* Gradient overlay for better text readability */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>

                    {/* Content container with improved mobile spacing and animations */}
                    <div className="flex flex-col flex-grow p-4 sm:p-5 space-y-3">
                      {/* Title with better mobile typography and hover effects */}
                      <Link
                        to={`/articles/${e.slug}`}
                        className="group/title"
                      >
                        <motion.h3
                          className="font-bold text-lg sm:text-xl text-blue-900 leading-tight line-clamp-2 group-hover/title:text-red-600 transition-colors duration-300"
                          whileHover={{ scale: 1.02 }}
                        >
                          {e.title}
                        </motion.h3>
                      </Link>

                      {/* Author with Learning Publics styling and improved mobile display */}
                      <motion.p
                        className="font-semibold text-base text-gray-700 truncate"
                        initial={{ opacity: 0.8 }}
                        animate={{ opacity: 1 }}
                      >
                        {e.author}
                      </motion.p>

                      {/* Description with mobile-optimized line clamping and fade effect */}
                      <motion.p
                        className="text-gray-600 text-sm sm:text-base leading-relaxed line-clamp-3 flex-grow"
                        initial={{ opacity: 0.9 }}
                        animate={{ opacity: 1 }}
                      >
                        {e.description}
                      </motion.p>

                      {/* Enhanced mobile-friendly read more button with touch feedback */}
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Link
                          to={`/articles/${e.slug}`}
                          className="inline-flex items-center justify-center mt-auto py-3 px-6 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 active:bg-red-800 transition-all duration-200 touch-manipulation min-h-[44px] shadow-md hover:shadow-lg focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                        >
                          <span>Read Article</span>
                          <motion.span
                            className="ml-2"
                            initial={{ x: 0 }}
                            whileHover={{ x: 4 }}
                            transition={{ duration: 0.2 }}
                          >
                            →
                          </motion.span>
                        </Link>
                      </motion.div>
                    </div>
                  </motion.div>
                );
              })
          ) : (
            <motion.div variants={slideUp}>
              <ArtCardRight>
                <ArticleCards
                  title="Global Journal of Computer Science"
                  body=" New shhdggsk lorem sikpi lodgers  ahshftdksn  ahvrxsfkffj ir utan beresam makrosaska. Äpolig
        pseudoktigt i togt som telenade..."
                />
              </ArtCardRight>
            </motion.div>
          )}
        </motion.div>
      </ArticleCardContainer>
    </motion.div>
  );
}

export default ArticleSection;
