import axios from "axios";
import logger from "./logService";

axios.interceptors.response.use(null, (error) => {
  const expectedError =
    error.response &&
    error.response?.status >= 400 &&
    error.response?.status < 500;

  if (!expectedError) {
    logger.log(error);
    if (error.Error === "Network Error") {
      // console.error("Network Error");
    }
  }

  return Promise.reject(error);
});

// export function setJwt(jwt) {
// 	if (typeof window === "object") {
// 		axios.defaults.headers.common["authorization"] = `Bearer ${jwt}`
// 	}
// }

// export const jmsApp = axios.create({baseURL:BASE_URL,
//     withCredentials:true,
//     timeout:1000,
// });

// let http = {
// 	get: axios.get,
// 	post: axios.post,
// 	put: axios.put,
// 	delete: axios.delete,
// 	setJwt,
// 	withCredentials: true
// }
// export default http
