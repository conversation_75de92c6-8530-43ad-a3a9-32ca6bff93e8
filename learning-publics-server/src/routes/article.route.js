const express = require("express");
const articleRoute = express.Router();
const Controller = require("../controllers");
const { adminRequired, notELearningRole } = require("../middlewares/auth.middleware");

articleRoute.post("/article", Controller.ArticleController.sendArticle)
articleRoute.get("/article", adminRequired,notELearningRole, Controller.ArticleController.articles)
articleRoute.patch("/article", adminRequired,notELearningRole, Controller.ArticleController.updateArticleStatus)
articleRoute.delete("/article", adminRequired,notELearningRole, Controller.ArticleController.deleteArticle);
articleRoute.get("/article-verify", Controller.ArticleController.verifyArticleView);
articleRoute.get("/articles/category", Controller.ArticleController.articlesByCategory);
articleRoute.get("/articles/all", adminRequired,notELearningRole, Controller.ArticleController.articlesAll);
articleRoute.patch("/article/admin", adminRequired,notELearningRole, Controller.ArticleController.adminUploadArticle);
articleRoute.get("/article/admin/:",adminRequired,notELearningRole, Controller.ArticleController.articlesById);
articleRoute.get("/article/:", Controller.ArticleController.publishedArticlesById);
articleRoute.get("/article/review",adminRequired,notELearningRole, Controller.ArticleController.articlesInReview);
articleRoute.get("/article/published", Controller.ArticleController.articlesPublished);
articleRoute.get("/articles/title", Controller.ArticleController.articlesByTitle);
articleRoute.post("/article/assign", adminRequired,notELearningRole, Controller.ArticleController.assignEditors)
articleRoute.post("/article/send-review", adminRequired, notELearningRole, Controller.ArticleController.sendArticleReview)

// New SEO-friendly routes
articleRoute.get("/articles/seo", Controller.ArticleController.getArticlesSEOOptimized);
articleRoute.get("/articles/search", Controller.ArticleController.searchArticlesController);
articleRoute.get("/articles/slug/:slug", Controller.ArticleController.getArticleBySlug);
articleRoute.get("/articles/category/:categorySlug", Controller.ArticleController.getArticlesByCategorySlug);
articleRoute.get("/articles/related", Controller.ArticleController.getRelatedArticlesController);
articleRoute.post("/articles/track-view/:slug", Controller.ArticleController.trackArticleViewController);
articleRoute.post("/articles/track-share/:slug", Controller.ArticleController.trackSocialShareController);

// Bookmark routes
articleRoute.post("/articles/bookmark/:slug", Controller.ArticleController.addBookmarkController);
articleRoute.delete("/articles/bookmark/:slug", Controller.ArticleController.removeBookmarkController);
articleRoute.get("/articles/bookmarks", Controller.ArticleController.getUserBookmarksController);
articleRoute.get("/articles/bookmark-status/:slug", Controller.ArticleController.getBookmarkStatusController);

// PDF Proxy route
articleRoute.get("/articles/pdf/:slug", Controller.ArticleController.getPDFProxy);

module.exports = {
  articleRoute,
};