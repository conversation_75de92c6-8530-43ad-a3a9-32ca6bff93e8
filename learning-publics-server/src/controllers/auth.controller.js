const { compareSync, hashSync } = require("bcryptjs");
const { APIError } = require("../utils/apiError");
const jwt = require("jsonwebtoken");
const responseBuilder = require("../utils/responsBuilder");
const { isValidEmail } = require("../utils/validation");
const {
  usernameExist,
  defaultAccount,
  emailExist,
  getUserById,
  updateUserPass,
  logOutUser,
  deleteUser,
} = require("../services");
const config = require("../config/env");
const logger = require("../logger");
const { ERROR_FIELD, META } = require("../utils/actions");
const AccountModel = require("../models/account.model");
const { CONSTANTS } = require("../config");

exports.login = async (req, res, next) => {
  try {
    let refreshToken = req.cookies?.jwt;
    if (!refreshToken) refreshToken = req.headers?.authorization?.split(" ")[1];
    if (!refreshToken) refreshToken = req.headers?.cookie?.split("=")[1];
    if (!refreshToken) refreshToken = req.body.token;
    const { username, password } = req.body;
    if (!username) return next(APIError.badRequest("username is required"));
    if (!password) return next(APIError.badRequest("password is required"));
    if (password.length < 8) return next(APIError.badRequest("Password must be at least 8 characters"));
    const exist = await usernameExist(username);
    if (!exist) return next(APIError.customError("User does  not exist", 404));
    if (exist.error) return next(APIError.customError(exist.error, 400));
    const verify = compareSync(password, exist.password);
    if (!verify) return next(APIError.badRequest("Incorrect password", 400));
    const foundUser = await AccountModel.findOne({ refreshToken }).exec();
    if (foundUser) {
      jwt.verify(
        refreshToken,
        config.TOKEN_SECRETE,
        // eslint-disable-next-line no-unused-vars
        async (err, _decoded) => {
          const untrusted = await AccountModel.findOne({
            _id: foundUser._id,
          }).exec();
          untrusted.refreshToken = [];
        }
      );
      logger.info("Token reuse detected", { meta: "auth-service" });
    }
    if (exist.refreshToken.length > 0 && exist.type === CONSTANTS.ACCOUNT_TYPE[0])
      return next(
        APIError.customError(
          "There is active session on this account, logout first",
          400
        )
      );
    let payload = {};
    payload = { id: exist._id, type: exist.type };
    const token = jwt.sign(payload, config.TOKEN_SECRETE, { expiresIn: "30m" });
    const newRefreshToken = jwt.sign(payload, config.REFRESH_TOKEN_SECRETE, {
      expiresIn: "60m",
    });

    res.clearCookie("jwt", token, {
      httpOnly: false,
      secure: true,
      sameSite: "none",
      // maxAge: 60 * 60 * 1000,
    });
    let newRefreshTokenArray = [];
    if (refreshToken)
      newRefreshTokenArray = exist.refreshToken.filter(
        (rt) => rt !== refreshToken
      );
    if (exist.type === CONSTANTS.ACCOUNT_TYPE[1])
      newRefreshTokenArray = exist.refreshToken;

    exist.refreshToken = [...newRefreshTokenArray, newRefreshToken];
    const data = responseBuilder.buildUser(exist.toObject());
    exist.save();
    logger.info("Login successful", { meta: "auth-service" });
    const response = responseBuilder.commonReponse(
      "login successful",
      data,
      "user",
      { token, refreshToken: newRefreshToken }
    );
    // res.cookie("jwt", newRefreshToken, {
    //   httpOnly: false,
    //   secure: true,
    //   sameSite: "none",
    //   // maxAge: 60 * 60 * 1000,
    // });
    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};

exports.defaultAdminAccount = async (req, res, next) => {
  try {
    const { name, username, password, email } = req.body;
    if (!name) return next(APIError.badRequest("Name is required"));
    if (!username) return next(APIError.badRequest("Username is required"));
    if (!password) return next(APIError.badRequest("Password is required"));
    if (!email) return next(APIError.badRequest("Email is required"));
    let exist = await usernameExist(username);
    if (exist)
      return next(APIError.customError(`${username} is not available`, 400));
    if (!isValidEmail(email))
      return next(APIError.badRequest("Email is not valid"));
    exist = await emailExist(email);
    if (exist) return next(APIError.customError(`${email} already exist`, 400));
    const details = { username, email, name };
    const hashedPassword = hashSync(password, 12);
    details.password = hashedPassword;
    if (req.body.phone) details.phone = req.body.phone;
    details.type = CONSTANTS.ACCOUNT_TYPE[2];
    const register = await defaultAccount(details);
    if (register.error) return next(APIError.customError(register.error, 400));
    logger.info("Default Account successful", { meta: "auth-service" });
    const data = responseBuilder.buildUser(register.toObject());
    const response = responseBuilder.commonReponse(
      "Registration successful",
      data,
      "account"
    );
    res.status(201).json(response);
  } catch (error) {
    next(error);
  }
};

exports.logout = async (req, res, next) => {
  try {
    // let refreshToken = req.body.refreshToken;
    let refreshToken = req.cookies?.jwt;
    if (!refreshToken) refreshToken = req.body.token;
    if (!refreshToken) refreshToken = req.headers?.authorization?.split(" ")[1];
    if (!refreshToken) refreshToken = req.headers?.cookie?.split("=")[1];
    if (!refreshToken) return next(APIError.customError("You need to login first", 401));
    const payload = jwt.verify(refreshToken, config.TOKEN_SECRETE);
    const found = await AccountModel.findOne({_id:payload.id}).exec();
  if (!found) return next(APIError.customError("You need to login first", 401));
    const isUser = await getUserById(payload.id);
    if (!isUser) return next(APIError.customError(`user does not exist`, 404));
    if (isUser.error) return next(APIError.customError(isUser.error));
    if (isUser.type === CONSTANTS.ACCOUNT_TYPE[0]) {
      isUser.refreshToken = [];
      isUser.save();
    } else if (isUser.type === CONSTANTS.ACCOUNT_TYPE[1] || isUser.type === CONSTANTS.ACCOUNT_TYPE[2]) {
      const refreshTokenArr = isUser.refreshToken.filter(
        (rt) => rt !== refreshToken
      );
      isUser.refreshToken = [...refreshTokenArr];
      isUser.save();
    }
    logger.info("Logout successful", { meta: "auth-service" });
    res.clearCookie("jwt");
    res
      .status(200)
      .json({ success: true, msg: "You have successfully logged out" });
  } catch (error) {
    if (error.message === ERROR_FIELD.JWT_EXPIRED) next(APIError.unauthenticated());
    else next(error);
  }
};
exports.logoutAll = async (req, res, next) => {
  try {
    let refreshToken = req.body.refreshToken;
    // let refreshToken = req.cookies?.jwt;
    if (!refreshToken) refreshToken = req.headers?.authorization?.split(" ")[1];
    if (!refreshToken) refreshToken = req.headers?.cookie?.split("=")[1];
    if (!refreshToken) refreshToken = req.body.token;
    const found = await AccountModel.findOne({ refreshToken }).exec();
    if (!found) {
      const { username, password } = req.body;
      if (!username || !password) return next(APIError.customError("Password and username ar required", 400));
      const exist = await usernameExist(username);
      if (!exist) return next(APIError.customError("Account Does not exist", 401));
      if (exist.error) return next(APIError.customError(exist.error, 400));
      const verify = compareSync(password, exist.password);
      if (!verify) return next(APIError.customError("Incorrect Password", 400));
      exist.refreshToken = [];
      exist.save();
      logger.info("Logged out all sessions successfully", { meta: "auth-service" });
      res.clearCookie("jwt");
      res
        .status(200)
        .json({ success: true, msg: "You have successfully logged out" });
    } else {
      if (!refreshToken) return next(APIError.customError("Refresh token is required", 401));
      const payload = jwt.decode(refreshToken, config.REFRESH_TOKEN_SECRETE);
      const isUser = await getUserById(payload.id);
      if (!isUser) return next(APIError.customError(`user does not exist`, 404));
      if (isUser.error) return next(APIError.customError(isUser.error));
      isUser.refreshToken = [];
      isUser.save();
      logger.info("Logged out all sessions successfully", { meta: "auth-service" });
      res.clearCookie("jwt");
      res
        .status(200)
        .json({ success: true, msg: "You have successfully logged out" });
    }
  } catch (error) {
    next(error);
  }
};

exports.resetLogin = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;
    if (!req.userId) return next(APIError.unauthenticated());
    if (!currentPassword)
      return next(APIError.badRequest("Provide current password"));
    if (!newPassword) return next(APIError.badRequest("Provide new password"));
    const check = await getUserById(req.userId);
    if (!check) return res.status(404).json({ error: "Incorrect password" });
    if (check.error) return res.status(404).json(check.error);
    const verify = compareSync(currentPassword, check.password);
    if (!verify)
      return next(APIError.customError("current password is incorrect", 400));
    const hashedPass = hashSync(newPassword, 12);
    const reset = await updateUserPass(req.userId, hashedPass);
    if (!reset) return next(APIError.customError());
    if (reset.error) return next(APIError.customError(reset.error, 400));
    await logOutUser(req.userId);
    logger.info("Login reset successful", { meta: "auth-service" });
    res.clearCookie("jwt");
    res.status(200).json({ success: true, msg: "Password reset successful" });
  } catch (error) {
    next(error);
  }
};

exports.checkUser = (req, res, next) => {
  try {
    let token = req.cookies?.jwt;
    if (!token) token = req.headers?.authorization?.split(" ")[1];
    if (!token) token = req.headers?.cookie?.split("=")[1];
    if (!token) token = req.body.token;
    if (!token) return next(APIError.unauthenticated());
    if (token.includes('"')) token = token.replaceAll('"', '');
    const verify = jwt.verify(token.trim(), config.TOKEN_SECRETE);
    logger.info("Check user successful", { meta: "auth-service" });
    if (verify)
      res.status(200).json({ success: true, msg: "User is Authenticated" });
  } catch (error) {
    if (error.message === ERROR_FIELD.JWT_EXPIRED) next(APIError.customError(ERROR_FIELD.EXPIRED_TOKEN, 403));
    else next(error);
  }
};

exports.deleteAccount = async (req, res, next) => {
  try {
    const { email } = req.query;
    if (!req.userId) return next(APIError.unauthenticated());
    if (!email)
      return next(APIError.badRequest("Email is required to perform delete"));
    if (!isValidEmail(email)) return next(APIError.badRequest("Invalid email"));
    const account = await deleteUser(email);
    if (!account) return next(APIError.customError("No Account found", 404));
    if (account.error) return next(APIError.customError(account.error, 400));
    logger.info("Delete account successful", { meta:"account-service" });
    res
      .status(200)
      .json({ success: true, msg: "Account deleted successfully" });
  } catch (error) {
    next(error);
  }
};
exports.deleteAdminAccount = async (req, res, next) => {
  try {
    const { email } = req.query; 
    if (req.userType !== CONSTANTS.ACCOUNT_TYPE[2]) return next(APIError.unauthorized());
    if (!email)
      return next(APIError.badRequest("Email is required to perform delete"));
    if (!isValidEmail(email)) return next(APIError.badRequest("Invalid email"));
    const account = await deleteUser(email.trim());
    if (!account) return next(APIError.customError("No Account found", 404));
    if (account.error) return next(APIError.customError(account.error, 400));
    logger.info("Delete account successful", { meta:"account-service" });
    res
      .status(200)
      .json({ success: true, msg: "Account deleted successfully" });
  } catch (error) {
    next(error);
  }
};

exports.handleRefreshToken = async (req, res, next) => {
  try{
  let token = req.cookies?.jwt;
  if (!token) token = req.headers?.authorization?.split(" ")[1];
  if (!token) token = req.headers?.cookie?.split("=")[1];
  // let token = req.body.token;
  if (!token || token  === null) return next(APIError.unauthenticated("Access token is required"));
  console.log(token)
  jwt.verify(token, config.TOKEN_SECRETE,(err, decoded) => {
      if(err && err.message !== ERROR_FIELD.JWT_EXPIRED) return next(APIError.unauthenticated("Invalid Access Token"))
  });
  const {refreshToken} = req.body; 
  if (!refreshToken) return next(APIError.unauthenticated("Refresh token is required"));
  res.clearCookie("jwt", {httpOnly:true, sameSite: 'None', secure:true});
  const foundUser = await AccountModel.findOne({refreshToken}).exec();
  // Detected refresh toke reuse
  if (!foundUser) {
    jwt.verify(refreshToken, config.REFRESH_TOKEN_SECRETE, async (err, decoded) => {
      if (err) return next(APIError.customError("Invalid Refresh Token", 403));
      const usedToken = await AccountModel.findOne({_id:decoded.id}).exec();
      usedToken.refreshToken = [];
      usedToken.save();
    });
    logger.info("Token reuse detected", {meta: META.AUTH_SERVICE});
    return next(APIError.customError("Invalid Refresh Token", 403));
  }
  const newRefreshTokenArr = foundUser.refreshToken.filter(rt => rt !== refreshToken);
  //evaluate jwt
  jwt.verify(refreshToken, config.REFRESH_TOKEN_SECRETE, async (err, decoded) => {
    if (err) {
      foundUser.refreshToken = [...newRefreshTokenArr];
      foundUser.save();
    }
    if (err || foundUser._id.toString() !== decoded.id) return next(APIError.customError(ERROR_FIELD.JWT_EXPIRED, 403));
    //Refresh token still valid 
    const payload = {id:foundUser._id, type:foundUser.type};
    const token = jwt.sign(payload, config.TOKEN_SECRETE, {expiresIn:"30m"});
    const newRefreshToken = jwt.sign(payload, config.REFRESH_TOKEN_SECRETE, {expiresIn:"60m"});
    foundUser.refreshToken = [...newRefreshTokenArr, newRefreshToken];
    foundUser.save(); 
    // res.cookie("jwt", token, {httpOnly:true, sameSite: 'None', secure: true});
    res.status(200).json({success:true, token, refreshToken:newRefreshToken});
  });
  }catch(error){
    next(error)
  }
};