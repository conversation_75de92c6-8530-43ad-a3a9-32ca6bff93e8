import React, { useEffect, useState } from "react";

import Axios from "axios";
import { saveAs } from "file-saver";

export default function FileDownloader({ url }, fileName) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const downloadFile = async () => {
      try {
        const response = await Axios({
          url: url,
          method: "GET",
          responseType: "blob", // Specify response type as blob to download binary data
          onDownloadProgress: (progressEvent) => {
            const progress = (progressEvent.loaded / progressEvent.total) * 100;
            setProgress(progress);
          }
        });

        // File download is complete
        const fileData = response.data;
        // Use the downloaded data as needed, e.g., create a Blob, save to local storage, etc.

        saveAs(new Blob([fileData]), fileName);
      } catch (error) {
        // Handle any errors
        ////console.error('Error during file download:', error)
      }
    };

    downloadFile();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [url]); // Run effect whenever the URL changes

  return (
    <div>
      {progress === 100 ? (
        <div>File download complete</div>
      ) : (
        <div>File download progress: {progress}%</div>
      )}
    </div>
  );
}

// Example usage:
<FileDownloader url="https://example.com/myfile.zip" />;
