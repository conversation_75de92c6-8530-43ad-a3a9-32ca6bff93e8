const mongoose = require('mongoose');

const PricingConfigSchema = new mongoose.Schema({
  // Service identification
  serviceType: {
    type: String,
    required: true,
    enum: [
      'article_submission',
      'expedited_review', 
      'open_access_fee',
      'reprints',
      'additional_review',
      'priority_processing',
      'extended_review_time',
      'author_corrections',
      'supplementary_materials'
    ]
  },
  
  serviceName: {
    type: String,
    required: true,
    trim: true
  },
  
  serviceDescription: {
    type: String,
    required: true,
    trim: true
  },

  // Current pricing
  currentPrice: {
    amount: {
      type: Number,
      required: true,
      min: 0,
      default: 0
    },
    currency: {
      type: String,
      required: true,
      enum: ['USD', 'NGN', 'EUR', 'GBP'],
      default: 'USD'
    }
  },

  // Pricing status
  isActive: {
    type: Boolean,
    default: true
  },
  
  isFree: {
    type: Boolean,
    default: true
  },

  // Scheduled pricing changes
  scheduledPricing: [{
    effectiveDate: {
      type: Date,
      required: true
    },
    newPrice: {
      amount: {
        type: Number,
        required: true,
        min: 0
      },
      currency: {
        type: String,
        required: true,
        enum: ['USD', 'NGN', 'EUR', 'GBP']
      }
    },
    reason: {
      type: String,
      trim: true
    },
    isExecuted: {
      type: Boolean,
      default: false
    },
    executedAt: Date,
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  }],

  // Marketing features
  promotionalPricing: {
    isActive: {
      type: Boolean,
      default: false
    },
    discountType: {
      type: String,
      enum: ['percentage', 'fixed_amount', 'free'],
      default: 'percentage'
    },
    discountValue: {
      type: Number,
      min: 0,
      default: 0
    },
    startDate: Date,
    endDate: Date,
    promoCode: {
      type: String,
      trim: true,
      uppercase: true
    },
    usageLimit: {
      type: Number,
      min: 0
    },
    currentUsage: {
      type: Number,
      default: 0
    },
    description: String
  },

  // Regional pricing
  regionalPricing: [{
    region: {
      type: String,
      required: true,
      enum: [
        'north_america', 'europe', 'asia_pacific', 'africa', 
        'south_america', 'middle_east', 'developing_countries'
      ]
    },
    countries: [String],
    price: {
      amount: {
        type: Number,
        required: true,
        min: 0
      },
      currency: {
        type: String,
        required: true,
        enum: ['USD', 'NGN', 'EUR', 'GBP']
      }
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],

  // Bulk pricing (for institutions)
  bulkPricing: [{
    minimumQuantity: {
      type: Number,
      required: true,
      min: 1
    },
    price: {
      amount: {
        type: Number,
        required: true,
        min: 0
      },
      currency: {
        type: String,
        required: true,
        enum: ['USD', 'NGN', 'EUR', 'GBP']
      }
    },
    description: String
  }],

  // Payment processing
  paymentMethods: [{
    type: String,
    enum: ['paystack', 'stripe', 'paypal', 'bank_transfer', 'crypto'],
    default: 'paystack'
  }],

  // Refund policy
  refundPolicy: {
    isRefundable: {
      type: Boolean,
      default: false
    },
    refundPeriodDays: {
      type: Number,
      min: 0,
      default: 0
    },
    refundPercentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    conditions: [String]
  },

  // Analytics and tracking
  analytics: {
    totalRevenue: {
      type: Number,
      default: 0
    },
    totalTransactions: {
      type: Number,
      default: 0
    },
    averageTransactionValue: {
      type: Number,
      default: 0
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },

  // Audit trail
  priceHistory: [{
    previousPrice: {
      amount: Number,
      currency: String
    },
    newPrice: {
      amount: Number,
      currency: String
    },
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    changeReason: String,
    changedAt: {
      type: Date,
      default: Date.now
    },
    effectiveDate: Date
  }],

  // Admin settings
  adminSettings: {
    autoApplyScheduledChanges: {
      type: Boolean,
      default: true
    },
    requireApprovalForChanges: {
      type: Boolean,
      default: false
    },
    notifyUsersOfPriceChanges: {
      type: Boolean,
      default: true
    },
    priceChangeNotificationDays: {
      type: Number,
      default: 7
    }
  },

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for performance
PricingConfigSchema.index({ serviceType: 1 });
PricingConfigSchema.index({ isActive: 1 });
PricingConfigSchema.index({ 'scheduledPricing.effectiveDate': 1 });
PricingConfigSchema.index({ 'promotionalPricing.isActive': 1 });

// Methods
PricingConfigSchema.methods.getCurrentPrice = function(userRegion = null, quantity = 1, promoCode = null) {
  // Check if service is free
  if (this.isFree) {
    return { amount: 0, currency: this.currentPrice.currency, isFree: true };
  }

  let basePrice = this.currentPrice;

  // Check regional pricing
  if (userRegion && this.regionalPricing.length > 0) {
    const regionalPrice = this.regionalPricing.find(rp => 
      rp.isActive && (rp.region === userRegion || rp.countries.includes(userRegion))
    );
    if (regionalPrice) {
      basePrice = regionalPrice.price;
    }
  }

  // Check bulk pricing
  if (quantity > 1 && this.bulkPricing.length > 0) {
    const applicableBulkPrice = this.bulkPricing
      .filter(bp => quantity >= bp.minimumQuantity)
      .sort((a, b) => b.minimumQuantity - a.minimumQuantity)[0];
    
    if (applicableBulkPrice) {
      basePrice = applicableBulkPrice.price;
    }
  }

  // Apply promotional pricing
  if (this.promotionalPricing.isActive && promoCode === this.promotionalPricing.promoCode) {
    const now = new Date();
    if (now >= this.promotionalPricing.startDate && now <= this.promotionalPricing.endDate) {
      if (this.promotionalPricing.usageLimit === 0 || this.promotionalPricing.currentUsage < this.promotionalPricing.usageLimit) {
        if (this.promotionalPricing.discountType === 'free') {
          return { amount: 0, currency: basePrice.currency, isFree: true, isPromotional: true };
        } else if (this.promotionalPricing.discountType === 'percentage') {
          const discountAmount = (basePrice.amount * this.promotionalPricing.discountValue) / 100;
          return { 
            amount: Math.max(0, basePrice.amount - discountAmount), 
            currency: basePrice.currency,
            originalAmount: basePrice.amount,
            discountAmount,
            isPromotional: true
          };
        } else if (this.promotionalPricing.discountType === 'fixed_amount') {
          return { 
            amount: Math.max(0, basePrice.amount - this.promotionalPricing.discountValue), 
            currency: basePrice.currency,
            originalAmount: basePrice.amount,
            discountAmount: this.promotionalPricing.discountValue,
            isPromotional: true
          };
        }
      }
    }
  }

  return basePrice;
};

PricingConfigSchema.methods.scheduleePriceChange = function(newPrice, effectiveDate, reason, adminId) {
  this.scheduledPricing.push({
    effectiveDate,
    newPrice,
    reason,
    createdBy: adminId
  });
  return this.save();
};

PricingConfigSchema.methods.executeScheduledPricing = function() {
  const now = new Date();
  const pendingChanges = this.scheduledPricing.filter(sp => 
    !sp.isExecuted && sp.effectiveDate <= now
  );

  if (pendingChanges.length > 0) {
    // Execute the most recent scheduled change
    const latestChange = pendingChanges.sort((a, b) => b.effectiveDate - a.effectiveDate)[0];
    
    // Add to price history
    this.priceHistory.push({
      previousPrice: this.currentPrice,
      newPrice: latestChange.newPrice,
      changedBy: latestChange.createdBy,
      changeReason: latestChange.reason,
      effectiveDate: latestChange.effectiveDate
    });

    // Update current price
    this.currentPrice = latestChange.newPrice;
    this.isFree = latestChange.newPrice.amount === 0;

    // Mark as executed
    latestChange.isExecuted = true;
    latestChange.executedAt = now;

    return this.save();
  }

  return Promise.resolve(this);
};

// Static methods
PricingConfigSchema.statics.executeAllScheduledPricing = function() {
  return this.find({ 
    'scheduledPricing.isExecuted': false,
    'scheduledPricing.effectiveDate': { $lte: new Date() }
  }).then(configs => {
    return Promise.all(configs.map(config => config.executeScheduledPricing()));
  });
};

module.exports = mongoose.model('PricingConfig', PricingConfigSchema);
