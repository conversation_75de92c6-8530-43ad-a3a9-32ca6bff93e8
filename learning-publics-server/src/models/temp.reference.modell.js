const { model,Schema } = require("mongoose"); 

const TemporalReferenceSchema = new Schema({
  plan: {
    type: Schema.Types.ObjectId,
    ref: "Plan",
    required: true,
    indexed: true,
  },
  account: {
    type: Schema.Types.ObjectId,
    ref: "Account",
    required: true,
    indexed: true,
  },
  reference: {
    type: String,
    required: true,
    indexed: true,
  }
}, {timestamps: true}
);
const TempReferenceModel = model("TemporalReference", TemporalReferenceSchema);
module.exports = TempReferenceModel;