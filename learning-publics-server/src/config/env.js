require("dotenv").config();
const config = {
  DB_URI: process.env.DB_URL,
  LOCAL_DBURL: process.env.LOCAL_DB_URL,
SERVER_PORT: process.env.PORT,
  FRONTEND_ORIGIN_URL: process.env.FRONTEND_ORIGIN_URL,
  TOKEN_SECRETE: process.env.TOKEN_SECRETE,
  REFRESH_TOKEN_SECRETE: process.env.REFRESH_TOKEN_SECRETE,
  NODE_ENV: process.env.NODE_ENV,
  MAIL_USER: process.env.MAIL_USER,
  MAIL_PASS: process.env.MAIL_PASS,
  COMPANY_ABB: process.env.COMPANY_ABB,  
  COMPANY_ADDRESS: process.env.COMPANY_ADDRESS,
  PAYSTACK_SECRETE_KEY: process.env.PAYSTACK_SECRET_KEY,  
  PAYSTACK_CALL_BACK_URL: process.env.PAYSTACK_CALL_BACK_URL,  
};
module.exports = config;
