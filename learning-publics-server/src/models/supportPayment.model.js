const mongoose = require('mongoose');

const supportPaymentSchema = new mongoose.Schema({
  reference: {
    type: String,
    required: true,
    unique: true
  },
  supportTier: {
    type: String,
    required: true,
    enum: [
      'Community Supporter',
      'Research Advocate', 
      'Academic Champion',
      'Institutional Patron',
      'Custom'
    ]
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true,
    default: 'USD'
  },
  userEmail: {
    type: String,
    required: true
  },
  userName: {
    type: String,
    required: false
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Account',
    required: false // Support can come from non-registered users
  },
  recurring: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'cancelled'],
    default: 'pending'
  },
  paymentDate: {
    type: Date,
    default: Date.now
  },
  nextPaymentDate: {
    type: Date,
    required: false // Only for recurring payments
  },
  paystackData: {
    type: mongoose.Schema.Types.Mixed,
    required: false
  },
  impactMetrics: {
    researchersSupported: {
      type: Number,
      default: 0
    },
    articlesSponsored: {
      type: Number,
      default: 0
    },
    countriesReached: {
      type: Number,
      default: 0
    }
  },
  benefits: [{
    type: String
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  cancelledAt: {
    type: Date,
    required: false
  },
  cancelReason: {
    type: String,
    required: false
  },
  notes: {
    type: String,
    required: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes
supportPaymentSchema.index({ reference: 1 });
supportPaymentSchema.index({ userEmail: 1, paymentDate: -1 });
supportPaymentSchema.index({ supportTier: 1, status: 1 });
supportPaymentSchema.index({ recurring: 1, isActive: 1 });

// Virtual for total contribution by user
supportPaymentSchema.virtual('totalContribution').get(function() {
  // This would need to be calculated via aggregation in practice
  return this.amount;
});

// Method to calculate impact based on tier and amount
supportPaymentSchema.methods.calculateImpact = function() {
  const impactRates = {
    'Community Supporter': {
      researchersSupported: 1,
      articlesSponsored: 0.5,
      countriesReached: 2
    },
    'Research Advocate': {
      researchersSupported: 2,
      articlesSponsored: 1,
      countriesReached: 3
    },
    'Academic Champion': {
      researchersSupported: 4,
      articlesSponsored: 2,
      countriesReached: 5
    },
    'Institutional Patron': {
      researchersSupported: 10,
      articlesSponsored: 5,
      countriesReached: 8
    }
  };

  const rates = impactRates[this.supportTier] || impactRates['Community Supporter'];
  
  this.impactMetrics = {
    researchersSupported: Math.floor(rates.researchersSupported * (this.amount / 25)),
    articlesSponsored: Math.floor(rates.articlesSponsored * (this.amount / 25)),
    countriesReached: Math.floor(rates.countriesReached * (this.amount / 25))
  };

  return this.impactMetrics;
};

// Static method to get support statistics
supportPaymentSchema.statics.getSupportStats = async function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        paymentDate: { $gte: startDate, $lte: endDate },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: '$supportTier',
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 },
        avgAmount: { $avg: '$amount' },
        totalResearchers: { $sum: '$impactMetrics.researchersSupported' },
        totalArticles: { $sum: '$impactMetrics.articlesSponsored' }
      }
    }
  ]);
};

// Static method to get recurring supporters
supportPaymentSchema.statics.getRecurringSupport = async function() {
  return this.find({
    recurring: true,
    isActive: true,
    status: 'completed'
  }).sort({ paymentDate: -1 });
};

// Method to cancel recurring support
supportPaymentSchema.methods.cancelRecurring = function(reason) {
  this.isActive = false;
  this.cancelledAt = new Date();
  this.cancelReason = reason;
  return this.save();
};

module.exports = mongoose.model('SupportPayment', supportPaymentSchema);
