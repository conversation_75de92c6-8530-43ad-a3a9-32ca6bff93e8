const pricingRoute = require('express').Router();
const Controller = require('../controllers');
const { adminRequired, notELearningRole } = require('../middlewares/auth.middleware');

// Public routes - Get current pricing
pricingRoute.get('/service/:serviceType', Controller.PricingController.getServicePricing);

// Admin routes - Require authentication and admin role
// Get all pricing configurations
pricingRoute.get('/admin/configs', adminRequired, notELearningRole, Controller.PricingController.getAllPricingConfigs);

// Create new pricing configuration
pricingRoute.post('/admin/configs', adminRequired, notELearningRole, Controller.PricingController.createPricingConfig);

// Update pricing configuration
pricingRoute.put('/admin/configs/:id', adminRequired, notELearningRole, Controller.PricingController.updatePricingConfig);

// Schedule price change
pricingRoute.post('/admin/configs/:id/schedule', adminRequired, notELearningRole, Controller.PricingController.schedulePriceChange);

// Set promotional pricing
pricingRoute.post('/admin/configs/:id/promotion', adminRequired, notELearningRole, Controller.PricingController.setPromotionalPricing);

// Toggle service pricing (free/paid)
pricingRoute.patch('/admin/service/:serviceType/toggle', adminRequired, notELearningRole, Controller.PricingController.toggleServicePricing);

// Execute scheduled pricing changes (can be called manually or by cron)
pricingRoute.post('/admin/execute-scheduled', adminRequired, notELearningRole, Controller.PricingController.executeScheduledPricing);

// Get pricing analytics
pricingRoute.get('/admin/analytics', adminRequired, notELearningRole, Controller.PricingController.getPricingAnalytics);

// Delete pricing configuration (Super Admin only - for now using same middleware)
pricingRoute.delete('/admin/configs/:id', adminRequired, notELearningRole, Controller.PricingController.deletePricingConfig);

module.exports = {
  pricingRoute
};
