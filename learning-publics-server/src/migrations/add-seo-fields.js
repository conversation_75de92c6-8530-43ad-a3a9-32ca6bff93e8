require('dotenv').config();
const mongoose = require('mongoose');
const ArticleModel = require('../models/article.model');
const slugify = require('slugify');

/**
 * Migration script to add SEO fields to existing articles
 * Run this script after updating the article model
 */

const generateSlugFromTitle = (title) => {
  return slugify(title, {
    lower: true,
    strict: true,
    remove: /[*+~.()'"!:@]/g
  });
};

const generateUniqueSlug = async (title, articleId = null) => {
  let baseSlug = generateSlugFromTitle(title);
  let slug = baseSlug;
  let counter = 1;
  
  while (true) {
    const existingArticle = await ArticleModel.findOne({ 
      slug: slug,
      ...(articleId && { _id: { $ne: articleId } })
    });
    
    if (!existingArticle) {
      break;
    }
    
    slug = `${baseSlug}-${counter}`;
    counter++;
  }
  
  return slug;
};

const migrateSEOFields = async () => {
  try {
    console.log('Starting SEO fields migration...');
    
    // Find all articles without slugs
    const articlesWithoutSlugs = await ArticleModel.find({
      $or: [
        { slug: { $exists: false } },
        { slug: null },
        { slug: '' }
      ]
    });
    
    console.log(`Found ${articlesWithoutSlugs.length} articles without slugs`);
    
    let processed = 0;
    let errors = 0;
    
    for (const article of articlesWithoutSlugs) {
      try {
        // Generate unique slug
        const slug = await generateUniqueSlug(article.title, article._id);
        
        // Generate meta description if not exists
        const metaDescription = article.metaDescription || 
          (article.description.length > 160 
            ? article.description.substring(0, 157) + '...'
            : article.description);
        
        // Generate canonical URL
        const canonicalUrl = `${process.env.FRONTEND_URL || 'https://learningpublics.com'}/articles/${slug}`;
        
        // Update article
        await ArticleModel.findByIdAndUpdate(article._id, {
          slug: slug,
          metaDescription: metaDescription,
          canonicalUrl: canonicalUrl,
          viewCount: article.viewCount || 0,
          keywords: article.keywords || [],
          socialShares: article.socialShares || {
            facebook: 0,
            twitter: 0,
            linkedin: 0
          }
        });
        
        processed++;
        console.log(`✓ Processed: ${article.title} -> ${slug}`);
        
      } catch (error) {
        errors++;
        console.error(`✗ Error processing article ${article._id}:`, error.message);
      }
    }
    
    console.log(`\nMigration completed:`);
    console.log(`- Processed: ${processed} articles`);
    console.log(`- Errors: ${errors} articles`);
    
    // Create indexes
    console.log('\nCreating SEO indexes...');
    await ArticleModel.collection.createIndex({ slug: 1 }, { unique: true, sparse: true });
    await ArticleModel.collection.createIndex({ status: 1, createdAt: -1 });
    await ArticleModel.collection.createIndex({ category: 1, status: 1 });
    await ArticleModel.collection.createIndex({ keywords: 1 });
    await ArticleModel.collection.createIndex({ viewCount: -1 });
    await ArticleModel.collection.createIndex({ 
      'title': 'text', 
      'description': 'text', 
      'keywords': 'text' 
    });
    
    console.log('✓ SEO indexes created successfully');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
};

// Run migration if called directly
if (require.main === module) {
  const runMigration = async () => {
    try {
      await mongoose.connect(process.env.DB_URL || 'mongodb://localhost:27017/jms');
      console.log('Connected to MongoDB');
      
      await migrateSEOFields();
      
      console.log('Migration completed successfully');
      process.exit(0);
    } catch (error) {
      console.error('Migration failed:', error);
      process.exit(1);
    }
  };
  
  runMigration();
}

module.exports = { migrateSEOFields };
