import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

const Pagination = ({ 
  currentPage, 
  totalPages, 
  onPageChange,
  showFirstLast = true,
  showPrevNext = true,
  maxVisiblePages = 5,
  className = ""
}) => {
  // Don't render pagination if there's only one page or no pages
  if (totalPages <= 1) return null;

  // Calculate which page numbers to show
  const getVisiblePages = () => {
    const pages = [];
    const halfVisible = Math.floor(maxVisiblePages / 2);
    
    let startPage = Math.max(1, currentPage - halfVisible);
    let endPage = Math.min(totalPages, currentPage + halfVisible);
    
    // Adjust if we're near the beginning or end
    if (endPage - startPage + 1 < maxVisiblePages) {
      if (startPage === 1) {
        endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      } else {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();
  const showLeftEllipsis = visiblePages[0] > 1;
  const showRightEllipsis = visiblePages[visiblePages.length - 1] < totalPages;

  const handlePageClick = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  const baseButtonClass = "relative inline-flex items-center px-3 py-2 text-sm font-medium transition-colors duration-200 focus:z-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2";
  const activeButtonClass = "z-10 bg-blue-600 text-white hover:bg-blue-700";
  const inactiveButtonClass = "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300";
  const disabledButtonClass = "bg-gray-100 text-gray-400 cursor-not-allowed";

  return (
    <nav className={`flex items-center justify-center ${className}`} aria-label="Pagination">
      <div className="flex items-center space-x-1">
        {/* First page button */}
        {showFirstLast && currentPage > 1 && (
          <button
            onClick={() => handlePageClick(1)}
            className={`${baseButtonClass} ${inactiveButtonClass} rounded-l-md`}
            aria-label="Go to first page"
          >
            First
          </button>
        )}

        {/* Previous page button */}
        {showPrevNext && (
          <button
            onClick={() => handlePageClick(currentPage - 1)}
            disabled={currentPage === 1}
            className={`${baseButtonClass} ${
              currentPage === 1 
                ? disabledButtonClass 
                : inactiveButtonClass
            } ${!showFirstLast || currentPage === 1 ? 'rounded-l-md' : ''}`}
            aria-label="Go to previous page"
          >
            <ChevronLeftIcon className="h-4 w-4" />
            <span className="ml-1 hidden sm:inline">Previous</span>
          </button>
        )}

        {/* Left ellipsis */}
        {showLeftEllipsis && (
          <span className="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300">
            ...
          </span>
        )}

        {/* Page number buttons */}
        {visiblePages.map((page) => (
          <button
            key={page}
            onClick={() => handlePageClick(page)}
            className={`${baseButtonClass} ${
              page === currentPage 
                ? activeButtonClass 
                : inactiveButtonClass
            }`}
            aria-label={`Go to page ${page}`}
            aria-current={page === currentPage ? 'page' : undefined}
          >
            {page}
          </button>
        ))}

        {/* Right ellipsis */}
        {showRightEllipsis && (
          <span className="relative inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300">
            ...
          </span>
        )}

        {/* Next page button */}
        {showPrevNext && (
          <button
            onClick={() => handlePageClick(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`${baseButtonClass} ${
              currentPage === totalPages 
                ? disabledButtonClass 
                : inactiveButtonClass
            } ${!showFirstLast || currentPage === totalPages ? 'rounded-r-md' : ''}`}
            aria-label="Go to next page"
          >
            <span className="mr-1 hidden sm:inline">Next</span>
            <ChevronRightIcon className="h-4 w-4" />
          </button>
        )}

        {/* Last page button */}
        {showFirstLast && currentPage < totalPages && (
          <button
            onClick={() => handlePageClick(totalPages)}
            className={`${baseButtonClass} ${inactiveButtonClass} rounded-r-md`}
            aria-label="Go to last page"
          >
            Last
          </button>
        )}
      </div>

      {/* Page info */}
      <div className="ml-4 text-sm text-gray-700 hidden md:block">
        Page {currentPage} of {totalPages}
      </div>
    </nav>
  );
};

export default Pagination;
