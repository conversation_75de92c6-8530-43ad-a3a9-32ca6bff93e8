import { DASHBOARD_COURSES_ROUTE, DASHBOARD_PAYMENT_DETAILS_ROUTE, DASHBOARD_PROFILEROUTE, DASHBOARD_ROUTE, DASHBOARD_SETTINGS_ROUTE } from "routes"
import { ArtPen } from "utils/assets"

export const menuData = [
	{
		icon: ArtPen,
		activeIcon: ArtPen,
		text: "Dashboard",
		isActive: false,
		link: DASHBOARD_ROUTE
	},
	{
		icon: ArtP<PERSON>,
		activeIcon: ArtPen,
		text: "Profile",
		isActive: false,
		link: DASHBOARD_PROFILEROUTE
	},
	{
		icon: Art<PERSON><PERSON>,
		activeIcon: ArtPen,
		text: "Payment Details",
		isActive: false,
		link: DASHBOARD_PAYMENT_DETAILS_ROUTE
	},
	{
		icon: ArtPen,
		activeIcon: ArtPen,
		text: "My Courses",
		isActive: false,
		link: DASHBOARD_COURSES_ROUTE
	},
	{
		icon: Art<PERSON><PERSON>,
		activeIcon: <PERSON><PERSON><PERSON>,
		text: "Setting<PERSON>",
		isActive: false,
		link: DASHBOARD_SETTINGS_ROUTE
	}
]
