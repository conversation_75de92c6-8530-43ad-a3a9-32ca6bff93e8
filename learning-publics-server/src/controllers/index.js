const AccountController = require("./account.controller")
const AuthController = require("./auth.controller");
const CatController = require("./category.controller");
const ArticleController = require("./article.controller");
const CourseController = require("./course.controller");
const PlanController = require("./plan.controller");
const DownloadController = require("./download.controller");
const SitemapController = require("./sitemap.controller");
const PricingController = require("./pricing.controller");
const PaymentController = require("./payment.controller");
module.exports = {
  Account<PERSON>ontroller,
  AuthController,
  CatController,
  ArticleController,
  CourseController,
  PlanController,
  DownloadController,
  SitemapController,
  PricingController,
  PaymentController,
};