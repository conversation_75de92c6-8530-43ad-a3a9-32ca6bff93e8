const express = require('express');
const paymentRoute = express.Router();
const Controller = require('../controllers');
const { userRequired } = require('../middlewares/auth.middleware');

// Payment verification and processing routes
paymentRoute.post('/verify', Controller.PaymentController.verifyPayment);
paymentRoute.post('/create', Controller.PaymentController.createPaymentRecord);

// Support payment processing
paymentRoute.post('/support', Controller.PaymentController.processSupportPayment);

// Journal payment processing (expedited review, open access, etc.)
paymentRoute.post('/journal', Controller.PaymentController.processJournalPayment);

// Free submission processing
paymentRoute.post('/free-submission', Controller.PaymentController.processFreeSubmission);

// Payment history (requires authentication)
paymentRoute.get('/history', userRequired, Controller.PaymentController.getPaymentHistory);

// Webhook for Paystack payment notifications
paymentRoute.post('/webhook/paystack', Controller.PaymentController.paystackWebhook);

module.exports = {
  paymentRoute
};
