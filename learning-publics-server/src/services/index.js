const AccountModule = require("./account.services");
const CategoryModule = require("./category.service");
const ArticleModule = require("./article.service");
const CourseModule = require("./course.service");
const PlanModule = require('./plan.services');
const DownloadModule = require("./download.service");
const UserPlanModule = require("./user.plan.service");
const TempRefModule = require("./temporal.ref.service");

exports.registerUser = async (details) => AccountModule.register(details);
exports.registerTemUser = async(details) => AccountModule.registerTempAccount(details);
exports.updateUserTempToken = async (tokenInfor) => AccountModule.updateTempToken(tokenInfor);
exports.getTemporalUser = async (id, refreshToken, otp) => AccountModule.temporalUser(id, refreshToken, otp);
exports.deleteTempUser = async (id) => AccountModule.removeTempAccount(id);
exports.updateUserTempOTP = async (id, infor) => AccountModule.updateTempOtp(id, infor);
exports.removeUserTempToken = async (infor) => AccountModule.deleteTempToken(infor);
exports.createAdmin = async (details) => AccountModule.create(details);
exports.defaultAccount = async (details) => AccountModule.defaultRegistration(details);
exports.emailExist = async (email) => AccountModule.checkEmail(email);
exports.usernameExist = async (username) => AccountModule.checkUsername(username);
exports.logOutUser = async (id) => AccountModule.logOut(id);
exports.updateUserToken = async (id, refreshToken,token) => AccountModule.updateToken(id, refreshToken,token);
exports.getUserById = async (id) => AccountModule.checkById(id);
exports.updateUserPass = async (id, password) => AccountModule.upatePassword(id, password);
exports.getAllAccounts = async () => AccountModule.userAccounts();
exports.deleteUser = async (email) => AccountModule.removeAccount(email);
exports.userExistByIdAndEmail = async (id, email) => AccountModule.checkByIdAndEmail(id, email);
exports.passwordRecovery = async (infor) => AccountModule.passwordRecoveryInfor(infor);
exports.getPasswordRecoveryInfor = async (id) => AccountModule.getRecoveryInfor(id);
exports.removePasswordRecoveryInfor = async (id) => AccountModule.deleteRecoveryInfor(id);
exports.updateUserPass = async (id, password) => AccountModule.resetPassword(id, password);
exports.updateUserInfor = async (id, details) => AccountModule.updateAccount(id, details);
exports.getAdmins = async () => AccountModule.getAdmin();
exports.getAdminsByRole = async (role) => AccountModule.adminByRole(role);
exports.getAuditorInfo = async (auditors) => AccountModule.auditors(auditors);
//Category section
exports.createCategory = async (details) => CategoryModule.create(details);
exports.categoryUpdate = async (details) => CategoryModule.update(details);
exports.categorydelete = async (id) => CategoryModule.delete(id);
exports.getCategory = async () => CategoryModule.all();
exports.findCategoryById = async (id) => CategoryModule.findById(id);

//Article section
exports.articleUpload = async (article) => ArticleModule.authorArticle(article);
exports.getArticles = async (userRole, userId) => ArticleModule.articles(userRole, userId);
exports.getArticlesByCategory = async (category) => ArticleModule.articlesByCategory(category);
exports.getArticlesByTitle = async (text) => ArticleModule.articlesByTitle(text);
exports.getArticlesById = async (id,userId, type) => ArticleModule.articlesId(id, userId, type);
exports.getArticlesPublishedById = async (id) => ArticleModule.articlesPublishedById(id);
exports.updateArticleStatus = async (id, info) => ArticleModule.articleStatusUpdate(id, info);
exports.deleteArticleById = async (id) => ArticleModule.articleDeleteById(id);
exports.updateArticle = async (id, infor) => ArticleModule.articleUpdate(id, infor);
exports.getArticlesInReview = async (status) => ArticleModule.reviewArticles(status);
exports.verifyArticleResubmit = async (articleId) => ArticleModule.verifyResubmission(articleId);
exports.assignArticle = async (id, editor) => ArticleModule.assign(id, editor);

// New SEO-friendly article methods
exports.getArticleBySlug = async (slug) => ArticleModule.findBySlug(slug);
exports.getArticlesSEOOptimized = async (options) => ArticleModule.getArticlesSEOOptimized(options);
exports.searchArticles = async (searchTerm, options) => ArticleModule.searchArticles(searchTerm, options);
exports.getArticlesByCategorySlug = async (categorySlug) => ArticleModule.articlesByCategorySlug(categorySlug);
exports.trackArticleView = async (slug) => ArticleModule.trackArticleView(slug);
exports.trackSocialShare = async (slug, platform) => ArticleModule.trackSocialShare(slug, platform);
exports.getRelatedArticles = async (categoryId, excludeId, limit) => ArticleModule.getRelatedArticles(categoryId, excludeId, limit);
exports.getPublishedArticles = async () => ArticleModule.getPublishedArticles();
exports.getCategories = async () => ArticleModule.getCategories();
exports.getArticlesByCategorySlug = async (categorySlug) => ArticleModule.getArticlesByCategorySlug(categorySlug);

// Courses Section
exports.addCourses = async (details) => CourseModule.create(details);
exports.getCourse = async () => CourseModule.course();
exports.deleteCourseById = async (id) => CourseModule.delete(id);
exports.updateCourse = async (id, details) => CourseModule.update(id, details);
exports.getCourseById = async (id) => CourseModule.courseByID(id);
exports.getCourseNotInPlan = async () => CourseModule.notInPlan();

// Plan Section
exports.createPlan = async(details) => PlanModule.create(details);
exports.getPlanById = async (id) => PlanModule.planById(id);  
exports.getPlans = async () => PlanModule.plans();
exports.deletePlan = async (id) => PlanModule.delete(id); 
exports.updatePlan = async(planId, infor) => PlanModule.update(planId, infor);
exports.removeCourse = async(planId, infor) => PlanModule.removeCourse(planId, infor);
exports.addCourse = async(planId, infor) => PlanModule.addCourse(planId, infor);
exports.getPlanByType = async (type) => PlanModule.planByType(type);  
exports.updatePlanView = async (id, details, courses) => PlanModule.updateView(id, details, courses);  

// Download Section
exports.createDownload = async (details) => DownloadModule.create(details);
exports.getDownloads = async (userId) => DownloadModule.downloads(userId);
exports.getDownloadByArticleId = async (account,articleId) => DownloadModule.findByArticleId(account,articleId);
exports.deleteDownload = async (admin, id) =>  DownloadModule.delete(admin, id);
  
// User Plan Section
exports.getUserPlanById = async (planId,userId) => UserPlanModule.planById(planId,userId);
exports.getUserPlans = async (userId) => UserPlanModule.userPlans(userId);
exports.createUserPlan = async (details) => UserPlanModule.createPlan(details);
exports.getUserPlanByReference = async(account,reference) => UserPlanModule.planByReference(account,reference);
exports.updateUserPlanView = async (id, details, courses) => UserPlanModule.updateView(id, details, courses);  
// Temporal Reference Section
exports.generateTempRef = async (details) => TempRefModule.create(details);
exports.getTempReference = async (reference) => TempRefModule.findByReference(reference);
exports.deleteTempReference = async (reference) => TempRefModule.deleteByReference(reference);
 
