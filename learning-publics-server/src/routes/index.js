const express = require("express");
const router = express.Router();
const AuthModule = require("./auth.route");
const UserModule = require("./user.route");
const CategoryModule = require("./category.route");
const ArticleModule = require("./article.route");
const courseModule = require("./course.route");
const PlanModule = require("./plan.route");
const DownloadModule = require("./download.route");
const SitemapModule = require("./sitemap.route");
const PricingModule = require("./pricing.routes");
const PaymentModule = require("./payment.routes");
router.use("/user", UserModule.userRoute);
router.use("/auth", AuthModule.authRoute);
router.use("/category", CategoryModule.categoryRoute);
router.use("/author", ArticleModule.articleRoute);
router.use("/course", courseModule.courseRoute);
router.use("/plan", PlanModule.planRoute);
router.use("/download", DownloadModule.downloadRoute);
router.use("/pricing", PricingModule.pricingRoute);
router.use("/payment", PaymentModule.paymentRoute);

module.exports = router;
