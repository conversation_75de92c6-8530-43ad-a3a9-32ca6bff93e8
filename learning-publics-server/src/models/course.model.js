const { Schema, model } = require("mongoose");
const { CONSTANTS } = require("../config");

const CourseSchema = new Schema({
  admin: {
    type: Schema.Types.ObjectId,
    ref: "account",
    required: true,
    indexed: true,
  },
  type: {
    type: String,
    required: true,
    trim: true,
    enum: CONSTANTS.COURSE_TYPE,
    indexed: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
    indexed: true,
  },
  category: {
    type: String,
    required: true,
    indexed: true,
    trim: true,
    enum: CONSTANTS.COURSE_CATEGORY,
  },
  resourceUrl: {
    type: String,
    trim: true,
  },
  resourceId: {
    type: String,
    trim: true,
  },
  completed: {
    type: Boolean,
    require: true,
    default: false,
  }
}, 
{timestamps: true}
);
const CourseModel = model("course", CourseSchema);
module.exports = CourseModel;