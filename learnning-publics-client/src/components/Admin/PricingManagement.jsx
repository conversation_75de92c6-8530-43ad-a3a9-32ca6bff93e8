import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';
import { toast } from 'react-hot-toast';
import Colors from 'utils/colors';
import jmsApp from 'api/jms';

const PricingContainer = styled.div`
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 2rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
`;

const Title = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  color: ${Colors.primary};
`;

const ActionButton = styled.button`
  background: linear-gradient(135deg, ${Colors.primary}, #5a6a72);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
`;

const ServiceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
`;

const ServiceCard = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
  }
`;

const ServiceHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const ServiceName = styled.h3`
  font-size: 1.3rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 0.5rem;
`;

const ServiceType = styled.span`
  background: #f3f4f6;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
`;

const ServiceDescription = styled.p`
  color: #64748b;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
`;

const PriceDisplay = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
`;

const CurrentPrice = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${props => props.isFree ? '#16a34a' : Colors.secondary};
`;

const PriceStatus = styled.span`
  background: ${props => props.isFree ? '#dcfce7' : '#fef3c7'};
  color: ${props => props.isFree ? '#15803d' : '#92400e'};
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
`;

const SmallButton = styled.button`
  background: ${props => props.variant === 'danger' ? '#dc2626' : 
                     props.variant === 'warning' ? '#f59e0b' : 
                     props.variant === 'success' ? '#16a34a' : Colors.primary};
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
`;

const ModalTitle = styled.h2`
  font-size: 1.5rem;
  font-weight: 600;
  color: ${Colors.primary};
  margin-bottom: 1.5rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const Label = styled.label`
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
`;

const Input = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: ${Colors.primary};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  background: white;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: ${Colors.primary};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  min-height: 100px;
  resize: vertical;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: ${Colors.primary};
  }
`;

const ToggleSwitch = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const Switch = styled.button`
  width: 3rem;
  height: 1.5rem;
  border-radius: 0.75rem;
  border: none;
  background: ${props => props.active ? Colors.primary : '#d1d5db'};
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &::after {
    content: '';
    position: absolute;
    top: 0.125rem;
    left: ${props => props.active ? '1.375rem' : '0.125rem'};
    width: 1.25rem;
    height: 1.25rem;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
  }
`;

const PricingManagement = () => {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(''); // 'edit', 'schedule', 'promotion'
  const [selectedService, setSelectedService] = useState(null);
  const [formData, setFormData] = useState({});

  const serviceTypes = [
    { value: 'article_submission', label: 'Article Submission' },
    { value: 'expedited_review', label: 'Expedited Review' },
    { value: 'open_access_fee', label: 'Open Access Fee' },
    { value: 'reprints', label: 'Reprints' },
    { value: 'additional_review', label: 'Additional Review' },
    { value: 'priority_processing', label: 'Priority Processing' }
  ];

  useEffect(() => {
    fetchPricingConfigs();
  }, []);

  const fetchPricingConfigs = async () => {
    try {
      setLoading(true);
      const response = await jmsApp.get('/pricing/admin/configs');
      if (response.data.success) {
        setServices(response.data.data.pricingConfigs);
      }
    } catch (error) {
      console.error('Error fetching pricing configs:', error);
      toast.error('Failed to load pricing configurations');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleService = async (serviceType, currentStatus) => {
    try {
      const response = await jmsApp.patch(`/pricing/admin/service/${serviceType}/toggle`, {
        isFree: !currentStatus,
        reason: `Service ${!currentStatus ? 'disabled' : 'enabled'} by admin`
      });

      if (response.data.success) {
        toast.success(`Service ${!currentStatus ? 'disabled' : 'enabled'} successfully`);
        fetchPricingConfigs();
      }
    } catch (error) {
      console.error('Error toggling service:', error);
      toast.error('Failed to update service status');
    }
  };

  const handleEditPrice = (service) => {
    setSelectedService(service);
    setModalType('edit');
    setFormData({
      amount: service.currentPrice.amount,
      currency: service.currentPrice.currency,
      changeReason: ''
    });
    setShowModal(true);
  };

  const handleScheduleChange = (service) => {
    setSelectedService(service);
    setModalType('schedule');
    setFormData({
      amount: service.currentPrice.amount,
      currency: service.currentPrice.currency,
      effectiveDate: '',
      reason: ''
    });
    setShowModal(true);
  };

  const handleSetPromotion = (service) => {
    setSelectedService(service);
    setModalType('promotion');
    setFormData({
      discountType: 'percentage',
      discountValue: 0,
      startDate: '',
      endDate: '',
      promoCode: '',
      usageLimit: 0,
      description: ''
    });
    setShowModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      let response;
      
      if (modalType === 'edit') {
        response = await jmsApp.put(`/pricing/admin/configs/${selectedService._id}`, {
          currentPrice: {
            amount: parseFloat(formData.amount),
            currency: formData.currency
          },
          isFree: parseFloat(formData.amount) === 0,
          changeReason: formData.changeReason
        });
      } else if (modalType === 'schedule') {
        response = await jmsApp.post(`/pricing/admin/configs/${selectedService._id}/schedule`, {
          newPrice: {
            amount: parseFloat(formData.amount),
            currency: formData.currency
          },
          effectiveDate: formData.effectiveDate,
          reason: formData.reason
        });
      } else if (modalType === 'promotion') {
        response = await jmsApp.post(`/pricing/admin/configs/${selectedService._id}/promotion`, formData);
      }

      if (response.data.success) {
        toast.success(`${modalType === 'edit' ? 'Price updated' : 
                      modalType === 'schedule' ? 'Price change scheduled' : 
                      'Promotion set'} successfully`);
        setShowModal(false);
        fetchPricingConfigs();
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Failed to save changes');
    }
  };

  const formatCurrency = (amount, currency) => {
    if (amount === 0) return 'FREE';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <PricingContainer>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading pricing configurations...</p>
        </div>
      </PricingContainer>
    );
  }

  return (
    <PricingContainer>
      <Header>
        <Title>Pricing Management</Title>
        <ActionButton onClick={() => {/* Handle create new service */}}>
          Add New Service
        </ActionButton>
      </Header>

      <ServiceGrid>
        {services.map((service) => (
          <motion.div
            key={service._id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ServiceCard>
              <ServiceHeader>
                <div>
                  <ServiceName>{service.serviceName}</ServiceName>
                  <ServiceType>{service.serviceType}</ServiceType>
                </div>
                <ToggleSwitch>
                  <span>Active</span>
                  <Switch 
                    active={service.isActive}
                    onClick={() => handleToggleService(service.serviceType, service.isFree)}
                  />
                </ToggleSwitch>
              </ServiceHeader>

              <ServiceDescription>{service.serviceDescription}</ServiceDescription>

              <PriceDisplay>
                <CurrentPrice isFree={service.isFree}>
                  {formatCurrency(service.currentPrice.amount, service.currentPrice.currency)}
                </CurrentPrice>
                <PriceStatus isFree={service.isFree}>
                  {service.isFree ? 'FREE' : 'PAID'}
                </PriceStatus>
              </PriceDisplay>

              <ActionButtons>
                <SmallButton onClick={() => handleEditPrice(service)}>
                  Edit Price
                </SmallButton>
                <SmallButton 
                  variant="warning" 
                  onClick={() => handleScheduleChange(service)}
                >
                  Schedule Change
                </SmallButton>
                <SmallButton 
                  variant="success" 
                  onClick={() => handleSetPromotion(service)}
                >
                  Set Promotion
                </SmallButton>
              </ActionButtons>
            </ServiceCard>
          </motion.div>
        ))}
      </ServiceGrid>

      <AnimatePresence>
        {showModal && (
          <Modal onClick={() => setShowModal(false)}>
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              onClick={(e) => e.stopPropagation()}
            >
              <ModalContent>
                <ModalTitle>
                  {modalType === 'edit' ? 'Edit Price' :
                   modalType === 'schedule' ? 'Schedule Price Change' :
                   'Set Promotion'}
                </ModalTitle>

                <form onSubmit={handleSubmit}>
                  {(modalType === 'edit' || modalType === 'schedule') && (
                    <>
                      <FormGroup>
                        <Label>Amount</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData.amount}
                          onChange={(e) => setFormData({...formData, amount: e.target.value})}
                          required
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Currency</Label>
                        <Select
                          value={formData.currency}
                          onChange={(e) => setFormData({...formData, currency: e.target.value})}
                          required
                        >
                          <option value="USD">USD</option>
                          <option value="NGN">NGN</option>
                          <option value="EUR">EUR</option>
                          <option value="GBP">GBP</option>
                        </Select>
                      </FormGroup>

                      {modalType === 'schedule' && (
                        <FormGroup>
                          <Label>Effective Date</Label>
                          <Input
                            type="datetime-local"
                            value={formData.effectiveDate}
                            onChange={(e) => setFormData({...formData, effectiveDate: e.target.value})}
                            required
                          />
                        </FormGroup>
                      )}

                      <FormGroup>
                        <Label>Reason</Label>
                        <TextArea
                          value={modalType === 'edit' ? formData.changeReason : formData.reason}
                          onChange={(e) => setFormData({
                            ...formData, 
                            [modalType === 'edit' ? 'changeReason' : 'reason']: e.target.value
                          })}
                          placeholder="Reason for this change..."
                        />
                      </FormGroup>
                    </>
                  )}

                  {modalType === 'promotion' && (
                    <>
                      <FormGroup>
                        <Label>Discount Type</Label>
                        <Select
                          value={formData.discountType}
                          onChange={(e) => setFormData({...formData, discountType: e.target.value})}
                          required
                        >
                          <option value="percentage">Percentage</option>
                          <option value="fixed_amount">Fixed Amount</option>
                          <option value="free">Free</option>
                        </Select>
                      </FormGroup>

                      {formData.discountType !== 'free' && (
                        <FormGroup>
                          <Label>Discount Value</Label>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            value={formData.discountValue}
                            onChange={(e) => setFormData({...formData, discountValue: e.target.value})}
                            required
                          />
                        </FormGroup>
                      )}

                      <FormGroup>
                        <Label>Start Date</Label>
                        <Input
                          type="datetime-local"
                          value={formData.startDate}
                          onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                          required
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>End Date</Label>
                        <Input
                          type="datetime-local"
                          value={formData.endDate}
                          onChange={(e) => setFormData({...formData, endDate: e.target.value})}
                          required
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Promo Code</Label>
                        <Input
                          type="text"
                          value={formData.promoCode}
                          onChange={(e) => setFormData({...formData, promoCode: e.target.value.toUpperCase()})}
                          placeholder="PROMO2024"
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Usage Limit (0 = unlimited)</Label>
                        <Input
                          type="number"
                          min="0"
                          value={formData.usageLimit}
                          onChange={(e) => setFormData({...formData, usageLimit: e.target.value})}
                        />
                      </FormGroup>

                      <FormGroup>
                        <Label>Description</Label>
                        <TextArea
                          value={formData.description}
                          onChange={(e) => setFormData({...formData, description: e.target.value})}
                          placeholder="Promotion description..."
                        />
                      </FormGroup>
                    </>
                  )}

                  <ActionButtons>
                    <SmallButton type="submit">
                      {modalType === 'edit' ? 'Update Price' :
                       modalType === 'schedule' ? 'Schedule Change' :
                       'Set Promotion'}
                    </SmallButton>
                    <SmallButton 
                      type="button" 
                      variant="danger" 
                      onClick={() => setShowModal(false)}
                    >
                      Cancel
                    </SmallButton>
                  </ActionButtons>
                </form>
              </ModalContent>
            </motion.div>
          </Modal>
        )}
      </AnimatePresence>
    </PricingContainer>
  );
};

export default PricingManagement;
