const { default: mongoose } = require("mongoose");
const { CONSTANTS } = require("../config");
const AccountModel = require("../models/account.model");
const ArticleModel = require("../models/article.model");
const CategoryModel = require("../models/category.model");

exports.authorArticle = async (article) => {
  try {
    if(article.articleId) return await ArticleModel.findOneAndUpdate({articleId: article.articleId}, {...article}, {returnOriginal: false}).exec();
   return await ArticleModel.create({...article})
  } catch (error) {
    return {error};
  }
};
exports.articles = async (type, userId) => {
  try {
    if(type.toLowerCase() === CONSTANTS.ACCOUNT_TYPE[2])
     return await ArticleModel.find({})
       .populate('category', 'name slug description')
       .sort({updatedAt: -1, createdAt: -1 }).exec();
    else{
      return await ArticleModel.find({"editors.id": mongoose.Types.ObjectId(userId), status:{$ne:CONSTANTS.ARTICLE_STATUS[4]}})
        .populate('category', 'name slug description')
        .sort({updatedAt: -1, createdAt: -1}).exec();

    }
  } catch (error) {
    return {error};
  }
};
exports.articlesByCategory = async (name) => {
  try {
    const cat = await CategoryModel.findOne({name}).exec();
    if(!cat)return {error:"Category does not exist"};
    return await ArticleModel.find(
      {
        $and:[
          { category:cat._id},
          {
            status:CONSTANTS.ARTICLE_STATUS[4]
          }
        ]
      },"-editors -comment")
      .populate('category', 'name slug description')
      .sort({createdAt: -1, updatedAt: -1}).exec();
  } catch (error) {
    return {error};
  }
};
exports.articlesByTitle = async (text) => {
  try {
    return await ArticleModel.find(
     { $and:[
      {
        $or:[
          {title: { $regex: new RegExp(text, 'i') }},
          {description: { $regex: new RegExp(text, 'i') }}
        ],
      },{status:{$regex: new RegExp (CONSTANTS.ARTICLE_STATUS[4], 'i')}}
     ] }, "-editors -comment").populate([{
      path: "admin",
      model: "Account",
      select: ["name"]
  }]).populate([{
    path: "category",
    model: "Categorie",
    select: ["name"]
  }]).sort({updatedAt: -1, createdAt: -1 }).exec(); 
  } catch (error) {
    return {error};
  }
};
exports.articlesId = async (id, userId, type) => {
  try {
    if(type.toLowerCase() === CONSTANTS.ACCOUNT_TYPE[2])
    return await ArticleModel.findOne(
      {
          $or:[
          {_id:id}, 
          {articleId:id}
        ]
      }
    ).exec();
   else{
    return await ArticleModel.findOne({$and:[
      {
          $or:[
          {_id:id}, 
          {articleId:id}
        ]
      },{"editors.id": mongoose.Types.ObjectId(userId)}
    ]}).exec();

   }
     
      
  } catch (error) {
    return {error};
  }
};
exports.articlesPublishedById = async (id) => {
  try {
      return await ArticleModel.findOne({$and:[
        {
            $or:[
            {_id:id}, 
            {articleId:id}
          ]
        },{status: CONSTANTS.ARTICLE_STATUS[4]}
      ]}).sort({updatedAt: -1, createdAt: -1}).exec();
      
  } catch (error) {
    return {error};
  }
};
exports.articleStatusUpdate = async (id,info) => {
  try {
    return await ArticleModel.findOneAndUpdate({_id:id}, {...info}, {returnOriginal: false});
  } catch (error) {
    return {error};
  }
};
exports.articleUpdate = async (id,info) => {
  try {
 const article = await ArticleModel.findOne({_id:id});

 if (!article) {
   return {error: 'Article not found'};
 }

 console.log('Before update:', {
   articleUrl: article.articleUrl,
   publicId: article.publicId,
   status: article.status
 });

 console.log('Update info:', info);

 // Update status if provided
 if(info.status) article.status = info.status;

 // Update editors if provided
 if(info.editors) article.editors = [...info.editors];

 // Update main article file URL if provided (for file reuploads)
 if(info.articleUrl) article.articleUrl = info.articleUrl;

 // Update Cloudinary public ID if provided
 if(info.publicId) article.publicId = info.publicId;

 // Update format if provided
 if(info.format) article.format = info.format;

  // if(info.comment) article.comment = [...info.comment];

  const savedArticle = await article.save();

  console.log('After update:', {
    articleUrl: savedArticle.articleUrl,
    publicId: savedArticle.publicId,
    status: savedArticle.status
  });

  return savedArticle;

  } catch (error) {
    console.error('Article update error:', error);
    return {error};
  }
};
exports.articleDeleteById = async (id) => {
  try {
    return await ArticleModel.findByIdAndDelete({_id:id});
  } catch (error) {
    return {error};
  }
};

exports.reviewArticles = async(status) => {
  try {
    return await ArticleModel.find({status}, "-editors -comment").sort({createdAt: -1, updatedAt: -1}).exec();
  } catch (error) {
    return {error};
  }
}

exports.verifyResubmission = async (articleId) => {
  try {
    return await ArticleModel.findOne({articleId}).exec();
  } catch (error) {
    return {error};
  }
}

exports.assign = async(id, editors) => {
  try {
      const article = await ArticleModel.findOne({_id:id});
      article.status = CONSTANTS.ARTICLE_STATUS[5];
      article.editors = [...editors];
       article.save();
       return article;
  } catch (error) {
      return { error }
  }
}

// New SEO-friendly methods
exports.findBySlug = async (slug) => {
  try {
    return await ArticleModel.findBySlug(slug);
  } catch (error) {
    return { error };
  }
};

exports.getArticlesSEOOptimized = async (options = {}) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      author,
      status = CONSTANTS.ARTICLE_STATUS[4],
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom,
      dateTo
    } = options;

    const query = { status };

    // Category filter
    if (category) {
      const cat = await CategoryModel.findOne({ slug: category }).exec();
      if (cat) {
        query.category = cat._id;
      }
    }

    // Author filter
    if (author) {
      query.author = { $regex: new RegExp(author, 'i') };
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) {
        query.createdAt.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        query.createdAt.$lte = new Date(dateTo);
      }
    }

    const skip = (page - 1) * limit;

    // Build sort object
    const sortObj = {};
    if (sortBy === 'title') {
      sortObj.title = sortOrder === 'asc' ? 1 : -1;
    } else if (sortBy === 'author') {
      sortObj.author = sortOrder === 'asc' ? 1 : -1;
    } else if (sortBy === 'viewCount') {
      sortObj.viewCount = sortOrder === 'asc' ? 1 : -1;
    } else {
      sortObj.createdAt = sortOrder === 'asc' ? 1 : -1;
    }

    const articles = await ArticleModel.find(query, "-editors -comment")
      .populate([{
        path: "category",
        model: "Categorie",
        select: ["name", "slug"]
      }])
      .sort(sortObj)
      .skip(skip)
      .limit(parseInt(limit))
      .exec();

    const total = await ArticleModel.countDocuments(query);
    const pages = Math.ceil(total / limit);

    return {
      articles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    return { error };
  }
};

exports.searchArticles = async (searchTerm, options = {}) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      author,
      sortBy = 'relevance',
      sortOrder = 'desc',
      dateFrom,
      dateTo
    } = options;
    const skip = (page - 1) * limit;

    const searchQuery = {
      $and: [
        {
          $or: [
            { title: { $regex: new RegExp(searchTerm, 'i') } },
            { description: { $regex: new RegExp(searchTerm, 'i') } },
            { keywords: { $in: [new RegExp(searchTerm, 'i')] } },
            { author: { $regex: new RegExp(searchTerm, 'i') } }
          ]
        },
        { status: CONSTANTS.ARTICLE_STATUS[4] }
      ]
    };

    // Add additional filters
    if (category) {
      const cat = await CategoryModel.findOne({ slug: category }).exec();
      if (cat) {
        searchQuery.$and.push({ category: cat._id });
      }
    }

    if (author) {
      searchQuery.$and.push({ author: { $regex: new RegExp(author, 'i') } });
    }

    // Date range filter
    if (dateFrom || dateTo) {
      const dateFilter = {};
      if (dateFrom) {
        dateFilter.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        dateFilter.$lte = new Date(dateTo);
      }
      searchQuery.$and.push({ createdAt: dateFilter });
    }

    // Build sort object
    const sortObj = {};
    if (sortBy === 'title') {
      sortObj.title = sortOrder === 'asc' ? 1 : -1;
    } else if (sortBy === 'author') {
      sortObj.author = sortOrder === 'asc' ? 1 : -1;
    } else if (sortBy === 'viewCount') {
      sortObj.viewCount = sortOrder === 'asc' ? 1 : -1;
    } else {
      // Default to date for relevance
      sortObj.createdAt = sortOrder === 'asc' ? 1 : -1;
    }

    const articles = await ArticleModel.find(searchQuery, "-editors -comment")
      .populate([{
        path: "category",
        model: "Categorie",
        select: ["name", "slug"]
      }])
      .sort(sortObj)
      .skip(skip)
      .limit(parseInt(limit))
      .exec();

    const total = await ArticleModel.countDocuments(searchQuery);
    const pages = Math.ceil(total / limit);

    return {
      articles,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    return { error };
  }
};

exports.articlesByCategorySlug = async (categorySlug) => {
  try {
    const category = await CategoryModel.findBySlug(categorySlug);
    if (!category) return { error: "Category does not exist" };

    const articles = await ArticleModel.find({
      $and: [
        { category: category._id },
        { status: CONSTANTS.ARTICLE_STATUS[4] }
      ]
    }, "-editors -comment")
      .populate([{
        path: "category",
        model: "Categorie",
        select: ["name", "slug", "description", "metaDescription"]
      }])
      .sort({ createdAt: -1, updatedAt: -1 })
      .exec();

    return { articles, category };
  } catch (error) {
    return { error };
  }
};

exports.trackArticleView = async (slug) => {
  try {
    const article = await ArticleModel.findBySlug(slug);
    if (!article) return { error: "Article not found" };

    // Use atomic update to prevent version conflicts
    const updatedArticle = await article.incrementViewCount();
    if (!updatedArticle) return { error: "Failed to update view count" };

    return { success: true };
  } catch (error) {
    console.error('Error tracking article view:', error);
    return { error: error.message || "Failed to track view" };
  }
};

exports.trackSocialShare = async (slug, platform) => {
  try {
    const article = await ArticleModel.findBySlug(slug);
    if (!article) return { error: "Article not found" };

    // Use atomic update to prevent version conflicts
    const updateQuery = {};
    updateQuery[`socialShares.${platform}`] = 1;

    const updatedArticle = await ArticleModel.findByIdAndUpdate(
      article._id,
      { $inc: updateQuery },
      { new: true, upsert: false }
    );

    if (!updatedArticle) return { error: "Failed to update share count" };

    return { success: true };
  } catch (error) {
    console.error('Error tracking social share:', error);
    return { error: error.message || "Failed to track share" };
  }
};

exports.getRelatedArticles = async (categoryId, excludeId, limit = 4) => {
  try {
    const articles = await ArticleModel.find({
      $and: [
        { category: categoryId },
        { status: CONSTANTS.ARTICLE_STATUS[4] },
        { _id: { $ne: excludeId } }
      ]
    }, "-editors -comment")
      .populate([{
        path: "category",
        model: "Categorie",
        select: ["name", "slug"]
      }])
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .exec();

    return { articles };
  } catch (error) {
    return { error };
  }
};

// Get all published articles for sitemap
exports.getPublishedArticles = async () => {
  try {
    return await ArticleModel.find({
      status: CONSTANTS.ARTICLE_STATUS[4] // Published
    })
    .select('title slug description author createdAt updatedAt keywords category')
    .populate('category', 'name slug')
    .sort({ createdAt: -1 })
    .exec();
  } catch (error) {
    return { error: error.message };
  }
};

// Get all categories for sitemap
exports.getCategories = async () => {
  try {
    return await CategoryModel.find({})
    .select('name slug description')
    .sort({ name: 1 })
    .exec();
  } catch (error) {
    return { error: error.message };
  }
};

// Get articles by category slug
exports.getArticlesByCategorySlug = async (categorySlug) => {
  try {
    // First find the category by slug
    const category = await CategoryModel.findOne({
      slug: categorySlug
    }).exec();

    if (!category) {
      // Return empty result instead of error for non-existent categories
      return {
        articles: [],
        category: {
          name: categorySlug.charAt(0).toUpperCase() + categorySlug.slice(1),
          slug: categorySlug,
          description: `Articles in ${categorySlug} category`
        }
      };
    }

    // Then find articles in that category
    const articles = await ArticleModel.find({
      category: category._id,
      status: CONSTANTS.ARTICLE_STATUS[4] // Published
    })
    .populate('category', 'name slug description')
    .sort({ createdAt: -1 })
    .exec();

    return {
      articles: articles || [],
      category: {
        _id: category._id,
        name: category.name,
        slug: category.slug,
        description: category.description
      }
    };
  } catch (error) {
    return { error: error.message };
  }
};
