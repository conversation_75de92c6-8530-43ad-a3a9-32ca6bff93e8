const TempReferenceModel = require("../models/temp.reference.modell");
const UserPlanModel = require("../models/user.plan.model");

exports.planById = async (planId, account) => {
  try{
      return await UserPlanModel.findOne({plan:planId,account:account}).exec();
  }catch(error){
      return {error};
  }
}
exports.userPlans = async (account) => {
  try{
   const plan = await UserPlanModel.find({account:account} ).exec();  
   
return plan
// .populate([{
//   path: "plan",
//   model: "Plan",
//   select: [ "major", "type", "price", "description","view", "courses"],
// }])
  }catch(error){
      return {error};
  }
}
exports.createPlan = async (details) => {
  try{
    let plan;  
      plan = await UserPlanModel.create({...details});
      plan.planInfo = [...details.info];
      plan.save();
      await TempReferenceModel.findOneAndDelete({reference: details.reference}).exec();
    return plan
  }catch(error){
    return {error};
  }
}
exports.planByReference = async (account, reference) => {
  try{
      return await UserPlanModel.findOne({account, reference}).exec();
  }catch(error){
      return {error};
  }
}

exports.updateView = async(id, details, courses) => {
  try {  
      const plan = await UserPlanModel.findOne({_id:id}); 
      const updateInfo = plan.planInfo[0];
      updateInfo.courses = [...courses];
      updateInfo.view = [details];
      plan.planInfo.pop();
      plan.planInfo = updateInfo
   return   plan.save(); 
  } catch (error) {
      return { error }
  }
}
