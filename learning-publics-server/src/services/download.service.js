const { CONSTANTS } = require("../config");
const DownloadModel = require("../models/donwload.model");

exports.create = async (details) => {
  try {
    const exist = await DownloadModel.findOne({article:details.article,admin:details.admin}).exec();
    if (exist) {
      exist.article.push(details.article);
      exist.save();
      return exist;
    }
    return await DownloadModel.create({...details});
  } catch (error) {
    return {error};
  }
};
exports.findByArticleId = async (account,articleId) => {
  try {
    return await DownloadModel.findOne({admin:account,article:articleId}).exec();
  } catch (error) {
    return {error};
  }
};
exports.delete = async (admin, _id) => {
  try {
    return await DownloadModel.findOneAndDelete({admin,_id}).exec();
  } catch (error) {
    return {error};
  }
};
exports.downloads = async (userId, role) => {
  try {
    if (role !== CONSTANTS.ACCOUNT_TYPE[2])
      return await DownloadModel.find({admin:userId}).populate( [
        {
          path: "article",
          model: "Article",
          select: ["title", "author", "status"],
          limit: 1,
        }
      ]).exec();
    if (role === CONSTANTS.ACCOUNT_TYPE[2])
      return await DownloadModel.find({}).populate(
        [
          {
            path: "article",
            model: "Article",
            select: ["title", "author"],
            limit: 1,
          }
        ]).exec();
  } catch (error) {
    return {error};
  }
};