import React from 'react';
import { motion } from 'framer-motion';
import { skeletonVariants } from 'design-system/components';

/**
 * Modern Skeleton Loading Component
 * Provides smooth, accessible loading states
 */

const Skeleton = ({ 
  variant = 'text', 
  width, 
  height, 
  className = '', 
  animate = true,
  ...props 
}) => {
  const baseStyles = skeletonVariants.base;
  const shapeStyles = skeletonVariants.shapes[variant] || skeletonVariants.shapes.text;
  
  const customStyles = {
    ...baseStyles,
    ...shapeStyles,
    ...(width && { width }),
    ...(height && { height })
  };

  const pulseAnimation = {
    opacity: [0.4, 0.8, 0.4],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  if (animate) {
    return (
      <motion.div
        className={`skeleton ${className}`}
        style={customStyles}
        animate={pulseAnimation}
        {...props}
      />
    );
  }

  return (
    <div
      className={`skeleton animate-pulse ${className}`}
      style={customStyles}
      {...props}
    />
  );
};

// Specialized skeleton components
export const SkeletonText = ({ lines = 1, className = '', ...props }) => (
  <div className={`space-y-2 ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <Skeleton
        key={index}
        variant="text"
        width={index === lines - 1 ? '75%' : '100%'}
        {...props}
      />
    ))}
  </div>
);

export const SkeletonTitle = ({ className = '', ...props }) => (
  <Skeleton
    variant="title"
    className={`mb-2 ${className}`}
    {...props}
  />
);

export const SkeletonAvatar = ({ size = 'md', className = '', ...props }) => {
  const sizes = {
    sm: { width: '2rem', height: '2rem' },
    md: { width: '2.5rem', height: '2.5rem' },
    lg: { width: '3rem', height: '3rem' },
    xl: { width: '4rem', height: '4rem' }
  };

  return (
    <Skeleton
      variant="avatar"
      width={sizes[size].width}
      height={sizes[size].height}
      className={className}
      {...props}
    />
  );
};

export const SkeletonButton = ({ size = 'md', className = '', ...props }) => {
  const sizes = {
    sm: { width: '4rem', height: '2rem' },
    md: { width: '6rem', height: '2.5rem' },
    lg: { width: '8rem', height: '3rem' }
  };

  return (
    <Skeleton
      variant="button"
      width={sizes[size].width}
      height={sizes[size].height}
      className={className}
      {...props}
    />
  );
};

export const SkeletonCard = ({ className = '', children, ...props }) => (
  <div className={`bg-white rounded-lg border border-neutral-200 p-6 ${className}`}>
    {children || (
      <>
        <div className="flex items-center space-x-4 mb-4">
          <SkeletonAvatar />
          <div className="flex-1">
            <SkeletonTitle />
            <SkeletonText lines={1} />
          </div>
        </div>
        <SkeletonText lines={3} />
        <div className="flex justify-between items-center mt-4">
          <SkeletonButton size="sm" />
          <SkeletonText lines={1} width="4rem" />
        </div>
      </>
    )}
  </div>
);

// Article-specific skeleton components
export const ArticleCardSkeleton = ({ className = '' }) => (
  <motion.div
    className={`bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden ${className}`}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="p-6">
      {/* Article title */}
      <SkeletonTitle />
      
      {/* Author and date */}
      <div className="flex items-center space-x-3 mb-4">
        <SkeletonAvatar size="sm" />
        <div className="flex-1">
          <SkeletonText lines={1} width="8rem" />
          <SkeletonText lines={1} width="6rem" />
        </div>
      </div>
      
      {/* Article description */}
      <SkeletonText lines={3} className="mb-4" />
      
      {/* Tags */}
      <div className="flex space-x-2 mb-4">
        <Skeleton width="4rem" height="1.5rem" style={{ borderRadius: '9999px' }} />
        <Skeleton width="5rem" height="1.5rem" style={{ borderRadius: '9999px' }} />
        <Skeleton width="3rem" height="1.5rem" style={{ borderRadius: '9999px' }} />
      </div>
      
      {/* Footer */}
      <div className="flex justify-between items-center">
        <SkeletonButton size="sm" />
        <SkeletonText lines={1} width="3rem" />
      </div>
    </div>
  </motion.div>
);

export const ArticleListSkeleton = ({ count = 6, className = '' }) => (
  <div className={`space-y-6 ${className}`}>
    {Array.from({ length: count }).map((_, index) => (
      <ArticleCardSkeleton key={index} />
    ))}
  </div>
);

export const ArticleViewSkeleton = ({ className = '' }) => (
  <div className={`max-w-4xl mx-auto ${className}`}>
    {/* Header */}
    <div className="mb-8">
      <SkeletonTitle />
      <div className="flex items-center space-x-4 mt-4">
        <SkeletonAvatar />
        <div className="flex-1">
          <SkeletonText lines={1} width="10rem" />
          <SkeletonText lines={1} width="8rem" />
        </div>
      </div>
    </div>
    
    {/* Content */}
    <div className="space-y-4">
      <SkeletonText lines={8} />
      <Skeleton variant="card" height="12rem" className="my-6" />
      <SkeletonText lines={6} />
    </div>
  </div>
);

// Modern Journal/Article List Skeleton
export const JournalListSkeleton = ({ count = 6, className = '' }) => (
  <div className={`space-y-4 ${className}`}>
    {Array.from({ length: count }).map((_, index) => (
      <motion.div
        key={index}
        className="border border-gray-200 rounded-lg p-4 bg-white"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
      >
        <div className="flex items-center gap-4">
          {/* Book/Article Image Skeleton */}
          <div className="flex-shrink-0">
            <Skeleton
              width="7rem"
              height="5rem"
              style={{ borderRadius: '0.375rem' }}
              className="bg-gray-200"
            />
          </div>

          {/* Content Skeleton */}
          <div className="flex-1 space-y-2">
            <SkeletonTitle width="85%" />
            <Skeleton variant="text" width="60%" className="text-sm" />
            <SkeletonText lines={2} className="text-sm" />
            <div className="flex items-center justify-between mt-3">
              <Skeleton width="5rem" height="1.5rem" style={{ borderRadius: '0.25rem' }} />
              <Skeleton width="3rem" height="1rem" />
            </div>
          </div>
        </div>
      </motion.div>
    ))}
  </div>
);

// Admin Dashboard Loading Skeleton
export const AdminDashboardSkeleton = ({ className = '' }) => (
  <div className={`space-y-6 p-6 ${className}`}>
    {/* Stats Cards Skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {Array.from({ length: 4 }).map((_, index) => (
        <motion.div
          key={index}
          className="bg-white p-6 rounded-lg border border-gray-200"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.1 }}
        >
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton width="4rem" height="2rem" />
              <Skeleton width="6rem" height="1rem" />
            </div>
            <Skeleton width="2.5rem" height="2.5rem" style={{ borderRadius: '50%' }} />
          </div>
        </motion.div>
      ))}
    </div>

    {/* Chart/Table Skeleton */}
    <motion.div
      className="bg-white p-6 rounded-lg border border-gray-200"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.4 }}
    >
      <SkeletonTitle width="40%" className="mb-4" />
      <div className="space-y-3">
        {Array.from({ length: 5 }).map((_, index) => (
          <div key={index} className="flex items-center justify-between">
            <Skeleton width="30%" height="1rem" />
            <Skeleton width="20%" height="1rem" />
            <Skeleton width="15%" height="1rem" />
            <Skeleton width="10%" height="1rem" />
          </div>
        ))}
      </div>
    </motion.div>
  </div>
);

// Form Loading Skeleton
export const FormSkeleton = ({ fields = 5, className = '' }) => (
  <div className={`space-y-4 ${className}`}>
    {Array.from({ length: fields }).map((_, index) => (
      <motion.div
        key={index}
        className="space-y-2"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: index * 0.1 }}
      >
        <Skeleton width="25%" height="1rem" />
        <Skeleton width="100%" height="2.5rem" style={{ borderRadius: '0.375rem' }} />
      </motion.div>
    ))}

    <motion.div
      className="flex justify-end space-x-3 mt-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: fields * 0.1 + 0.2 }}
    >
      <Skeleton width="5rem" height="2.5rem" style={{ borderRadius: '0.375rem' }} />
      <Skeleton width="6rem" height="2.5rem" style={{ borderRadius: '0.375rem' }} />
    </motion.div>
  </div>
);

export default Skeleton;
