import { Img, Section } from 'globalStyles'
import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { DASHBOARD_ROUTE } from 'routes'
import { AdminAuthIcon } from 'utils/assets'
import { MainBtn } from '../Hero Section/style'
import { AdminLoginCon } from './style'

const UserLoginComp = () => {
  return (
    <Section>
      <AdminLoginCon>
        <Img src={AdminAuthIcon} alt="user auth icon" />
        <Link to={DASHBOARD_ROUTE}>
          <MainBtn> Login to your Dashboard</MainBtn>
        </Link>
      </AdminLoginCon>
    </Section>
  )
}

export default UserLoginComp