import { Img } from "globalStyles"
import React, { useState } from "react"
import { Document, Page, pdfjs } from "react-pdf"
import { pdfIcon } from "utils/assets"
import { PdfCardCon } from "./style"


import "react-pdf/dist/esm/Page/AnnotationLayer.css"
import "react-pdf/dist/esm/Page/TextLayer.css"

import "./pdfStyles.css"
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`

function PdfCard({ pdfFile, btnText = "" }) {
	const [numPages, setNumPages] = useState(null)
	const [pageNumber, setPageNumber] = useState(1)
	const [pdfScreen, setPdfScreen] = useState(false)

	function onDocumentLoadSuccess({ numPages }) {
		setNumPages(numPages)
		setPageNumber(1)
	}

	function changePage(offset) {
		setPageNumber(prevPageNumber => prevPageNumber + offset)
	}

	function previousPage() {
		changePage(-1)
	}

	function nextPage() {
		changePage(1)
	}

	const toggleScreen = () => {
		setPdfScreen(!pdfScreen)
	}

	return (
		<PdfCardCon>
			<Img src={pdfIcon} alt="PDF Icon" />
			<button onClick={toggleScreen} className="btn-screen">
				{btnText ? btnText : "View Guarantor"}
			</button>

			{pdfScreen && (
				// <div className="Example__container">

				<div className="overlay">
					<button className="close-screen" onClick={toggleScreen}>
						CLOSE
					</button>
					<div className="Example__container__document">
						{pdfFile ? (
							<Document file={pdfFile} onLoadSuccess={onDocumentLoadSuccess}>
								<Page size=" A4" pageNumber={pageNumber || 1} />
							</Document>
						) : null}
					</div>

					<div className="page-btn">
						<p>
							Page {pageNumber || (numPages ? 1 : "--")} of {numPages || "--"}
						</p>
						<button className="page-prevBtn" type="button" disabled={pageNumber <= 1} onClick={previousPage}>
							Previous
						</button>
						<button className="page-toggleBtn" type="button" disabled={pageNumber >= numPages} onClick={nextPage}>
							Next
						</button>
					</div>
				</div>
				// </div>
				// </div>
			)}
		</PdfCardCon>
	)
}

export default PdfCard
