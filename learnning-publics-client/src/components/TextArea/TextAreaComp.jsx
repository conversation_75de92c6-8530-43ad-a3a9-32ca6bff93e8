import React from 'react'
import { TextArea } from './style'

function TextAreaComp({
  text = 'Describe your journal...',
  labelText = 'Journal',
  onChange,
  name,
  type,
  id,
  ...rest
}) {
  return (
    <div>
      {/* <label htmlFor="">{labelText}</label> */}
      <TextArea
        placeholder={text}
        type={type}
        name={name}
        id={id}
        onChange={onChange}
        {...rest}
      />
    </div>
  )
}

export default TextAreaComp
