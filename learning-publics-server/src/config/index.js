exports.CONFIG = {
  APP_NAME: "Learning Publics Journals",
};
// `https://www.learningpublics.com`
// exports.CORS_WHITELISTS = ["http://localhost:3000","http://127.0.0.1:3000", `localhost:${process.env.PORT || 8001}`, `localhost:${process.env.FRONTEND_ORIGIN_URL}`,`${process.env.FRONTEND_ORIGIN_URL}`, "http://localhost:3001", "http://localhost:5173", "https://learningpublics.com", "https://learnning-publics-client.vercel.app", "https://jms-frontend-update.vercel.app"];
const sanitizeOrigin = (origin) => origin?.replace(/\/$/, "");

exports.CORS_WHITELISTS = [
  "http://localhost:3000",
  "http://127.0.0.1:3000",
  `localhost:${process.env.PORT || 8001}`,
  `localhost:${process.env.FRONTEND_ORIGIN_URL}`,
  `${process.env.FRONTEND_ORIGIN_URL}`,
  "http://localhost:3001",
  "http://localhost:5173",
  "https://learningpublics.com",
  "https://www.learningpublics.com",
  "https://learnning-publics-client.vercel.app",
  "https://jms-frontend-update.vercel.app"
].map(sanitizeOrigin);
exports.CONSTANTS = {
  ACCOUNT_TYPE: ["user", "admin", "super", "editor", "third", "sub", "e-learning", "formatter"],
  ACCOUNT_STATUS: ["verified", "unverified"],
  ARTICLE_CATEGORY: ["coding", "non-coding"],
  ARTICLE_STATUS: ["pending", "review", "rejected", "approved", "published", "assigned", "reviewed", "formatted", "resubmit", "resubmitted"],
  ARTICLE_FORMAT: [".pdf", ".doc", ".docx", "application/pdf", "application/doc", "application/docx"],
  CATEGORY_IMG_FORMAT: [".jpeg", ".jpg", ".png", ".gif", "application/jpeg", "application/jpg", "application/png", "application/gif"],
  COURSE_TYPE: ["art", "science", "combined"],
  COURSE_CATEGORY: ["video", "book"],
  PLAN_TYPE: ["free", "waec", "jamb", "dual", "combined"],
  PLAN_DURATION: ["WKS", "MTH", "FREE"]
};