const { model, Schema } = require('mongoose') 
const PlanSchema = new Schema({
    major:{
        type: String,
        // enums: CONSTANTS.COURSE_TYPE,
        required: true,
        indexed: true,
    },
    type:{
        type: String,
        // enums: CONSTANTS.PLAN_TYPE,
        required: true
    },
    price:{
        type: Number, 
        required:true,
    },
   
    courses:{
        type: [],
        required: true,
        // ref: "course"
    },
    // duration:{
    //     type: String,
    //     enums: CONSTANTS.PLAN_DURATION,
    //     required: true,
    //     indexed: true,
    // }
    description:{
        type: String, 
        required: true,
        indexed: true,
    },
    view:{
        type:[], 
    }

})
const   PlanModel =  model('Plan', PlanSchema)
module.exports = PlanModel;