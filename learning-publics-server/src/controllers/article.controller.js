const { CONSTANTS } = require('../config');
const logger = require('../logger');
const {
	articleUpload,
	getAdmins,
	getArticles,
	getArticlesById,
	updateArticleStatus,
	getArticlesByCategory,
	deleteArticleById,
	updateArticle,
	getArticlesInReview,
	getArticlesByTitle,
	getDownloadByArticleId,
	verifyArticleResubmit,
	findCategoryById,
	categoryExist,
	assignArticle,
	getAuditorInfo,
	getArticlesPublishedById,
	// New SEO-friendly services
	getArticleBySlug,
	getArticlesSEOOptimized,
	searchArticles,
	getArticlesByCategorySlug,
	trackArticleView,
	trackSocialShare,
	getRelatedArticles,
} = require('../services');

// Import bookmark services
const {
	addBookmark,
	removeBookmark,
	getUserBookmarks,
	getBookmarkStatus,
	getBookmarkCount
} = require('../services/bookmark.service');
const { META, ERROR_FIELD } = require('../utils/actions');
const { APIError } = require('../utils/apiError');
const { cloudinary, accessPath } = require('../utils/cloudinary');
const axios = require('axios');
const {
	articleUploadMailHandler,
	articleAdminNotifyMailHandler,
	articleRejectionMailHandler,
	articleAprovedMailHandler,
	articleResubmissionMailHandler,
	articleReviewMailHandler,
} = require('../utils/mailer');
const { isValidEmail, articleIdGenerator } = require('../utils/validation');
const responsBuilder = require('../utils/responsBuilder');
const { requiredAdminRole } = require('../middlewares/auth.middleware');
const { default: mongoose } = require('mongoose');
const { checkById } = require('../services/account.services');

exports.sendArticle = async (req, res, next) => {
	try {
		// Handle both old and new form structures for backward compatibility
		const {
			// Old form fields (for backward compatibility)
			author,
			email,
			description,
			phone,
			country,
			title,
			category,
			fileData,
			format,

			// New comprehensive form fields
			abstract,
			keywords,
			manuscriptType,
			correspondingAuthor,
			institution,
			orcid,
			coAuthors,
			wordCount,
			references,
			figures,
			tables,
			supplementaryFiles,
			fundingInfo,
			conflictOfInterest,
			coverLetter,
			ethicsStatement,
			dataAvailability,

			// Declarations
			originalWork,
			noConflict,
			ethicsApproval,
			consentToPublish,
			agreeToTerms
		} = req.body;
		// Determine if this is new comprehensive form or old form
		const isNewForm = correspondingAuthor || abstract || keywords;

		// Validate based on form type
		if (isNewForm) {
			// New comprehensive form validation
			if (!title) return next(APIError.badRequest('Title is required'));
			if (!abstract) return next(APIError.badRequest('Abstract is required'));
			if (!keywords) return next(APIError.badRequest('Keywords are required'));
			if (!category) return next(APIError.badRequest('Category is required'));
			if (!manuscriptType) return next(APIError.badRequest('Manuscript type is required'));
			if (!correspondingAuthor) return next(APIError.badRequest('Corresponding author is required'));
			if (!email) return next(APIError.badRequest('Email address is required'));
			if (!isValidEmail(email)) return next(APIError.badRequest('Email is not valid'));
			if (!institution) return next(APIError.badRequest('Institution is required'));
			if (!country) return next(APIError.badRequest('Country is required'));
			if (!wordCount) return next(APIError.badRequest('Word count is required'));
			if (!originalWork) return next(APIError.badRequest('You must confirm this is original work'));
			if (!agreeToTerms) return next(APIError.badRequest('You must agree to the terms'));
		} else {
			// Old form validation (backward compatibility)
			if (!author) return next(APIError.badRequest('Author name(s) are required'));
			if (!email) return next(APIError.badRequest('Email address is required'));
			if (!isValidEmail(email)) return next(APIError.badRequest('Email is not valid'));
			if (!phone) return next(APIError.badRequest('Phone number is required'));
			if (!title) return next(APIError.badRequest('Title is required'));
			if (!country) return next(APIError.badRequest('Country of residence is required'));
			if (!description) return next(APIError.badRequest('Article description is required'));
			if (!category) return next(APIError.badRequest('Article category is required'));
			if (!fileData || fileData === " ") return next(APIError.badRequest('Article content is required'));
			if (!format) return next(APIError.badRequest('Article format is required'));
			if (!CONSTANTS.ARTICLE_FORMAT.includes(format)) return next(APIError.badRequest('Invalid file format'));
		}
		// Create article object based on form type
		const article = isNewForm ? {
			// New comprehensive form structure
			title,
			abstract,
			keywords,
			category,
			manuscriptType,
			author: correspondingAuthor,
			email,
			phone: phone || '',
			institution,
			country,
			orcid: orcid || '',
			coAuthors: <AUTHORS>
			wordCount: parseInt(wordCount) || 0,
			references: parseInt(references) || 0,
			figures: parseInt(figures) || 0,
			tables: parseInt(tables) || 0,
			supplementaryFiles: supplementaryFiles || '',
			fundingInfo: fundingInfo || '',
			conflictOfInterest: conflictOfInterest || '',
			coverLetter: coverLetter || '',
			ethicsStatement: ethicsStatement || '',
			dataAvailability: dataAvailability || '',
			description: abstract, // Map abstract to description for compatibility
			format: 'application/pdf', // Default for new form

			// Declarations
			originalWork: originalWork || false,
			noConflict: noConflict || false,
			ethicsApproval: ethicsApproval || false,
			consentToPublish: consentToPublish || false,
			agreeToTerms: agreeToTerms || false
		} : {
			// Old form structure (backward compatibility)
			author,
			email,
			description,
			phone,
			country,
			title,
			category,
			format,
		};
		// Handle both category ID and category name
		let categoryFound;
		const mongoose = require('mongoose');

		// Check if category is a valid ObjectId
		if (mongoose.Types.ObjectId.isValid(category)) {
			categoryFound = await findCategoryById(category);
		} else {
			// If not ObjectId, try to find by name
			categoryFound = await categoryExist(category);
		}

		if (!categoryFound) return next(APIError.badRequest("Category does not exist"));
		if (categoryFound.error) return next(APIError.badRequest(categoryFound.error));

		// Ensure we use the category ObjectId for the article
		article.category = categoryFound._id;
		article.status = CONSTANTS.ARTICLE_STATUS[0];

		// Handle file upload - both forms use base64 data
		if (fileData) {
			// Base64 file data (both new and old forms)
			article.fileData = fileData;
		} else if (req.file) {
			// Fallback: File uploaded via multer (if needed)
			const reader = require('fs').readFileSync(req.file.path);
			article.fileData = `data:${req.file.mimetype};base64,${reader.toString('base64')}`;
			article.format = req.file.mimetype;

			// Clean up uploaded file
			require('fs').unlinkSync(req.file.path);
		} else {
			return next(APIError.badRequest('Manuscript file is required'));
		}
		// check if article is being resubmitted
		if(req.body.reSubmit){
			const {articleId} = req.body;
			if(!articleId) return next(APIError.badRequest('Article ID is required for resubmission'));
			const verify = await verifyArticleResubmit(articleId.trim());
			if (!verify) return next(APIError.badRequest("Article does not exist"));
			if (verify.error) return next(APIError.badRequest(verify.error));
			article.articleId = articleId;
			article.status = CONSTANTS.ARTICLE_STATUS[9];
		 await cloudinary.uploader.destroy(verify.publicId, {
				upload_preset: accessPath.preset(),
				folder: accessPath.folder(),
			});
			logger.info('Existing article deleted successfully', {
				meta: META.CLOUDINARY_SERVICE,
			});
		}
		//send file to cloudinary
		const uploadFile = await cloudinary.uploader.upload(fileData, {
			// eslint-disable-next-line camelcase
			upload_preset: accessPath.preset(),
			folder: accessPath.folder(),
		});
		logger.info('Article uploaded successfully', {
			meta: META.CLOUDINARY_SERVICE,
		});
		article.articleUrl = uploadFile.secure_url;
		article.publicId = uploadFile.public_id;
	
		//Save data to database
		const upload = await articleUpload(article);
		if (upload.error) return next(APIError.badRequest(upload.error));
		logger.info('Article send successfully', { meta: META.ARTICLE_SERVICE });
		//send email to author
		const mail = await articleUploadMailHandler(email, title, author);
		if (mail.error) {
			return next(APIError.customError(ERROR_FIELD.ARTICLE_UPLOAD_MAIL,400));
		}
		logger.info('Article upload mail sent successfully', {
			meta: META.MAIL_SERVICE,
		});

		//send mail to admins
		const admins = await getAdmins();
		if (admins.error) return next(APIError.customError(admins.error, 400));
		admins.map(async (cur) => {
			//send mail
			if(cur.type.toLowerCase() === CONSTANTS.ACCOUNT_TYPE[2]){
			const mail = await articleAdminNotifyMailHandler(
				cur.email,
				title,
				cur.username
			);
			if (mail.error) {
				logger.info('Admin notification mail failed', {
					meta: META.MAIL_SERVICE,
				});
			}
		}
		});
	
		logger.info('Admin notification mail sent successfully', {
			meta: META.MAIL_SERVICE,
		});
		res
			.status(200)
			.json({ success: true, msg: 'Article uploaded successfully' });
	} catch (error) {
		next(error);
	}
};

exports.adminUploadArticle = async (req, res, next) => {
	try {
		console.log('=== ADMIN UPLOAD ARTICLE ===');
		console.log('User Type:', req.userType);
		console.log('User ID:', req.userId);
		console.log('Request Body:', { articleId: req.body.articleId, format: req.body.format, hasFileData: !!req.body.fileData });

		if(req.userType === CONSTANTS.ACCOUNT_TYPE[0] || req.userType === CONSTANTS.ACCOUNT_TYPE[6]) return next(APIError.unauthorized());
		const { fileData, format, articleId } = req.body;
		if (!articleId) return next(APIError.badRequest('Article Id is required'));
		if (!fileData) return next(APIError.badRequest("Article content is required"));
		if (!format) return next(APIError.badRequest('Article format is required'));
		if (!CONSTANTS.ARTICLE_FORMAT.includes(format)) return next(APIError.badRequest('Invalid file format'));
		
			// check if article have been downloaded by the user (skip for super admins)
			if (req.userType.toLowerCase() !== CONSTANTS.ACCOUNT_TYPE[2]) {
				const isDownloaded = await getDownloadByArticleId(req.userId, articleId.trim());
				if(!isDownloaded || isDownloaded.length === 0) return next(APIError.badRequest("Download article first"));
			}
		//get existing article
		const existingArticle = await getArticlesById(articleId, req.userId, req.userType);
		if (!existingArticle)
			return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (existingArticle.error)
			return next(APIError.customError(existingArticle.error, 400));
			if( existingArticle.status === CONSTANTS.ARTICLE_STATUS[0]) return  next(APIError.badRequest("Article has not been accepted"));
			if( existingArticle.status === CONSTANTS.ARTICLE_STATUS[2]) return next(APIError.badRequest("Article has been rejected"));
			// Check if user is assigned as editor (skip for super admins)
			console.log('Checking editor assignment...');
			console.log('Article editors:', existingArticle.editors.map(e => ({ id: e.id.toString(), name: e.name })));
			console.log('Current user ID:', req.userId);
			console.log('User type:', req.userType);
			console.log('Super admin type:', CONSTANTS.ACCOUNT_TYPE[2]);

			let currentEditor = existingArticle.editors.find(article => article.id.toString() === req.userId);
			console.log('Found current editor:', !!currentEditor);

			// Super admins can upload to any article without being assigned
			if (!currentEditor && req.userType.toLowerCase() === CONSTANTS.ACCOUNT_TYPE[2]) {
				console.log('Creating super admin editor entry...');
				// Create a temporary editor entry for super admin
				currentEditor = {
					id: req.userId,
					name: "Super Admin",
					role: "super",
					assignedAt: new Date()
				};
			}

			// For non-super admins, check if they are assigned to this article
			if (!currentEditor) {
				console.log('❌ User not assigned to article and not super admin');
				return next(APIError.unauthorized("This was not assigned to you"));
			}

			console.log('✅ User authorized to upload');

			const otherEditors = existingArticle.editors.filter(el => el.id.toString() !== req.userId);
		const details = { 
			format,
		};
		if(req.userType.toLowerCase() === CONSTANTS.ACCOUNT_TYPE[3]){

			if(req.body.comment)  {
				if (req.body.comment.length === 0) return next(APIError.badRequest('comment is required'));
 
				currentEditor.comment = req.body.comment;
 
			};
			otherEditors.push(currentEditor);
			details.editors = otherEditors
			details.status = CONSTANTS.ARTICLE_STATUS[6]
		}

		// Delete old file from Cloudinary if it exists
		if (existingArticle.publicId) {
			try {
				await cloudinary.uploader.destroy(existingArticle.publicId, {
					upload_preset: accessPath.preset(),
					folder: accessPath.folder(),
				});
				logger.info('Existing article file deleted successfully', {
					meta: META.CLOUDINARY_SERVICE,
				});
			} catch (deleteError) {
				logger.warn('Failed to delete existing article file', {
					meta: META.CLOUDINARY_SERVICE,
					error: deleteError.message
				});
				// Continue with upload even if deletion fails
			}
		}

		//send file to data cloudinary
		const uploadFile = await cloudinary.uploader.upload(fileData, {
			upload_preset: accessPath.preset(),
			folder: accessPath.folder(),
		});
		logger.info('Article Re-uploaded successfully', {
			meta: META.CLOUDINARY_SERVICE,
		});
		// Update editor-specific fields
		currentEditor.articleUrl = uploadFile.secure_url;
		currentEditor.articleId = uploadFile.public_id;
		currentEditor.format = format;

		// Update main article file URL and publicId
		details.articleUrl = uploadFile.secure_url;
		details.publicId = uploadFile.public_id;
		details.format = format;

		//Save data to database
		if(req.userType.toLowerCase() === CONSTANTS.ACCOUNT_TYPE[4] || req.userType.toLowerCase() === CONSTANTS.ACCOUNT_TYPE[7]){
			otherEditors.push(currentEditor);
			details.editors = otherEditors
			details.status = CONSTANTS.ARTICLE_STATUS[7]
		}
		console.log('Updating article with details:', details);
		const upload = await updateArticle(existingArticle._id, details);
		if (upload.error) {
			console.error('Article update failed:', upload.error);
			return next(APIError.customError(upload.error, 400));
		}
		console.log('Article updated successfully:', upload.articleUrl);
		logger.info('Article Re-uploaded successfully', {
			meta: META.ARTICLE_SERVICE,
		});
		res
			.status(200)
			.json({ success: true, msg: 'Article uploaded successfully' });
	} catch (error) {
		next(error);
	}
};

exports.updateArticleStatus = async (req, res, next) => {
	try { 
		if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error) return next(APIError.unauthorized());
		const { status, articleId, comment } = req.body;
		if (!articleId) return next(APIError.badRequest('Article Id is required'));
		if (!status) return next(APIError.badRequest('Article status is required'));
		const article = await getArticlesById(articleId, req.userId, req.userType);
		if (!article) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (!CONSTANTS.ARTICLE_STATUS.includes(status.toLowerCase()))
			return next(APIError.badRequest('Invalid article status'));
		if(!article.articleId) article.articleId = articleIdGenerator(article._id)
		// check if article has been download by this admin
		if(status !== CONSTANTS.ARTICLE_STATUS[1] && req.userType !== CONSTANTS.ACCOUNT_TYPE[2]){
			const downloaded = await getDownloadByArticleId(req.userId, articleId);
			if(!downloaded) return next(APIError.badRequest("You have to download this article first"));
			if(downloaded.error) return next(APIError.badRequest(downloaded.error, 400));
		}
		const newUrl = {};
		const emailAttachments = {}
		let editorsComments = comment ? comment : "";
		if(status === CONSTANTS.ARTICLE_STATUS[2] && article.status === CONSTANTS.ARTICLE_STATUS[0]) return   next(APIError.badRequest("Article has not been accepted"));
		if (status.toLowerCase() !== article.status) {
			if(status.toLowerCase() === CONSTANTS.ARTICLE_STATUS[8] && article.status === CONSTANTS.ARTICLE_STATUS[9]){
				// resubmit article
				emailAttachments.path = article.articleUrl;
				emailAttachments.filename = article.title;
			} else if(status.toLowerCase() === CONSTANTS.ARTICLE_STATUS[8] && article.status !== CONSTANTS.ARTICLE_STATUS[6]) return   next(APIError.badRequest("Article have not been reviewed"));
			if(status.toLowerCase() === CONSTANTS.ARTICLE_STATUS[4] || status.toLowerCase() === "publish"){
				article.editors.forEach((cur) => {
					if(cur.role === CONSTANTS.ACCOUNT_TYPE[7]){
						newUrl.publicId = cur.publicId;
						newUrl.articleUrl = cur.articleUrl
					}
					
				})
				
			} 
				//update article status
				const info = {
					articleId, 
					status, 
					admin:req.userId, 
					articleId: article.articleId,
					publicId: newUrl.publicId,
					articleUrl: newUrl.articleUrl,
				}
				const update = await updateArticleStatus(articleId, info);
				if (update?.error) return next(APIError.customError(update.error));
				logger.info('Article status updated successfully', {
					meta: META.ARTICLE_SERVICE,
				});
 
			//if article is rejected send rejection mail
			let author = article.author.trim().split(' ')[0];
			author = author.charAt(0).toUpperCase() + author.slice(1);
			if (status.toLowerCase() === CONSTANTS.ARTICLE_STATUS[2]) {
				const emailAuthor = await articleRejectionMailHandler(
					article.email,
					article.title,
					author
				);
				if (emailAuthor.error) {
					logger.info('Article rejection mail failed', {
						meta: META.MAIL_SERVICE,
					});
				} else {
					logger.info('Article rejection mail sent successfully', {
						meta: META.MAIL_SERVICE,
					});
				}
			} else if (status.toLowerCase() === CONSTANTS.ARTICLE_STATUS[3] || status.toLowerCase() === CONSTANTS.ARTICLE_STATUS[4]) {
				//when approved send notification to the author
			
				const emailAuthor = await articleAprovedMailHandler(
					article.email,
					article.title,
					author,
					articleId,
					status
				);
				if (emailAuthor.error) {
					logger.info(`'Article ${status}al mail failed'`, {
						meta: META.MAIL_SERVICE,
					});
				} else {
					logger.info('Article status mail sent successfully', {
						meta: META.MAIL_SERVICE,
					});
				}
			}else if(status.toLowerCase() === CONSTANTS.ARTICLE_STATUS[8]){
					// send resubmission email
				const emailAuthor = await articleResubmissionMailHandler(
					article.email,
					article.title,
					author,
					article.articleId, 
					editorsComments
				);
				if (emailAuthor.error) {
					logger.info(`'Article ${status}al mail failed'`, {
						meta: META.MAIL_SERVICE,
					});
				} else {
					logger.info('Article resubmission mail sent successfully', {
						meta: META.MAIL_SERVICE,
					});
				}
		
			}
		}

		res
			.status(200)
			.json({ success: true, msg: 'Article status updated successfully' });
	} catch (error) {
		next(error);
	}
};
exports.verifyArticleView = async (req, res, next) => {
	try {
		const { token } = req.query;
		if (!token) return next(APIError.badRequest('token is required'));
		const article = await getArticlesPublishedById(token);
		if (!article) return next(APIError.customError('Invalid token', 403));
		if (article.error) return next(APIError.badRequest(article.error));
		logger.info('Article view verified successfully', {
			meta: META.ARTICLE_SERVICE,
		});
		res.status(200).json({ success: true, msg: 'Article authenticated' });
	} catch (error) {
		next(error);
	}
};
exports.articlesByCategory = async (req, res, next) => {
	try {
		const { category, page = 1, limit = 10 } = req.query;

		if (!category) {
			return next(APIError.badRequest('Article category is required'));
		}

		// Check if category is a slug (contains hyphens) or an ID
		let result;
		if (category.includes('-') || isNaN(category)) {
			// It's a slug
			result = await getArticlesByCategorySlug(category.toLowerCase());
		} else {
			// It's an ID
			const articles = await getArticlesByCategory(category.toLowerCase());
			result = { articles: articles || [], category: null };
		}

		if (result.error) {
			return next(APIError.customError(result.error, 400));
		}

		if (!result.articles || result.articles.length === 0) {
			return res.status(200).json(responsBuilder.commonReponse('No articles found', {
				articles: [],
				category: result.category,
				pagination: {
					page: parseInt(page),
					limit: parseInt(limit),
					total: 0,
					pages: 0
				}
			}, 'article'));
		}

		// Apply pagination
		const startIndex = (parseInt(page) - 1) * parseInt(limit);
		const endIndex = startIndex + parseInt(limit);
		const paginatedArticles = result.articles.slice(startIndex, endIndex);

		const data = paginatedArticles.map((item) => {
			return responsBuilder.buildArticle(item.toObject());
		});

		const response = responsBuilder.commonReponse('Found', {
			articles: data,
			category: result.category,
			pagination: {
				page: parseInt(page),
				limit: parseInt(limit),
				total: result.articles.length,
				pages: Math.ceil(result.articles.length / parseInt(limit))
			}
		}, 'article');

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
exports.articlesAll = async (req, res, next) => {
	try {
		const articles = await getArticles(req.userType, req.userId);
		if (!articles || articles.length === 0)
			return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		const approved = articles.filter(
			(item) =>
				item.status.toLowerCase() ===
					CONSTANTS.ARTICLE_STATUS[3].toLowerCase() ||
				item.status.toLowerCase() === CONSTANTS.ARTICLE_STATUS[4].toLowerCase()
		);
		const data = approved.map((item) => {
			return responsBuilder.buildArticle(item.toObject());
		});
		if (data.length === 0)
			return res.status(404).json({ msg: 'No approved article found' });
		const response = responsBuilder.commonReponse('Found', data, 'article');
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

exports.articles = async (req, res, next) => {
	try { 
		const articles = await getArticles(req.userType, req.userId);
		if (!articles || articles.length === 0)
			return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		const data = articles.map((item) => {
			if(req.userType !== CONSTANTS.ACCOUNT_TYPE[2]){
				const editor =item.editors.find(x => x.id.toString() === req.userId);
				item.editors = [editor];
			}
			return responsBuilder.buildArticle(item.toObject());
		});
		const response = responsBuilder.commonReponse('Found', data, 'article');
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
exports.deleteArticle = async (req, res, next) => {
	try {
		if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error ) return next(APIError.unauthorized());
		const { articleId } = req.query;
		if (!articleId) return next(APIError.badRequest('Article Id is required'));
		const article = await deleteArticleById(articleId);
		if (!article) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (article.error) {
			if (article.error.message.toLowerCase().includes('cast to objectid'))
				return next(APIError.customError('Invalid article id', 400));
			return next(APIError.customError(article.error.message, 400));
		}
		// delete from cloudinary
		 await cloudinary.uploader.destroy(article.publicId, {
			upload_preset: accessPath.preset(),
			folder: accessPath.folder(),
		});
		logger.info('Article deleted successfully', {
			meta: META.CLOUDINARY_SERVICE,
		});
		logger.info('Deleted article successfully', { meta: META.ARTICLE_SERVICE });
		res
			.status(200)
			.json({ success: true, msg: 'Article deleted successfully' });
	} catch (error) {
		next(error);
	}
};
exports.articlesById = async (req, res, next) => {
	try {
		if(req.userType === CONSTANTS.ACCOUNT_TYPE[0]) return next(APIError.unauthorized());
		const { articleId } = req.query;
		if (!articleId) return next(APIError.badRequest('Article ID is required'));
		const article = await getArticlesById(articleId, req.userId, req.userType);

		if (!article) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (article.error) return next(APIError.customError(article.error, 400));
		const data = responsBuilder.buildArticle(article.toObject());
		if(req.userType !== CONSTANTS.ACCOUNT_TYPE[2]){
			const editor = data.editors.find(x => x.id.toString() === req.userId);
			data.editors = [editor];
		}
		const response = responsBuilder.commonReponse('Found', data, 'article');
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
exports.publishedArticlesById = async (req, res, next) => {
	try {
		const { articleId } = req.query;
		if (!articleId) return next(APIError.badRequest('Article ID is required'));
		const article = await getArticlesPublishedById(articleId);

		if (!article) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (article.error) return next(APIError.customError(article.error, 400));
		const data = responsBuilder.buildArticle(article.toObject());
		const response = responsBuilder.commonReponse('Found', data, 'article');
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
exports.articlesByTitle = async (req, res, next) => {
	try {
		const { title } = req.query;
		if (!title) return next(APIError.badRequest('Search string is required'));
		const article = await getArticlesByTitle(title);

		if (!article || article.length === 0) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (article.error) return next(APIError.customError(article.error, 400));
		let data = [];
		article.forEach((cur) => { 
		data.push(responsBuilder.buildSearchArticle(cur.toObject()));
		}) 
		const response = responsBuilder.commonReponse('Found', data, 'article');
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
exports.articlesInReview = async (req, res, next) => {
	try {
		if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error)  return next(APIError.unauthorized());
		const article = await getArticlesInReview(CONSTANTS.ARTICLE_STATUS[1]);
		if (!article || article.length === 0)
			return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (article.error) return next(APIError.customError(article.error, 400));
		const data = article.map((cur) => {
			return responsBuilder.buildArticleReview(cur.toObject());
		});
		const response = responsBuilder.commonReponse('Found', data, 'article');
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
exports.articlesPublished = async (req, res, next) => {
	try {
		const article = await getArticlesInReview(CONSTANTS.ARTICLE_STATUS[4]);
		if (!article || article.length === 0)
			return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (article.error) return next(APIError.customError(article.error, 400));
		const data = article.map((cur) => {
			return responsBuilder.buildArticleReview(cur.toObject());
		});
		const response = responsBuilder.commonReponse('Found', data, 'article');
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};
exports.assignEditors = async (req, res, next) => {
	try {
		if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error)  return next(APIError.unauthorized());
			const {articleId, editors } = req.body; 
			if (!articleId) return next(APIError.badRequest('Article ID required'));
			if (!editors || editors.length === 0) return next(APIError.badRequest('Editor ID is required'));
			const articleExist = await getArticlesById(articleId, req.userId, req.userType);
			if (!articleExist) return next(APIError.badRequest("Article does not exist"));
			if (articleExist.error) return next(APIError.badRequest(articleExist.error));
			if (articleExist.status === CONSTANTS.ARTICLE_STATUS[0]) return next(APIError.badRequest("Article must be accepted(review) first"));
			if (articleExist.status !== CONSTANTS.ARTICLE_STATUS[1] && articleExist.status !== CONSTANTS.ARTICLE_STATUS[5] && articleExist.status !== CONSTANTS.ARTICLE_STATUS[3] && articleExist.status !== CONSTANTS.ARTICLE_STATUS[9]) return next(APIError.badRequest("Article has has passed this stage"));
			const auditorInfo = await getAuditorInfo(editors);
			if (!auditorInfo || auditorInfo.length === 0) return next(APIError.badRequest("Editor does not exist"));
			if (auditorInfo.error) return next(APIError.badRequest(auditorInfo.error));
			// check who article is assigned to
			const error = []
			auditorInfo.forEach((cur)=> {
				if(cur.role === CONSTANTS.ACCOUNT_TYPE[7] || cur.role === CONSTANTS.ACCOUNT_TYPE[7] && articleExist.status !== CONSTANTS.ARTICLE_STATUS[9]) error.push({err:"Article cannot be assigned to one of the selected yet"})
				else if (cur.role  === CONSTANTS.ACCOUNT_TYPE[3] && articleExist.status !== CONSTANTS.ARTICLE_STATUS[1] && articleExist.status !== CONSTANTS.ARTICLE_STATUS[5] && articleExist.status !== CONSTANTS.ARTICLE_STATUS[6]) error.push({err: "Article review has passed this stage"})
			})
		if(error.length > 0) return next(APIError.badRequest(error[0].err));
			const mailReceivers = [];
			// find newly added editors to send mail
			auditorInfo.forEach((el) =>{
				if(!articleExist.editors.includes(el._id)){ 
					mailReceivers.push(responsBuilder.buildAuditor(el.toObject()));
				}
			})
		 
		
			const assign = await assignArticle(articleId, mailReceivers);
			if (!assign) return next(APIError.badRequest("Article assignment failed, try again"));
			if (assign.error) return next(APIError.badRequest(assign.error));
			logger.info("Article assigned successfully", {meta:META.ARTICLE_SERVICE});
			
			// send email to editors
			mailReceivers.forEach(async (cur)=> {
				// email handler
				const emailEditor = await articleAdminNotifyMailHandler(
					cur.email,
					articleExist.title,
					cur.username, 
				);
				if (emailEditor.error) {
					logger.info(`'Article assignment mail failed'`, {
						meta: META.MAIL_SERVICE,
					});
				} else {
					logger.info('Article assignment mail sent successfully', {
						meta: META.MAIL_SERVICE,
					});
				}
			})
			res.status(201).json({
					success: true,
					msg: 'Article assigned successfully'
			});
	} catch (error) {
			next(error);
	}
}

exports.sendArticleReview = async (req, res, next) => {
	try{
		if(requiredAdminRole(req.userType, CONSTANTS.ACCOUNT_TYPE[2])?.error)  return next(APIError.unauthorized());
		const { articleId, editorId} = req.body;
		if(!articleId) return next(APIError.badRequest("Article ID is required"));
		if(!editorId) return next(APIError.badRequest("Editor's comment is required"));
		// get article to see if it is still in review
		const editor = await checkById(editorId)
		const articleExist= await getArticlesById(articleId, editorId, editor.type);
		if(!articleExist) return next(APIError.badRequest("Article does not exist or not assigned to this admin"));
		if(articleExist.error) return next(APIError.badRequest(articleExist.error));
		if(articleExist.status !== CONSTANTS.ARTICLE_STATUS[6]) return next(APIError.badRequest("Article has might have exist this stage"));
		const editorIsAssigned = articleExist.editors.find(x => x.id.toString() === editorId); 
		if(!editorIsAssigned) return next(APIError.badRequest("Editor was not assigned this article"));
		if(!editorIsAssigned.comment) return next(APIError.badRequest("Editor have no comment on article review"));
		let extn;
		if(articleExist.format.includes("/")){
			let ending =  articleExist.format.slice(articleExist.format.indexOf('/')+1)
			extn = `.${ending}`
		}else extn = articleExist.format
		const mailDoc = {
			filename: `${articleExist.title}${extn}`,
			path:editorIsAssigned.articleUrl,
		}
	// send email
	 const sendReview = await articleReviewMailHandler(
		articleExist.email, 
		articleExist.title, 
		articleExist.author, 
		articleExist.articleId,
		editorIsAssigned.comment, 
		mailDoc);
		if (sendReview.error) {
			logger.info(`'Article review mail failed'`, {
				meta: META.MAIL_SERVICE,
			});
		} else {
			logger.info('Article review mail sent successfully', {
				meta: META.MAIL_SERVICE,
			});
		}
		res
		.status(200)
		.json({ success: true, msg: 'Article review sent successfully' });
	}catch(error){
		next(error);
	}
}

// New SEO-friendly controller methods

exports.getArticleBySlug = async (req, res, next) => {
	try {
		const { slug } = req.params;
		if (!slug) return next(APIError.badRequest('Article slug is required'));

		const article = await getArticleBySlug(slug);
		if (!article) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (article.error) return next(APIError.customError(article.error, 400));

		// Generate breadcrumbs
		const breadcrumbs = [
			{ name: 'Home', url: '/' },
			{ name: 'Articles', url: '/articles' }
		];

		if (article.category) {
			breadcrumbs.push({
				name: article.category.name,
				url: `/articles/category/${article.category.slug}`
			});
		}

		breadcrumbs.push({
			name: article.title,
			url: `/articles/${article.slug}`,
			current: true
		});

		const data = responsBuilder.buildArticle(article.toObject());
		const response = responsBuilder.commonReponse('Found', { article: data, breadcrumbs }, 'article');
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

exports.getArticlesSEOOptimized = async (req, res, next) => {
	try {
		const {
			page = 1,
			limit = 20,
			category,
			author,
			sortBy = 'createdAt',
			sortOrder = 'desc',
			status = 'published',
			dateFrom,
			dateTo
		} = req.query;

		const options = {
			page: parseInt(page),
			limit: parseInt(limit),
			...(category && { category }),
			...(author && { author }),
			...(status && { status }),
			...(dateFrom && { dateFrom }),
			...(dateTo && { dateTo }),
			sortBy,
			sortOrder
		};

		const result = await getArticlesSEOOptimized(options);
		if (result.error) return next(APIError.customError(result.error, 400));

		const data = result.articles.map((item) => {
			return responsBuilder.buildArticle(item.toObject());
		});

		const response = responsBuilder.commonReponse('Found', {
			articles: data,
			pagination: result.pagination,
			filters: {
				category,
				author,
				sortBy,
				sortOrder,
				status,
				dateFrom,
				dateTo
			}
		}, 'articles');

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

exports.searchArticlesController = async (req, res, next) => {
	try {
		const {
			q: searchTerm,
			page = 1,
			limit = 20,
			category,
			author,
			sortBy = 'relevance',
			sortOrder = 'desc',
			dateFrom,
			dateTo
		} = req.query;

		if (!searchTerm) return next(APIError.badRequest('Search query is required'));

		const options = {
			page: parseInt(page),
			limit: parseInt(limit),
			...(category && { category }),
			...(author && { author }),
			...(dateFrom && { dateFrom }),
			...(dateTo && { dateTo }),
			sortBy,
			sortOrder
		};

		const result = await searchArticles(searchTerm, options);
		if (result.error) return next(APIError.customError(result.error, 400));

		const data = result.articles.map((item) => {
			return responsBuilder.buildArticle(item.toObject());
		});

		const response = responsBuilder.commonReponse('Found', {
			articles: data,
			pagination: result.pagination,
			searchTerm,
			filters: {
				category,
				author,
				sortBy,
				sortOrder,
				dateFrom,
				dateTo
			}
		}, 'search');

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

exports.getArticlesByCategorySlug = async (req, res, next) => {
	try {
		const { categorySlug } = req.params;
		if (!categorySlug) return next(APIError.badRequest('Category slug is required'));

		const result = await getArticlesByCategorySlug(categorySlug);
		if (result.error) return next(APIError.customError(result.error, 400));

		const data = result.articles.map((item) => {
			return responsBuilder.buildArticle(item.toObject());
		});

		const response = responsBuilder.commonReponse('Found', {
			articles: data,
			category: result.category
		}, 'category');

		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

exports.trackArticleViewController = async (req, res, next) => {
	try {
		const { slug } = req.params;
		if (!slug) return next(APIError.badRequest('Article slug is required'));

		const result = await trackArticleView(slug);
		if (result.error) return next(APIError.customError(result.error, 400));

		res.status(200).json({ success: true, message: 'View tracked successfully' });
	} catch (error) {
		next(error);
	}
};

exports.trackSocialShareController = async (req, res, next) => {
	try {
		const { slug } = req.params;
		const { platform } = req.body;

		if (!slug) return next(APIError.badRequest('Article slug is required'));
		if (!platform) return next(APIError.badRequest('Platform is required'));

		const validPlatforms = ['facebook', 'twitter', 'linkedin', 'email'];
		if (!validPlatforms.includes(platform)) {
			return next(APIError.badRequest('Invalid platform'));
		}

		const result = await trackSocialShare(slug, platform);
		if (result.error) return next(APIError.customError(result.error, 400));

		res.status(200).json({ success: true, message: 'Share tracked successfully' });
	} catch (error) {
		next(error);
	}
};

exports.getRelatedArticlesController = async (req, res, next) => {
	try {
		const { category, exclude, limit = 4 } = req.query;

		if (!category) return next(APIError.badRequest('Category ID is required'));
		if (!exclude) return next(APIError.badRequest('Article ID to exclude is required'));

		const result = await getRelatedArticles(category, exclude, parseInt(limit));
		if (result.error) return next(APIError.customError(result.error, 400));

		const data = result.articles.map((item) => {
			return responsBuilder.buildArticle(item.toObject());
		});

		const response = responsBuilder.commonReponse('Found', data, 'related');
		res.status(200).json(response);
	} catch (error) {
		next(error);
	}
};

// PDF Proxy endpoint to handle CORS issues
exports.getPDFProxy = async (req, res, next) => {
	try {
		const { slug } = req.params;

		if (!slug) return next(APIError.badRequest('Article slug is required'));

		// Check if request is coming from our frontend (basic protection against direct access)
		const referer = req.get('Referer') || req.get('Origin');
		const { CORS_WHITELISTS } = require('../config');

		if (!referer || !CORS_WHITELISTS.some(origin => referer.startsWith(origin))) {
			return next(APIError.customError('Access denied: PDF can only be viewed through the platform', 403));
		}

		// Get article by slug to get the PDF URL
		const article = await getArticleBySlug(slug);

		if (!article) return next(APIError.customError(ERROR_FIELD.NOT_FOUND, 404));
		if (article.error) return next(APIError.customError(article.error, 400));

		// Check if article is published
		if (article.status !== CONSTANTS.ARTICLE_STATUS[4]) {
			return next(APIError.customError('Article not available', 403));
		}

		try {
			// Fetch PDF from Cloudinary
			const response = await axios({
				method: 'GET',
				url: article.articleUrl,
				responseType: 'stream',
				timeout: 30000, // 30 second timeout
				headers: {
					'User-Agent': 'Learning-Publics-Server/1.0'
				}
			});

			// Set appropriate headers for PDF with download protection
			res.setHeader('Content-Type', 'application/pdf');
			res.setHeader('Content-Disposition', 'inline'); // Force inline viewing, no filename to discourage downloads

			// Set CORS origin dynamically based on the requesting domain
			const requestOrigin = req.get('Origin') || req.get('Referer')?.split('/').slice(0, 3).join('/');
			const allowedOrigin = CORS_WHITELISTS.find(origin => requestOrigin?.startsWith(origin));
			res.setHeader('Access-Control-Allow-Origin', allowedOrigin || 'http://localhost:3000');

			res.setHeader('Access-Control-Allow-Methods', 'GET');
			res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
			res.setHeader('X-Content-Type-Options', 'nosniff'); // Prevent MIME type sniffing
			res.setHeader('X-Frame-Options', 'SAMEORIGIN'); // Prevent embedding in other sites
			res.setHeader('Referrer-Policy', 'same-origin'); // Restrict referrer information

			// Use article's updatedAt timestamp for cache busting
			const lastModified = new Date(article.updatedAt).toUTCString();
			res.setHeader('Last-Modified', lastModified);
			res.setHeader('ETag', `"${article._id}-${article.updatedAt}"`);

			// Check if client has the latest version
			const ifModifiedSince = req.get('If-Modified-Since');
			const ifNoneMatch = req.get('If-None-Match');

			if (ifModifiedSince === lastModified || ifNoneMatch === `"${article._id}-${article.updatedAt}"`) {
				return res.status(304).end(); // Not Modified
			}

			// Cache for shorter time but allow revalidation
			res.setHeader('Cache-Control', 'public, max-age=300, must-revalidate'); // Cache for 5 minutes with revalidation

			// Pipe the PDF stream to response
			response.data.pipe(res);

		} catch (fetchError) {
			logger.error('PDF fetch error:', fetchError);
			return next(APIError.customError('Failed to fetch PDF file', 500));
		}

	} catch (error) {
		next(error);
	}
};

// Bookmark Controllers
exports.addBookmarkController = async (req, res, next) => {
	try {
		const { slug } = req.params;
		const userId = req.headers['x-user-id'] || 'anonymous'; // For now, use header or anonymous
		const userEmail = req.headers['x-user-email'] || '<EMAIL>';

		if (!slug) return next(APIError.badRequest('Article slug is required'));

		const result = await addBookmark(userId, userEmail, slug);
		if (result.error) return next(APIError.customError(result.error, 400));

		res.status(201).json({
			success: true,
			message: 'Article bookmarked successfully',
			bookmark: result
		});
	} catch (error) {
		next(error);
	}
};

exports.removeBookmarkController = async (req, res, next) => {
	try {
		const { slug } = req.params;
		const userId = req.headers['x-user-id'] || 'anonymous';

		if (!slug) return next(APIError.badRequest('Article slug is required'));

		const result = await removeBookmark(userId, slug);
		if (result.error) return next(APIError.customError(result.error, 400));

		res.status(200).json({
			success: true,
			message: 'Bookmark removed successfully'
		});
	} catch (error) {
		next(error);
	}
};

exports.getUserBookmarksController = async (req, res, next) => {
	try {
		const userId = req.headers['x-user-id'] || 'anonymous';
		const page = parseInt(req.query.page) || 1;
		const limit = parseInt(req.query.limit) || 10;

		const result = await getUserBookmarks(userId, page, limit);
		if (result.error) return next(APIError.customError(result.error, 400));

		res.status(200).json({
			success: true,
			data: result
		});
	} catch (error) {
		next(error);
	}
};

exports.getBookmarkStatusController = async (req, res, next) => {
	try {
		const { slug } = req.params;
		const userId = req.headers['x-user-id'] || 'anonymous';

		if (!slug) return next(APIError.badRequest('Article slug is required'));

		const result = await getBookmarkStatus(userId, slug);
		if (result.error) return next(APIError.customError(result.error, 400));

		res.status(200).json({
			success: true,
			data: result
		});
	} catch (error) {
		next(error);
	}
};