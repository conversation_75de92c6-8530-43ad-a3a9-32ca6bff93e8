const planRoute = require('express').Router()
const Controller = require("../controllers");

const { adminRequired, userRequired } = require("../middlewares/auth.middleware");
 
planRoute.post('/', adminRequired, Controller.PlanController.createPlan).get("/", Controller.PlanController.viewPlans).delete("/", adminRequired, Controller.PlanController.deletePlanById).put('/:', adminRequired, Controller.PlanController.updatePlan).get("/:", userRequired, Controller.PlanController.viewPlansById);
planRoute.post('/', adminRequired, Controller.PlanController.createPlan).get("/", Controller.PlanController.viewPlans).delete("/", adminRequired, Controller.PlanController.deletePlanById);
planRoute.get("/userplan", user<PERSON>equired, Controller.PlanController.userPlans);
planRoute.post("/transaction", userRequired, Controller.PlanController.initiateTransaction ).get("/transaction", userRequired, Controller.PlanController.verifyTransaction);
planRoute.post("/payment-successful", Controller.PlanController.paymentCompleted);
planRoute.put("/remove-course", adminRequired, Controller.PlanController.removePlanCourse);
planRoute.put("/add-course", userRequired, Controller.PlanController.addPlanCourse);
planRoute.get("/view-course", userRequired, Controller.PlanController.viewCourse)
module.exports = {
    planRoute
}