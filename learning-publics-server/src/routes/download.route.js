const express = require("express");
const downloadRoute = express.Router();
const Controller = require("../controllers");
const { adminRequired, notELearningRole } = require("../middlewares/auth.middleware");

downloadRoute.post("/", adminRequired,notELearningRole, Controller.DownloadController.downloads).get("/", adminRequired,notELearningRole, Controller.DownloadController.getUserDownloads).delete("/", adminRequired,notELearningRole, Controller.DownloadController.deleteUserDownloads);
downloadRoute.get("/:", adminRequired,notELearningRole, Controller.DownloadController.getUserDownloadsById)
module.exports = {
  downloadRoute,
}