import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

const Breadcrumbs = ({ items = [] }) => {
  if (!items || items.length <= 1) return null;

  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRightIcon className="h-4 w-4 text-gray-400 mx-2" />
            )}
            
            {index === items.length - 1 ? (
              // Current page - not clickable
              <span className="text-gray-500 text-sm font-medium">
                {index === 0 && <HomeIcon className="h-4 w-4 inline mr-1" />}
                {item.name}
              </span>
            ) : (
              // Clickable breadcrumb
              <Link
                to={item.url}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-200"
              >
                {index === 0 && <HomeIcon className="h-4 w-4 inline mr-1" />}
                {item.name}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
