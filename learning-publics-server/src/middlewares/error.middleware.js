const logger = require("../logger");
// eslint-disable-next-line no-unused-vars
exports.notFound = (_req, res, _next) => {
  const err = new Error("Route Not Found");
  err.status = 404;
  logger.error(err);
  res.status(err.status).json({ error: err.message });
};

// eslint-disable-next-line no-unused-vars
exports.errorHandler = (err, _req, res, _next) => {
  logger.error(err);
  if (err.error){
    return res
      .status(err.status || 404)
      .json({ error: "No Internet connection" });
  }
  res.status(err.status || 500).json({ error: err.message || "Unknown error" });
};
