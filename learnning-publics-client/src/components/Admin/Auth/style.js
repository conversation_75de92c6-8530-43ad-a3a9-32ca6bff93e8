import Colors from "utils/colors";
import media from "utils/media";
import styled from "styled-components/macro";

// import { AuthProp } from "utils/assets";

export const Authcontainer = styled.div`
  width: 100%;
  height: 100vh;
  background: ${Colors.white};
`;
export const AuthContent = styled.div`
  width: 100%;
  height: 100%;
  background: ${Colors.primary};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;
export const AuthCard = styled.div`
  width: 100%;
  background: ${Colors.white};
  border-radius: 35px;
  margin-top: 1rem;
  padding: 1rem 1rem;
  ${media.mobile`
	padding: 2rem 1rem;
	`}
  ${media.smallMobile`
	box-shadow:unset;
	padding: unset;
	`}
`;
export const AuthText = styled.h3`
  font-family: "Roboto Slab";
  font-style: normal;
  font-weight: 700;
  font-size: 22px;
  text-align: center;
  line-height: 103%;
  color: ${Colors.primary};
  ${"" /* margin-top: -8px; */}
  margin-bottom: 1px;
  ${media.mobile`
	font-size: 22px;

	`}
`;
export const Authbtn = styled.button`
  ${"" /* width: 30%; */}
  height: 40px;
  border: none;
  outline: none;
  border-radius: 10px;
  background: ${Colors.primary};
  color: ${Colors.white};
  padding: 5px 35px;
  margin-top: 20px;
  cursor: pointer;
`;
export const AuthBottomDIv = styled.div`
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-top: 8px;
  .auth--bottom-link {
    color: ${Colors.light};
    font-size: 14px;
  }
  .link-bottom {
    margin-left: 5px;
    font-size: 12px;
    text-decoration: none;
    font-weight: 700;
    color: ${Colors.primary};
  }
`;

export const CreateContainer = styled.div`
  width: 70%;
`;
export const CreateAccountButton = styled.button`
  width: 100%;
  height: 35px;
  border: none;
  outline: none;
  border-radius: 10px;
  background: ${Colors.gradient};
  color: ${Colors.white};
  padding: 5px 20px;
  margin-top: 20px;
  cursor: pointer;
`;
export const SetPasswordButton = styled.button`
  width: 40%;
  height: 35px;
  border: none;
  outline: none;
  border-radius: 10px;
  background: ${Colors.gradient};
  color: ${Colors.white};
  padding: 5px 20px;
  margin-top: 20px;
  cursor: pointer;
`;
export const AuthContentcreate = styled.div`
  width: 100%;
  height: 100%;

  background-repeat: no-repeat;
  background-position: -14% 130%;
  background-size: 400px 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  ${media.mobile`
	background-position: -40.5% 110%;
	background-size: 200px 200px;
	`}
`;
export const AuthSectionUnique = styled.div`
  width: 100%;
  background: ${Colors.gradient};
`;
export const EmailCreateText = styled.p`
  font-family: "Roboto Slab";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  letter-spacing: 0.005em;
  text-transform: capitalize;
  color: ${Colors.black};
`;

export const VerifyResendBtn = styled.button`
  background: transparent;
  outline: none;
  border: none;
  font-weight: 700;
  margin-left: -10px;
  color: ${Colors.primaryLight};
  width: 100px;
  margin-top: 10px;
  cursor: pointer;
`;

export const VerifyExpires = styled.p`
  font-size: 12px;
  color: ${Colors.primaryLight};
  margin-top: 20px;
`;

export const LoginContainer = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
`;
export const LoginDiv = styled.form`
  width: 35%;
  display: flex;
  flex-direction: column;
  align-items: center;
  ${media.smallDesktopMinimum`
	width:50%;
	`}
  ${media.tablet`
	width:70%;
	`}
  ${media.mobile`
	width:85%;
	`}
  ${media.smallMobile`
	width:90%;
	`}
`;
export const LoginFormContainer = styled.div`
  width: 82%;
  display: flex;
  margin: auto;
  flex-direction: column;
  ${media.mobile`
width:100%;
	`}
`;
export const LoginCont = styled.div`
  width: 82%;
  display: flex;
  margin: auto;
  flex-direction: column;
  justify-content: center;

  ${media.mobile`
width:100%;
	`}
`;
export const ForgotDiv = styled.div`
  margin-top: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  input {
    margin-right: 3px;
  }

  .forgot,
  span {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 17px;
  }
`;
export const CreateAcct = styled.div`
  display: flex;
  margin-top: 6px;
  justify-content: center;
  align-items: center;

  span {
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 17px;
    color: ${Colors.approvedColor};
  }
`;
export const ForgotPasswordDiv = styled.div`
  width: 100%;
`;
