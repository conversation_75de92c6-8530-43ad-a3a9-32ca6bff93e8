import {
  ABOUT_ROUTE,
  ELEARN_ROUTE,
  HOME_ROUTE,
  LOGIN_ROUTE,
  SUBMIT_ROUTE
} from "routes";
import { AppLink, Img } from "globalStyles";
import { AppLogo, HamburgerIcon } from "utils/assets";
import {
  HamBurgerBtn,
  HomeLogoContainer,
  HomeNavigationContainer,
  NavLinkCon,
  NavLinkList
} from "./style";
import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

import { CATEGORY_ENDPOINT } from "api/ACTION";
import MobileMenu from "components/MobileMenu";
import jmsApp from "api/jms";
import { useLocation } from "react-router-dom";
import {
  navItemVariants,
  buttonVariants,
  slideInFromTop,
  fadeIn
} from "design-system/animations";

// import tw from "twin.macro";

function NavBar() {
  const [journalList, setJournalList] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [activeNav, setActiveNav] = useState(1);
  const [categories, setCategories] = useState([]);
  const [open, setOpen] = useState(false);

  const toggleNav = () => {
    setOpen(!open);
  };

  const journalHandler = () => {
    setJournalList(!journalList);
  };
  const toggleActiveNav = (index) => {
    setActiveNav(index);
  };

  const location = useLocation();
  const { pathname } = location;
  const path = pathname.slice(0);

  const handleCategories = async () => {
    try {
      const { data } = await jmsApp.get(
        CATEGORY_ENDPOINT.GET_ARTICLE_CATEGORY()
      );
      if (data.success) setCategories((categories) => [...data.category]);
    } catch (error) {
      setCategories([]);
      // if (error.response) toast.error(error.response.data.error);
      // else toast.error(error);
    }
  };

  useEffect(() => {
    handleCategories();
  }, []);

  return (
    <>
      <AnimatePresence>
        {open && <MobileMenu isOpen={open} close={() => toggleNav()} />}
      </AnimatePresence>

      <motion.div
        initial="hidden"
        animate="visible"
        variants={slideInFromTop}
      >
        <HomeNavigationContainer>
          {/* Enhanced hamburger button with animation */}
          <motion.div
            variants={buttonVariants}
            initial="rest"
            whileHover="hover"
            whileTap="tap"
          >
            <HamBurgerBtn onClick={() => toggleNav()}>
              <Img src={HamburgerIcon} alt="Menu" />
            </HamBurgerBtn>
          </motion.div>

          <motion.div
            variants={fadeIn}
            transition={{ delay: 0.2 }}
          >
            <HomeLogoContainer>
              <Img src={AppLogo} alt="LP Logo" />
            </HomeLogoContainer>
          </motion.div>

          <NavLinkCon>
            <NavLinkList>
              <motion.li
                variants={navItemVariants}
                initial="closed"
                animate="open"
                transition={{ delay: 0.1 }}
                className="transition-all duration-300 ease-in-out transform hover:font-bold"
              >
                <motion.div
                  variants={buttonVariants}
                  initial="rest"
                  whileHover="hover"
                  whileTap="tap"
                >
                  <AppLink
                    className={path === HOME_ROUTE ? "scale-110 font-semibold text-blue-600" : "scale-95 hover:text-blue-600"}
                    onClick={() => {
                      toggleActiveNav(1);
                    }}
                    to={HOME_ROUTE}
                  >
                    <span className="transition-all duration-300 ease-in-out transform hover:font-bold">
                      Home
                    </span>
                  </AppLink>
                </motion.div>
              </motion.li>

            <motion.li
              variants={navItemVariants}
              initial="closed"
              animate="open"
              transition={{ delay: 0.2 }}
              className="relative journal_li"
            >
              <motion.div
                variants={buttonVariants}
                initial="rest"
                whileHover="hover"
                whileTap="tap"
                className="transition-all duration-300 ease-in-out transform hover:font-bold"
              >
                <AppLink
                  className={path.startsWith('/journals') ? "scale-110 font-semibold text-blue-600" : "scale-95 hover:text-blue-600"}
                  onClick={() => {
                    toggleActiveNav(2);
                    journalHandler();
                  }}
                >
                  <span className="transition-all duration-300 ease-in-out transform hover:font-bold">
                    Journals
                  </span>
                </AppLink>
              </motion.div>

              <AnimatePresence>
                {journalList && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scale: 0.95 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -10, scale: 0.95 }}
                    transition={{ duration: 0.2 }}
                    className="absolute mt-5 rounded-lg shadow-lg bg-white text-gray-700 text-sm font-medium capitalize w-max mx-auto border border-gray-200 z-50 max-w-xs"
                  >
                    {/* Header */}
                    <div className="px-4 py-2 bg-gray-50 border-b border-gray-200 rounded-t-lg">
                      <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">
                        Journal Categories
                      </span>
                    </div>

                    {/* Scrollable Content */}
                    <div className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                      <ul className="p-2">
                        {categories?.map((category, index) => (
                          <motion.li
                            key={category.id}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className="p-2 rounded-lg transition-all duration-300 ease-in-out hover:bg-blue-50 hover:text-blue-600"
                          >
                            <AppLink
                              to={`/journals/category/${category.slug || category.name.toLowerCase().replace(/\s+/g, '-')}`}
                              className="block w-full text-left"
                            >
                              {category.name}
                            </AppLink>
                          </motion.li>
                        ))}

                        {/* Show message if no categories */}
                        {(!categories || categories.length === 0) && (
                          <li className="p-4 text-center text-gray-500 text-xs">
                            No categories available
                          </li>
                        )}
                      </ul>
                    </div>

                    {/* Footer with scroll indicator if needed */}
                    {categories && categories.length > 6 && (
                      <div className="px-4 py-2 bg-gray-50 border-t border-gray-200 rounded-b-lg text-center">
                        <span className="text-xs text-gray-500">
                          Scroll for more categories
                        </span>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.li>
            {/* <li>
              <AppLink
                
              className={path === SUPPORT_ROUTE ? "  scale-110 " : " scale-95"}
                onClick={() => {
                  toggleActiveNav(3);
                }}
                to={SUPPORT_ROUTE}
              >
                Support
              </AppLink>
            </li> */}
            {/* <li>
              <AppLink
                
              className={path === LOGIN_ROUTE ? "  scale-110 " : " scale-95"}
                onClick={() => {
                  toggleActiveNav(4);
                }}
                to={LOGIN_ROUTE}
              >
                Login
              </AppLink>
            </li> */}
            <li className=" transition-all duration-300 ease-in-out transform  hover:font-bold  hover:scale-110">
              <AppLink
                className={`${
                  path === ELEARN_ROUTE ? "  scale-120 " : " scale-90"
                } whitespace-nowrap transform transition-all duration-300 ease-in-out !hover:text-black/50`}
                onClick={() => {
                  toggleActiveNav(5);
                }}
                // to={DASHBOARD_ROUTE}
                to={ELEARN_ROUTE}
              >
                <span>E-learning</span>
              </AppLink>
            </li>
            <li className=" transition-all duration-300 ease-in-out transform hover:scale-110  hover:font-bold ">
              <AppLink
                className={`${
                  path === ABOUT_ROUTE ? "  scale-120 " : " scale-90"
                } whitespace-nowrap  transform transition-all duration-300 ease-in-out hover:scale-110  hover:font-bold !hover:text-black/50`}
                onClick={() => {
                  toggleActiveNav(6);
                }}
                to={ABOUT_ROUTE}
              >
                <span>About Us</span>
              </AppLink>
            </li>

            {/* <li>
              <AppLink
                
              className={path === BLOG_ROUTE ? '  scale-110 ' : ' scale-95'}
                onClick={() => {
                  toggleActiveNav(7)
                }}
                to={BLOG_ROUTE}
              >
                Blog
              </AppLink>
            </li> */}
          </NavLinkList>
        </NavLinkCon>
        <NavLinkCon>
          {/* Show Login/Signup for E-learning routes, Support for everything else */}
          {path.startsWith('/courses') || path.startsWith('/elearn') || path.startsWith('/dashboard') || path.startsWith('/payment') || path.startsWith('/checkout') || path === '/login' || path === '/signup' || path === '/create-account' ? (
            <>
              <li className="m-2.5 text-[#46555c] text-lg font-medium transition-all duration-300 ease-in-out border-b border-solid border-transparent hover:border-[#46555c]">
                <AppLink
                  className={`${
                    path === LOGIN_ROUTE ? "scale-120" : "scale-90"
                  } whitespace-nowrap transform transition-all duration-300 ease-in-out !hover:text-black/50`}
                  onClick={() => {
                    toggleActiveNav(4);
                  }}
                  to={LOGIN_ROUTE}
                >
                  Login
                </AppLink>
              </li>
              <li className="m-2.5 text-[#46555c] text-lg font-medium transition-all duration-300 ease-in-out border-b border-solid border-transparent hover:border-[#46555c]">
                <AppLink
                  className={`${
                    path === '/create-account' ? "scale-120" : "scale-90"
                  } whitespace-nowrap transform transition-all duration-300 ease-in-out !hover:text-black/50`}
                  onClick={() => {
                    toggleActiveNav(5);
                  }}
                  to="/create-account"
                >
                  Sign Up
                </AppLink>
              </li>
            </>
          ) : (
            <li className="m-2.5 text-[#46555c] text-lg font-medium transition-all duration-300 ease-in-out border-b border-solid border-transparent hover:border-[#46555c]">
              <AppLink
                className={`${
                  path === '/support' ? "scale-120" : "scale-90"
                } whitespace-nowrap transform transition-all duration-300 ease-in-out !hover:text-black/50`}
                onClick={() => {
                  toggleActiveNav(4);
                }}
                to="/support"
              >
                Support
              </AppLink>
            </li>
          )}

          <button
            disabled={path === SUBMIT_ROUTE}
            className={` disabled:cursor-progress disabled:hover:bg-transparent disabled:hover:text-[#46555c] border border-solid border-[#46555c] p-2.5 rounded-[5px] h-12 transition-colors duration-300 ease-in-out hover:bg-[#46555c] hover:text-white hover:shadow-lg active:shadow-none`}
          >
            <AppLink
              className={`${
                path === HOME_ROUTE ? "  scale-120 " : " scale-90"
              } whitespace-nowrap transform transition-all duration-300 ease-in-out !hover:text-black/50`}
              onClick={() => {
                toggleActiveNav(1);
              }}
              to={SUBMIT_ROUTE}
            >
              Submit Paper
            </AppLink>
          </button>
        </NavLinkCon>
      </HomeNavigationContainer>
      </motion.div>
    </>
  );
}

export default NavBar;
