import { Img } from "globalStyles"
import React from "react"
import { App<PERSON><PERSON>, ArtPen } from "utils/assets"
import MenuDas from "../Menu"
import BigMenu from "../Menu/BigMenu"
import { AuthOverlay, BigMenuDiv, LogoutDiv, SideImageDiv, UserDpName } from "./style"
import { DASHBOARD_LOGOUT_ROUTE } from "routes"
const AuthSideNav = ({ isOpen, onClick, user }) => {
	return (
		<AuthOverlay open={isOpen} onClick={onClick}>
			<BigMenuDiv>
				<SideImageDiv>
					<Img src={AppLogo} alt="LPJ user" />
				</SideImageDiv>
				<UserDpName>
					Prince <PERSON>
				</UserDpName>
				<MenuDas />
				<LogoutDiv>
					<BigMenu link={DASHBOARD_LOGOUT_ROUTE} text="Logout" icon={AppLogo} />
				</LogoutDiv>
			</BigMenuDiv>
		</AuthOverlay>
	)
}

export default AuthSideNav
