import { ARTICLE_ENDPOINT, DOWNLOAD_ENDPOINT } from "api/ACTION";
import { Article, Move, OptCon, TableData } from "./style";
import { FiDownload, FiEye } from "react-icons/fi";
import React, { useEffect, useState } from "react";
import { usePagination, useTable } from "react-table";

import { AiOutlineCheck } from "react-icons/ai";
import Checkbox from "./CheckBox";
import { Cloudinary } from "@cloudinary/url-gen";
import DropDownBtn from "./DropDownBtn";
import { IconContext } from "react-icons/lib";
import { LOCAL_STORAGE } from "api/LOCALSTORAGE";
import axios from "axios";
import jmsApp from "api/jms";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";

function Table({ data, color, value, user, columns, view }) {
  const navigate = useNavigate();

  const [type, setType] = useState(null);
  const [tableInfo, setTableInfo] = useState(data);
  const [defaultStat, setdefaultStat] = useState([]);
  const [statSelected, setStatSelected] = useState([]);
  const [checked, setChecked] = useState([]);
  // const [checkStatus, setCheckStatus] = useState("");

  const tableHooks = (hooks) => {
    hooks.visibleColumns.push((columns) => [
      ...columns,
      {
        id: "Edit",
        Header: "",
        Cell: ({ row }) => (
          <button
            className="remove"
            onClick={() => alert("hii" + row.values.name)}
          >
            Delete
          </button>
        )
      }
    ]);
  };

  useEffect(() => {
    validateUser();
    setTableInfo(data);

    const tmp = [],
      tmp1 = [],
      checkValues = [];
    tableInfo.forEach((e) => {
      tmp.push(e.status);
      tmp1.push(e.status);
      checkValues.push(false);
    });

    setChecked(checkValues);
    setdefaultStat([...tmp1]);
    setStatSelected([...tmp]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    page,
    nextPage,
    canNextPage,
    canPreviousPage,
    previousPage,
    pageOptions,
    state,
    prepareRow
  } = useTable(
    { columns, data: tableInfo },
    usePagination,
    user === "staff" && tableHooks
  );

  const cld = new Cloudinary({
    cloud: {
      cloudName: process.env.REACT_APP_CLOUDINARY_NAME,
      apiKey: process.env.REACT_APP_CLOUDINARY_API_KEY,
      apiSecret: process.env.REACT_APP_CLOUDINARY_API_SECRET
    }
  });

  const handleStatus = async (articleId, status, id) => {
    try {
      const { data } = await jmsApp.patch(ARTICLE_ENDPOINT.ARTICLE_STATUS(), {
        articleId: articleId,
        status: status
      });
      if (data.success) {
        defaultStat.splice(id, 1, status);
        setdefaultStat([...defaultStat]);
      }
    } catch (error) {
      if (error.response) {
        //toast.error(error.response.data.error)
      }
    }
  };

  const downloadFile = async (articleURL, title, articleId) => {
    const myImage = cld.image(articleURL);
    await axios({
      url: myImage.publicID, //your url
      method: "GET",
      responseType: "blob" // important
    }).then((res) => {
      jmsApp
        .post(DOWNLOAD_ENDPOINT.DOWNLOADED_ARTICLE(), { articleId: articleId })
        .then(() => {
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(res.data);
          link.setAttribute("download", `${title}.pdf`);
          document.body.appendChild(link);
          link.click();
        })
        .catch((err) => {
          if (err.response.status === 401) {
            navigate("/login");
          }
          if (err.response) {
            // toast.error(err.response.data.error);
          }
        });
    });
  };

  const validateUser = () => {
    if (LOCAL_STORAGE.user()) {
      setType(LOCAL_STORAGE.user().type);
    }
  };

  const handleCheckVal = (id, cell) => {
    const find = checked.filter((e) => e === true);
    if (find) {
      checked.fill(false);
    }
    const val = checked[id];
    checked.splice(id, 1, !val);
    setChecked([...checked]);
    localStorage.setItem(
      "articleId",
      JSON.stringify(cell.row.original.articleId)
    );
  };

  const changeDropDownVal = (e, id, lbl) => {
    let articleId;

    const updatedData = tableInfo.map((item, i) => {
      if (i === id) {
        statSelected.splice(id, 1, e);
        articleId = item.articleId;
        return { ...item, status: e };
      }

      return item;
    });

    setStatSelected([...statSelected]);

    setTableInfo(updatedData);
  };

  const changeNav = (e, id) => {
    let articleId = id;
    navigate(`/admin/articles/${articleId}`);
  };

  const options = ["pending", "review", "approved", "published", "rejected"];

  const { pageIndex } = state;
  return (
    <>
      <div className=" px-4 py-2 w-full">
        <Article
          color={color}
          {...getTableProps()}
          className="min-w-full divide-y divide-gray-300"
        >
          <thead className="table w-full">
            {headerGroups?.map((headerGroup, idx) => (
              <tr
                key={idx}
                style={{ background: `${color === "grey" ? "" : "white"}` }}
                {...headerGroup.getHeaderGroupProps()}
              >
                {headerGroup.headers?.map((head, headID) => (
                  <th
                    {...head.getHeaderProps()}
                    key={headID}
                    style={{ color: `${color === "grey" ? "" : "black"}` }}
                    scope="col"
                    className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0"
                  >
                    {head.render("Header")}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody {...getTableBodyProps()}>
            {page?.map((item, id) => {
              prepareRow(item);
              return (
                <TableData {...item.getRowProps()} id={id} key={id}>
                  {item.cells?.map((cell, i) => {
                    return (
                      <td
                        key={i}
                        {...cell.getCellProps()}
                        className={`${
                          cell.column.Header === "Title" ? "title" : ""
                        } whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0`}
                      >
                        {cell.column.Header === "Author" ? (
                          <>
                            {view === "article" ? (
                              <Checkbox
                                onClick={() => handleCheckVal(id, cell)}
                                checked={checked[id]}
                              />
                            ) : (
                              <></>
                            )}
                            &nbsp; {cell.value}
                          </>
                        ) : cell.column.Header === "Status" ? (
                          <OptCon>
                            <div className="dropDown">
                              <DropDownBtn
                                props={cell.value}
                                handleClick={(e) =>
                                  changeDropDownVal(e, id, cell.value)
                                }
                                options={options}
                                index={id}
                              />
                            </div>
                            {type === "super" || type === "admin" ? (
                              <>
                                <IconContext.Provider
                                  value={{ color: "blue", size: "20px" }}
                                >
                                  <div
                                    onClick={() =>
                                      downloadFile(
                                        cell.row.original.articleUrl,
                                        cell.row.original.title,
                                        cell.row.original.articleId
                                      )
                                    }
                                  >
                                    <FiDownload />
                                  </div>
                                </IconContext.Provider>
                                <IconContext.Provider
                                  value={{ color: "green", size: "20px" }}
                                >
                                  <div
                                    id={cell.row.original.articleId}
                                    index={id}
                                    onClick={(e) =>
                                      user === "article"
                                        ? changeNav(
                                            e,
                                            cell.row.original.articleId
                                          )
                                        : null
                                    }
                                  >
                                    <FiEye />
                                  </div>
                                </IconContext.Provider>
                                {statSelected &&
                                defaultStat[id] !== statSelected[id] ? (
                                  <IconContext.Provider
                                    value={{ color: "green", size: "20px" }}
                                  >
                                    <div
                                      id={cell.row.original.articleId}
                                      index={id}
                                      onClick={(e) =>
                                        handleStatus(
                                          cell.row.original.articleId,
                                          cell.value,
                                          id
                                        )
                                      }
                                    >
                                      <AiOutlineCheck />
                                    </div>
                                  </IconContext.Provider>
                                ) : (
                                  <></>
                                )}
                              </>
                            ) : (
                              <></>
                            )}
                          </OptCon>
                        ) : cell.column.Header === "" ? (
                          <></>
                        ) : (
                          cell.render("Cell")
                        )}
                      </td>
                    );
                  })}
                </TableData>
              );
            })}
          </tbody>
        </Article>
        {data.length > 10 && (
          <Move>
            <span>
              Page{" "}
              <strong>
                {pageIndex + 1} of {pageOptions.length}
              </strong>{" "}
            </span>
            <button onClick={() => previousPage()} disabled={!canPreviousPage}>
              Prev
            </button>
            <button onClick={() => nextPage()} disabled={!canNextPage}>
              Next
            </button>
          </Move>
        )}
      </div>
    </>
  );
}

export default Table;
