import media from "utils/media";
import styled from "styled-components";
import tw from "twin.macro";

export const NavCon = styled.nav`
  width: 100%;
  /* Mobile-first responsive height */
  height: 64px;
  position: fixed;
  top: 0;
  z-index: 700;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  /* Modern shadow with Learning Publics brand touch */
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  border-bottom: 2px solid #dc2626; /* Learning Publics red accent */
  transition: all 0.3s ease-in-out;

  /* Mobile-first responsive padding */
  ${tw`px-4 sm:px-6 lg:px-10`}

  /* Responsive height adjustments */
  ${media.tablet`
    height: 72px;
  `}
  ${media.smallDesktop`
    height: 80px;
  `}

  /* Enhanced mobile touch targets */
  ${media.mobile`
    height: 60px;
    padding: 0 16px;
  `}
`;

export const HomeNavigationContainer = styled(NavCon)`
  ${tw`px-4 sm:px-6 lg:px-14`}
`;

export const NavImgCon = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const HomeLogoContainer = styled(NavImgCon)`
  /* Mobile-first responsive logo sizing */
  ${tw`w-16 h-12 sm:w-20 sm:h-16 lg:w-24 lg:h-20`}

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
`;

export const NavLinkCon = styled.div`
  /* // margin-right: 8.5%; */
  display: flex;
  /* // flex-direction: column; */
  justify-content: center;

  .journal_li {
    position: relative;
  }
  /* //new style */
  ${media.mobile`
  display:none;
  `}
`;
export const NavLinkList = styled.ul`
  display: flex;

  list-style: none;
  margin: 0;
  padding: 0;
  border: none;
  & > *:not(:last-child) {
    margin-right: 2rem;
  }
  & > a {
    display: block;
    padding: 5px 15px 5px 0.5em;
    width: 100%;
    color: #46555c;
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 20px;

    &:hover {
      /* // background: #46555c;
    // color: #ffffff;
    // font-size: 20px; */

      font-weight: bold;
      ${tw` transition-transform duration-500 ease-in-out transform hover:scale-110`}
    }
    & .active__nav {
      /* // background: #46555c; */
      color: #46555c;
      font-weight: bold;
      font-size: 20px;
    }
  }

  /* //new style */
  ${media.mobile`
  display:none;
  `}
  ${media.tablet`
	display: none;
	`}
`;

export const JournalListDiv = styled.div`
  position: absolute;
  /* width: 250px; */
  width: 100%;
  top: 60px;
  padding: 5%;
  background: #ffffff;
  border-radius: 10px;
  /* // box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */

  a {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #46555c;
    margin-top: 10px;
    text-transform: uppercase;
    text-align: center;

    &:hover {
      background-color: rgba(90, 105, 113, 0.5);
      border-radius: 2px;
    }
  }

  /* //new style */
  ${media.mobile`
  display: none;
  `}
  ${media.tablet`
	display: none;
	`}
`;
//deleted

export const HamBurgerBtn = styled.button`
  display: none;
  width: 50px;
  height: 30px;
  outline: none;
  border: none;
  background: transparent;
  padding: 0;
  img {
    width: 100%;
    height: 100%;
  }
  ${media.tablet`
	display: flex;
  position: relative;
	`}
  ${media.mobile`
	display: flex;
	position: relative;
	`}
`;
